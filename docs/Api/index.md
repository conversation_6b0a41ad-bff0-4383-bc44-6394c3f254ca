# SuperPay

> 请先在Api设置里设置 “Api白名单” 也就是请求Api的白名单IP

### Base URLs: $$HOST$$

> 全局请求Header

| 名称          | 位置     | 类型     | 必选 | 说明          |
|-------------|--------|--------|----|-------------|
| merchant-on | header | string | 否  | 商户号：也可随参数传递 |

## 数据签名

> 商户号和商户密钥到商户后台查看、签名密钥就是商户密钥
>
> 签名后字符，在参数里加入字段'sign'一起传递

```php
    /**
     * 生成签名
     *
     * @param array  $data      签名数据
     * @param string $appSecret 签名密钥
     *
     * @return string
     */
    public function signature(array $data, string $key): string
    {
        foreach ($data as $field => $value) {
            if ($value == '' or $value == null or $field == 'sign' or $field == 'SIGN') {
                unset($data[$field]);
            }
        }
        ksort($data); //键名从低到高进行排序
        $post_url = '';
        foreach ($data as $field => $value) {
            $post_url .= $field . '=' . $value . '&';
        }
        $stringSignTemp = $post_url . 'key=' . $key;
        $sign           = md5($stringSignTemp);
        return strtoupper($sign);
    }

    /**
     * 验证签名
     *
     * @param array  $data       签名数据
     * @param string $signString 签名字符
     * @param string $appSecret  签名密钥
     *
     * @return bool
     */
    public function verifySign(array $data, string $signString, string $appSecret): bool
    {
        $sysSignString = $this->sign($data, $appSecret);
        return $sysSignString === $signString;
    }
```

## 代收订单

### 提交订单

> POST /api/collecting/pay
>
> 请求参数 **Content-Type: application/json**

| 名称                | 位置     | 类型     | 必选 | 说明                    |
|-------------------|--------|--------|----|-----------------------|
| merchant_on       | header | string | 否  | 商户号：可从header传，也可随参数传递 |
| amount            | body   | string | 是  | 订单金额：元                |
| order_sn          | body   | string | 是  | 商户订单号                 |
| notify_url        | body   | string | 否  | 异步通知地址                |
| return_url        | body   | string | 否  | 同步跳转地址                |
| extend            | body   | string | 否  | 商户扩展参数，回调通知时原样返回      |
| channel_code      | body   | string | 否  | 渠道编码                  |
| channel_type_code | body   | string | 否  | 渠道类型编码                |
| uid               | body   | string | 是  | 用户标识                  |
| remark            | body   | string | 否  | 备注                    |
| sign              | body   | string | 是  | 签名字符                  |

> 返回示例

> 200 Response

```json
{
    "success": true,
    "code": 0,
    "error_message": "Successful",
    "items": {
        "amount": "200",
        "address": "TKFAJP5BesJfP19aSCCN1om5FA2ZFEuvmx",
        "created_at": "2025-06-23T04:34:46.000000Z",
        "end_time": "2025-06-23 05:04:46",
        "jump_url": "https://test.pay.dev/pay/A202506230434462592339917",
        "uid": "1",
        "order_sn": "35067122440586476821078689231239"
    }
}
```

### 返回结果

| 状态码 | 状态码含义 | 说明   | 数据模型   |
|-----|-------|------|--------|
| 200 | OK    | none | Inline |

### 返回数据结构

状态码 **200**

| 名称              | 类型      | 必选   | 约束   | 中文名    | 说明      |
|-----------------|---------|------|------|--------|---------|
| » success       | boolean | true | none | 请求状态   | true 成功 |
| » code          | integer | true | none | 状态码    | 0 成功    |
| » error_message | string  | true | none |        | none    |
| » items         | object  | true | none |        | none    |
| »» amount       | integer | true | none | 订单金额：元 | none    |
| »» jump_url     | string  | true | none | 支付地址   | none    |
| »» uid          | string  | true | none | 用户标识   | none    |
| »» order_sn     | string  | true | none | 订单编号   | none    |

## GET 查单

> GET /api/collecting/pay
>
> 请求参数 **Content-Type: application/json**

| 名称          | 位置     | 类型     | 必选 | 说明                    |
|-------------|--------|--------|----|-----------------------|
| order_sn    | query  | string | 是  | 商户订单号                 |
| sign        | query  | string | 是  | 签名字符                  |
| merchant_on | header | string | 否  | 商户号：可从header传，也可随参数传递 |

> 返回示例

> 200 Response

```json
{
    "success": true,
    "code": 0,
    "error_message": "string",
    "items": {
        "uid": "string",
        "order_sn": "string",
        "state": 0,
        "amount": "string",
        "rate": "string",
        "payment_amount": 0,
        "payment_date": null,
        "created_at": "string"
    }
}
```

### 返回结果

| 状态码 | 状态码含义 | 说明   | 数据模型   |
|-----|-------|------|--------|
| 200 | OK    | none | Inline |

### 返回数据结构

状态码 **200**

| 名称                | 类型      | 必选   | 约束   | 中文名                          | 说明   |
|-------------------|---------|------|------|------------------------------|------|
| » success         | boolean | true | none | 请求状态                         | none |
| » code            | integer | true | none | 状态码                          | none |
| » error_message   | string  | true | none | 提示信息                         | none |
| » items           | object  | true | none |                              | none |
| »» uid            | string  | true | none | 用户标识                         | none |
| »» order_sn       | string  | true | none | 订单号                          | none |
| »» state          | integer | true | none | 支付状态:0-待支付,1-已失败,2-已超时,3-已支付 | none |
| »» amount         | string  | true | none | 订单金额：元                       | none |
| »» rate           | string  | true | none | 费率                           | none |
| »» payment_amount | integer | true | none | 支付金额                         | none |
| »» payment_date   | null    | true | none | 支付时间                         | none |
| »» created_at     | string  | true | none | 创建时间                         | none |

## POST 回调

> POST 您配置或传递的回调地址
> 
> 请求参数 **Content-Type: application/json**

| 名称             | 位置   | 类型      | 必选 | 中文名      | 说明   |
|----------------|------|---------|----|----------|------|
| uid            | body | string  | 是  | 用户标识     | none |
| order_sn       | body | string  | 是  | 商户订单编号   | none |
| state          | body | integer | 是  | 订单状态     | none |
| amount         | body | number  | 是  | 订单金额：元   | none |
| payment_amount | body | number  | 是  | 实际支付金额：元 | none |
| rate           | body | number  | 是  | 费率%      | none |
| created_at     | body | string  | 是  | 下单时间     | none |
| payment_date   | body | string  | 是  | 支付时间     | none |
| sign           | body | string  | 是  | 签名数据     | none |

> 返回示例

> 200 Response

```json
{
    "uid": "1",
    "order_sn": "TS202506251441191177708918",
    "state": 3,
    "amount": "10.0000",
    "rate": "16.0000",
    "payment_amount": "10.0000",
    "payment_date": "2025-06-25 06:59:51",
    "created_at": "2025-06-25 06:59:51",
    "sign": "DDF2BF24852ECF4C59DC8ADA06290A21"
}
```

### 返回结果

| 状态码 | 状态码含义 | 说明   | 数据模型   |
|-----|-------|------|--------|
| 200 | OK    | none | Inline |

### 返回数据结构
> 您的业务处理成功后 返回字段串 “success” 否则会间隔30秒，连续通知3次后停止通知


## GET 查询余额

> GET /api/balance
>
> 请求参数 **Content-Type: application/json**

| 名称          | 位置     | 类型     | 必选 | 说明                    |
|-------------|--------|--------|----|-----------------------|
| merchant_on | header | string | 否  | 商户号：可从header传，也可随参数传递 |

> 返回示例

> 200 Response

```json
{
    "success": true,
    "code": 0,
    "error_message": "string",
    "items": {
        "balance": 801.9981
    }
}
```

### 返回结果

| 状态码 | 状态码含义 | 说明   | 数据模型   |
|-----|-------|------|--------|
| 200 | OK    | none | Inline |

### 返回数据结构

状态码 **200**

| 名称              | 类型      | 必选   | 约束   | 中文名  | 说明   |
|-----------------|---------|------|------|------|------|
| » success       | boolean | true | none |      | none |
| » code          | integer | true | none |      | none |
| » error_message | string  | true | none |      | none |
| » items         | object  | true | none |      | none |
| »» balance      | number  | true | none | 余额:元 | none |



## GET 渠道编码

GET /api/collecting/channelCode

### 请求参数

|名称|位置|类型|必选|中文名| 说明                    |
|---|---|---|---|---|-----------------------|
|merchant-on|header|string| 否 || 商户号：可从header传，也可随参数传递 |
|type_id|query|string| 否 || 渠道类型ID                |
|sign|query|string| 否 || 签名字符，不传参数时不签名         |

> 返回示例

> 200 Response

```json
{
  "success": true,
  "code": 0,
  "error_message": "Successful",
  "items": [
    {
      "code": "DF6667",
      "name": "东方支付-6667",
      "min_amount": "200.0000",
      "max_amount": "2000.0000",
      "point_amount": null,
      "remark": null
    },
    {
      "code": "HL001",
      "name": "狐狸TS001",
      "min_amount": null,
      "max_amount": null,
      "point_amount": null,
      "remark": null
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» success|boolean|true|none||none|
|» code|integer|true|none||none|
|» error_message|string|true|none||none|
|» items|[object]|true|none||none|
|»» code|string|true|none|编码|none|
|»» name|string|true|none|名称|none|
|»» min_amount|string¦null|true|none|最小限额|none|
|»» max_amount|string¦null|true|none|最大限额|none|
|»» point_amount|null|true|none|固定金额|none|
|»» remark|null|true|none|说明|none|

## GET 渠道类型编码

GET /api/collecting/channelTypeCode

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|merchant-on|header|string| 否 ||商户号：可从header传，也可随参数传递|

> 返回示例

> 200 Response

```json
{
  "success": true,
  "code": 0,
  "error_message": "Successful",
  "items": [
    {
      "id": 5,
      "parent_id": 0,
      "name": "数字货币",
      "code": "upay",
      "children": [
        {
          "id": 9,
          "parent_id": 5,
          "name": "usdt固定",
          "code": "usdt-gd"
        },
        {
          "id": 6,
          "parent_id": 5,
          "name": "USDT",
          "code": "usdt"
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» success|boolean|true|none||none|
|» code|integer|true|none||none|
|» error_message|string|true|none||none|
|» items|[object]|true|none||none|
|»» id|integer|true|none||none|
|»» parent_id|integer|true|none||none|
|»» name|string|true|none|名称|none|
|»» code|string|true|none|编码|none|
|»» children|[object]|true|none||none|
|»»» id|integer|true|none||none|
|»»» parent_id|integer|true|none||none|
|»»» name|string|true|none||none|
|»»» code|string|true|none||none|
