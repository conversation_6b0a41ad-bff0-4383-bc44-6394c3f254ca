body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:2391px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1746_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u1746 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
  display:flex;
}
#u1746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u1747 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:100px;
  width:982px;
  height:649px;
  display:flex;
}
#u1747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1748 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:103px;
  width:950px;
  height:370px;
}
#u1749_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1749 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u1749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1750 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:0px;
  width:750px;
  height:40px;
  display:flex;
  text-align:left;
}
#u1750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1751_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1751 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1752 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:40px;
  width:750px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1753_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1753 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1754 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:80px;
  width:750px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1755_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1755 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1756_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1756 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:120px;
  width:750px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1757 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1758_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1758 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:160px;
  width:750px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1759 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1760_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1760 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:200px;
  width:750px;
  height:40px;
  display:flex;
  text-align:left;
}
#u1760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1761 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1762 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:240px;
  width:750px;
  height:40px;
  display:flex;
  text-align:left;
}
#u1762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1763_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u1763 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:280px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1764_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:40px;
}
#u1764 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:280px;
  width:750px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u1764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1765 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:320px;
  width:200px;
  height:50px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:750px;
  height:50px;
}
#u1766 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:320px;
  width:750px;
  height:50px;
  display:flex;
  text-align:left;
}
#u1766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1767_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1767 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:428px;
  width:120px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u1767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  background:inherit;
  background-color:rgba(234, 143, 36, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1768 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:428px;
  width:120px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u1768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1769_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  background:inherit;
  background-color:rgba(47, 189, 79, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1769 {
  border-width:0px;
  position:absolute;
  left:826px;
  top:428px;
  width:120px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u1769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1770 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:43px;
  width:1px;
  height:8px;
  display:flex;
}
#u1770 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u1771 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
  display:flex;
}
#u1771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1772_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:185px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:24px;
  color:#FFFFFF;
}
#u1772 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:14px;
  width:185px;
  height:28px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:24px;
  color:#FFFFFF;
}
#u1772 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1772_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u1773 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:18px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u1773 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1773_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1774_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u1774 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:25px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u1774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1775_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1775 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:69px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u1775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1776_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1776 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:69px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1776 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1776_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1777_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u1777 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:100px;
  width:5px;
  height:5px;
  display:flex;
}
#u1777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1778_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u1778 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:95px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u1778 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1778_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1779_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  background:inherit;
  background-color:rgba(225, 193, 50, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1779 {
  border-width:0px;
  position:absolute;
  left:556px;
  top:428px;
  width:120px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u1779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1780_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:28px;
  background:inherit;
  background-color:rgba(234, 143, 36, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1780 {
  border-width:0px;
  position:absolute;
  left:431px;
  top:350px;
  width:110px;
  height:28px;
  display:flex;
  font-size:14px;
}
#u1780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1781 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:135px;
  width:201px;
  height:45px;
  display:flex;
}
#u1781 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1782 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:150px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u1782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1783 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:152px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1783 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1783_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1784_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1784 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:193px;
  width:20px;
  height:20px;
  display:flex;
}
#u1784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1785_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1785 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:195px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1785 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1785_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1786_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1786 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:241px;
  width:20px;
  height:20px;
  display:flex;
}
#u1786 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1787_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1787 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:243px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1787 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1787_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1788 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:291px;
  width:20px;
  height:20px;
  display:flex;
}
#u1788 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1789_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1789 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:293px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1789 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1789_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1790 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:342px;
  width:20px;
  height:20px;
  display:flex;
}
#u1790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1791 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:345px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1791 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1791_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1792 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:495px;
  width:973px;
  height:370px;
}
#u1793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1793 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
#u1793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1794 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:0px;
  width:280px;
  height:40px;
  display:flex;
  text-align:left;
}
#u1794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1795 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:0px;
  width:280px;
  height:40px;
  display:flex;
  text-align:left;
}
#u1795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1796 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:0px;
  width:109px;
  height:40px;
  display:flex;
  text-align:left;
}
#u1796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1797 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1798 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:40px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1799 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:40px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1800 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:40px;
  width:109px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1801 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1802 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:80px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1802 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1803_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1803 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:80px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1804 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:80px;
  width:109px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1805 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1806 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:120px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1807 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:120px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1808 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:120px;
  width:109px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1809 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1810 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:160px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1811 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:160px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1812_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1812 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:160px;
  width:109px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1813_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1813 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1814_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1814 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:200px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1815 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:200px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1816_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1816 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:200px;
  width:109px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1817 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1818 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:240px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1819 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:240px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1820_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1820 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:240px;
  width:109px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1820 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1821_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1821 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:280px;
  width:280px;
  height:40px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1822 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:280px;
  width:280px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#169BD5;
  text-align:left;
}
#u1822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1823_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:40px;
}
#u1823 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:280px;
  width:280px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u1823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:40px;
}
#u1824 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:280px;
  width:109px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u1824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1825_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:50px;
}
#u1825 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:320px;
  width:280px;
  height:50px;
  display:flex;
  font-size:15px;
  text-align:left;
}
#u1825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:50px;
}
#u1826 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:320px;
  width:280px;
  height:50px;
  display:flex;
  text-align:left;
}
#u1826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:50px;
}
#u1827 {
  border-width:0px;
  position:absolute;
  left:560px;
  top:320px;
  width:280px;
  height:50px;
  display:flex;
  text-align:left;
}
#u1827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:50px;
}
#u1828 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:320px;
  width:109px;
  height:50px;
  display:flex;
  text-align:left;
}
#u1828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1829_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u1829 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:961px;
  width:1200px;
  height:768px;
  display:flex;
}
#u1829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u1830 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:1061px;
  width:982px;
  height:649px;
  display:flex;
}
#u1830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1831 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:1004px;
  width:1px;
  height:8px;
  display:flex;
}
#u1831 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1832_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u1832 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:961px;
  width:202px;
  height:768px;
  display:flex;
}
#u1832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1833 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:972px;
  width:40px;
  height:40px;
  display:flex;
  color:#535252;
}
#u1833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u1834 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:978px;
  width:87px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u1834 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1834_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u1835 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:979px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u1835 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1835_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1836_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u1836 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:986px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u1836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1837 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:1030px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u1837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1838 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:1030px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1838 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1838_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u1839 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:1061px;
  width:5px;
  height:5px;
  display:flex;
}
#u1839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u1840 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:1056px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u1840 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1840_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1140px;
  width:201px;
  height:45px;
  display:flex;
}
#u1841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1842 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1111px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u1842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1843_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1843 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1113px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1843 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1843_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1844 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1154px;
  width:20px;
  height:20px;
  display:flex;
}
#u1844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1845 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1156px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1845 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1845_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1846 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1140px;
  width:1241px;
  height:148px;
}
#u1847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1847 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1848 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1849 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1850 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:40px;
}
#u1851 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:0px;
  width:105px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1852 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1853 {
  border-width:0px;
  position:absolute;
  left:605px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1854 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1855_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1855 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:40px;
}
#u1856 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:0px;
  width:168px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:40px;
}
#u1857 {
  border-width:0px;
  position:absolute;
  left:1073px;
  top:0px;
  width:168px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1858 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1859 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1860 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1861 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:38px;
}
#u1862 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:40px;
  width:105px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1863_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1863 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1864 {
  border-width:0px;
  position:absolute;
  left:605px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1865_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1865 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1866 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
  color:#EA9B13;
}
#u1866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:38px;
}
#u1867 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:40px;
  width:168px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:38px;
}
#u1868 {
  border-width:0px;
  position:absolute;
  left:1073px;
  top:40px;
  width:168px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1869 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1870 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1871 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1872_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1872 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:35px;
}
#u1873 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:78px;
  width:105px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1874_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1874 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1875 {
  border-width:0px;
  position:absolute;
  left:605px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1876 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1877 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:35px;
}
#u1878 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:78px;
  width:168px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:35px;
}
#u1879 {
  border-width:0px;
  position:absolute;
  left:1073px;
  top:78px;
  width:168px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1880 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1881 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1882 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1883 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:35px;
}
#u1884 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:113px;
  width:105px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1885 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1886 {
  border-width:0px;
  position:absolute;
  left:605px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1887 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1888 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
  color:#FF0808;
}
#u1888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:35px;
}
#u1889 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:113px;
  width:168px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:35px;
}
#u1890 {
  border-width:0px;
  position:absolute;
  left:1073px;
  top:113px;
  width:168px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1891_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1891_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1891_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1891 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:1077px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u1891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1891_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1891.disabled {
}
#u1892_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1892_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1892 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1077px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u1892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1892_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1892.disabled {
}
#u1893_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1893_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1893 {
  border-width:0px;
  position:absolute;
  left:622px;
  top:1077px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u1893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1893_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1893.disabled {
}
#u1894_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1894_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u1894 {
  border-width:0px;
  position:absolute;
  left:826px;
  top:1077px;
  width:140px;
  height:30px;
  display:flex;
  color:#B2B0B0;
}
#u1894 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1894_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u1894.disabled {
}
.u1894_input_option {
}
#u1895_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1895_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u1895 {
  border-width:0px;
  position:absolute;
  left:994px;
  top:1077px;
  width:140px;
  height:30px;
  display:flex;
  color:#B2B0B0;
}
#u1895 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1895_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u1895.disabled {
}
.u1895_input_option {
}
#u1896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1896 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:1077px;
  width:80px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u1896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1897 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:391px;
  width:20px;
  height:20px;
  display:flex;
}
#u1897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1898 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:394px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1898 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1898_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1899 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:438px;
  width:20px;
  height:20px;
  display:flex;
}
#u1899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1900 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:441px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1900 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1900_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1901 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1214px;
  width:20px;
  height:20px;
  display:flex;
}
#u1901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1902 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1216px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1902 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1902_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1903 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1264px;
  width:20px;
  height:20px;
  display:flex;
}
#u1903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1904 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1266px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1904 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1904_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1905 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1315px;
  width:20px;
  height:20px;
  display:flex;
}
#u1905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1906_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1906 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1318px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1906 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1906_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1907 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1364px;
  width:20px;
  height:20px;
  display:flex;
}
#u1907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1908 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1367px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1908 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1908_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1909 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1411px;
  width:20px;
  height:20px;
  display:flex;
}
#u1909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1910_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1910 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1414px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1910 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1910_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u1911 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1828px;
  width:1200px;
  height:768px;
  display:flex;
}
#u1911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u1912 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:1928px;
  width:982px;
  height:649px;
  display:flex;
}
#u1912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1913 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:1871px;
  width:1px;
  height:8px;
  display:flex;
}
#u1913 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u1914 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1828px;
  width:202px;
  height:768px;
  display:flex;
}
#u1914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1915 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:1839px;
  width:40px;
  height:40px;
  display:flex;
  color:#535252;
}
#u1915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u1916 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:1845px;
  width:87px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u1916 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1916_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u1917 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:1846px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u1917 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1917_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u1918 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:1853px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u1918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1919 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:1897px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u1919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1920 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:1897px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1920 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1920_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u1921 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:1928px;
  width:5px;
  height:5px;
  display:flex;
}
#u1921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1922_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u1922 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:1923px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u1922 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1922_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1923 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2067px;
  width:201px;
  height:45px;
  display:flex;
}
#u1923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1924 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:1978px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u1924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1925 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1980px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1925 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1925_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1926 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2021px;
  width:20px;
  height:20px;
  display:flex;
}
#u1926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1927 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2023px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1927 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1927_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1928 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2081px;
  width:20px;
  height:20px;
  display:flex;
}
#u1928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1929 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2083px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1929 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1929_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1930 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2131px;
  width:20px;
  height:20px;
  display:flex;
}
#u1930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1931 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2133px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1931 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1931_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1932 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2182px;
  width:20px;
  height:20px;
  display:flex;
}
#u1932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1933 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2185px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1933 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1933_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1934 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2231px;
  width:20px;
  height:20px;
  display:flex;
}
#u1934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1935 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2234px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1935 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1935_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1936 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2278px;
  width:20px;
  height:20px;
  display:flex;
}
#u1936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u1937 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2281px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u1937 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1937_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1938_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1938_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1938 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:1947px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u1938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1938_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1938.disabled {
}
#u1939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1939 {
  border-width:0px;
  position:absolute;
  left:655px;
  top:1947px;
  width:80px;
  height:30px;
  display:flex;
}
#u1939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1940 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:2046px;
  width:955px;
  height:148px;
}
#u1941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u1941 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1942 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1943 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1944 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1945_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1945 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1946 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1947_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u1947 {
  border-width:0px;
  position:absolute;
  left:620px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:115px;
  height:40px;
}
#u1948 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:0px;
  width:115px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u1949 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u1949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u1950 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1951 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1952 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1953 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1954 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1955 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u1956 {
  border-width:0px;
  position:absolute;
  left:620px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:115px;
  height:38px;
}
#u1957 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:40px;
  width:115px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u1958 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u1958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u1959 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1960 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1961 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1962 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1963 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1964_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1964 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1965 {
  border-width:0px;
  position:absolute;
  left:620px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:115px;
  height:35px;
}
#u1966 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:78px;
  width:115px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u1967 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u1968 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1969 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1970 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1971 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1972 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1973 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u1974 {
  border-width:0px;
  position:absolute;
  left:620px;
  top:113px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:115px;
  height:35px;
}
#u1975 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:113px;
  width:115px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u1976 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u1976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1977_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1977_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1977 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1947px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u1977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1977_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u1977.disabled {
}
#u1978 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 221, 221, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1979 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1995px;
  width:255px;
  height:36px;
  display:flex;
}
#u1979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:36px;
  background:inherit;
  background-color:rgba(234, 155, 19, 1);
  border:none;
  border-radius:8px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1980 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1995px;
  width:94px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u1980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1981 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 221, 221, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1982 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:1995px;
  width:255px;
  height:36px;
  display:flex;
}
#u1982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:36px;
  background:inherit;
  background-color:rgba(234, 155, 19, 1);
  border:none;
  border-radius:8px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1983 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:1995px;
  width:94px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u1983 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1984 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 221, 221, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1985 {
  border-width:0px;
  position:absolute;
  left:761px;
  top:1995px;
  width:255px;
  height:36px;
  display:flex;
}
#u1985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:36px;
  background:inherit;
  background-color:rgba(234, 155, 19, 1);
  border:none;
  border-radius:8px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1986 {
  border-width:0px;
  position:absolute;
  left:761px;
  top:1995px;
  width:94px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u1986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1987_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u1987 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2715px;
  width:1200px;
  height:768px;
  display:flex;
}
#u1987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1988_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u1988 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:2815px;
  width:982px;
  height:649px;
  display:flex;
}
#u1988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1988_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1989 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:2758px;
  width:1px;
  height:8px;
  display:flex;
}
#u1989 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1989_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1990_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u1990 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2715px;
  width:202px;
  height:768px;
  display:flex;
}
#u1990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1991_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1991 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:2726px;
  width:40px;
  height:40px;
  display:flex;
  color:#535252;
}
#u1991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u1992 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:2732px;
  width:87px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u1992 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1992_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u1993 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:2733px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u1993 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1993_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1994_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u1994 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:2740px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u1994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1995_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u1995 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:2784px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u1995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1996 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:2784px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1996 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1997_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u1997 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:2815px;
  width:5px;
  height:5px;
  display:flex;
}
#u1997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u1998 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:2810px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u1998 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1998_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1999 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3005px;
  width:201px;
  height:45px;
  display:flex;
}
#u1999 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2000 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2865px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u2000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2001 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2867px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2001 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2001_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2002 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2908px;
  width:20px;
  height:20px;
  display:flex;
}
#u2002 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2003 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2910px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2003 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2003_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2004 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:2968px;
  width:20px;
  height:20px;
  display:flex;
}
#u2004 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2005 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2970px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2005 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2005_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2006 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3018px;
  width:20px;
  height:20px;
  display:flex;
}
#u2006 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2006_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2007 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3020px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2007 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2007_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2008_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2008 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3069px;
  width:20px;
  height:20px;
  display:flex;
}
#u2008 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2008_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2009 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3072px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2009 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2009_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2010_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2010 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3118px;
  width:20px;
  height:20px;
  display:flex;
}
#u2010 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2010_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2011 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3121px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2011 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2011_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2012_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2012 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3165px;
  width:20px;
  height:20px;
  display:flex;
}
#u2012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2013 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3168px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2013 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2013_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2014_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2014_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2014 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:2830px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u2014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2014_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2014.disabled {
}
#u2015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2015 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:2830px;
  width:80px;
  height:30px;
  display:flex;
}
#u2015 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2015_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2016 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:2872px;
  width:1014px;
  height:113px;
}
#u2017_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u2017 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:40px;
}
#u2018 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:100px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2019_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:40px;
}
#u2019 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:0px;
  width:105px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2020_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:40px;
}
#u2020 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:0px;
  width:101px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2021_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:40px;
}
#u2021 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:0px;
  width:101px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2021 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2022_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:40px;
}
#u2022 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:0px;
  width:101px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2022 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2022_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2023_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:40px;
}
#u2023 {
  border-width:0px;
  position:absolute;
  left:608px;
  top:0px;
  width:101px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2023 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2023_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2024_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:40px;
}
#u2024 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:0px;
  width:101px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2025_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:40px;
}
#u2025 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:0px;
  width:101px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2025 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2025_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2026_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:40px;
}
#u2026 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:0px;
  width:103px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2027_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u2027 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2027 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2028_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:38px;
}
#u2028 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:40px;
  width:100px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2029_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:38px;
}
#u2029 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:40px;
  width:105px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2029 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2030_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:38px;
}
#u2030 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:40px;
  width:101px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2031_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:38px;
}
#u2031 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:40px;
  width:101px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2031 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2032_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:38px;
}
#u2032 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:40px;
  width:101px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2032 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2033_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:38px;
}
#u2033 {
  border-width:0px;
  position:absolute;
  left:608px;
  top:40px;
  width:101px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2034_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:38px;
}
#u2034 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:40px;
  width:101px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2035_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:38px;
}
#u2035 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:40px;
  width:101px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2036_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:38px;
}
#u2036 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:40px;
  width:103px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2037_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u2037 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
}
#u2038 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:78px;
  width:100px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2039_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:35px;
}
#u2039 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:78px;
  width:105px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2039 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2039_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2040_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
}
#u2040 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:78px;
  width:101px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2041_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
}
#u2041 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:78px;
  width:101px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2042_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
}
#u2042 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:78px;
  width:101px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2043_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
}
#u2043 {
  border-width:0px;
  position:absolute;
  left:608px;
  top:78px;
  width:101px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2043 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2044_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
}
#u2044 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:78px;
  width:101px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2045_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
}
#u2045 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:78px;
  width:101px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2046_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:35px;
}
#u2046 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:78px;
  width:103px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2047_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2047_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2047_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2047 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:2830px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u2047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2047_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2047.disabled {
}
#u2048_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2048_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2048 {
  border-width:0px;
  position:absolute;
  left:622px;
  top:2830px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u2048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2048_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2048.disabled {
}
#u2049_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2049_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2049_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u2049 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:2830px;
  width:140px;
  height:30px;
  display:flex;
  color:#B2B0B0;
}
#u2049 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2049_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u2049.disabled {
}
.u2049_input_option {
}
#u2050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u2050 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3567px;
  width:1200px;
  height:768px;
  display:flex;
}
#u2050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2051_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u2051 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:3667px;
  width:982px;
  height:649px;
  display:flex;
}
#u2051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2052 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:3610px;
  width:1px;
  height:8px;
  display:flex;
}
#u2052 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2053_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u2053 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3567px;
  width:202px;
  height:768px;
  display:flex;
}
#u2053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2054_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u2054 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:3578px;
  width:40px;
  height:40px;
  display:flex;
  color:#535252;
}
#u2054 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u2055 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:3584px;
  width:87px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u2055 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2055_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2056_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u2056 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:3585px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u2056 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2056_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2057_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u2057 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:3592px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u2057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2058_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u2058 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:3636px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u2058 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2058_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2059 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:3636px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2059 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2059_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2060_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u2060 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:3667px;
  width:5px;
  height:5px;
  display:flex;
}
#u2060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u2061 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:3662px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u2061 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2061_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2062 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:3910px;
  width:201px;
  height:45px;
  display:flex;
}
#u2062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2063_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2063 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3717px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u2063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2064 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3719px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2064 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2064_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2065_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2065 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3760px;
  width:20px;
  height:20px;
  display:flex;
}
#u2065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2066_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2066 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3762px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2066 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2066_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2067 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3820px;
  width:20px;
  height:20px;
  display:flex;
}
#u2067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2068 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3822px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2068 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2068_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2069_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2069 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3870px;
  width:20px;
  height:20px;
  display:flex;
}
#u2069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2070 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3872px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2070 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2070_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2071 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3921px;
  width:20px;
  height:20px;
  display:flex;
}
#u2071 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2072_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2072 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3924px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2072 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2072_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2073_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2073 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:3970px;
  width:20px;
  height:20px;
  display:flex;
}
#u2073 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2073_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2074_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2074 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:3973px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2074 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2074_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2075 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4017px;
  width:20px;
  height:20px;
  display:flex;
}
#u2075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2076 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4020px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2076 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2076_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2077_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2077_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2077_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2077 {
  border-width:0px;
  position:absolute;
  left:514px;
  top:3679px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u2077 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2077_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2077.disabled {
}
#u2078_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2078 {
  border-width:0px;
  position:absolute;
  left:869px;
  top:3679px;
  width:80px;
  height:30px;
  display:flex;
}
#u2078 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2078_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2079 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:3767px;
  width:840px;
  height:148px;
}
#u2080_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2080 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2081_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2081 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2081 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2082_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2082 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2083_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2083 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2083 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2083_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2084_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2084 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2084 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2084_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2085 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2086_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:40px;
}
#u2086 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:0px;
  width:120px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2086 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2086_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2087 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2087 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2087_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2088 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2088 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2088_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2089_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2089 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2090_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2090 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2091_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2091 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2092_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2092 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2092 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2092_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2093_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:38px;
}
#u2093 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:40px;
  width:120px;
  height:38px;
  display:flex;
  font-size:15px;
  color:#13BC75;
}
#u2093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2094 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2095_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2095 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2096_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2096 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2097_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2097 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2098_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2098 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2098 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2098_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2099 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2099 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2100_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2100 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:78px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2100 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2101_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2101 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2102 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2103 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2104 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2105 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2106 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
}
#u2106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:35px;
}
#u2107 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:113px;
  width:120px;
  height:35px;
  display:flex;
  font-size:15px;
  color:#FF0808;
}
#u2107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2108_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2108_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2108 {
  border-width:0px;
  position:absolute;
  left:313px;
  top:3679px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u2108 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2108_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2108.disabled {
}
#u2109_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2109_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u2109 {
  border-width:0px;
  position:absolute;
  left:716px;
  top:3679px;
  width:140px;
  height:30px;
  display:flex;
  color:#B2B0B0;
}
#u2109 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2109_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u2109.disabled {
}
.u2109_input_option {
}
#u2110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2110 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:3679px;
  width:80px;
  height:30px;
  display:flex;
}
#u2110 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2110_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2111 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 221, 221, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2112 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:3719px;
  width:255px;
  height:36px;
  display:flex;
}
#u2112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:36px;
  background:inherit;
  background-color:rgba(234, 155, 19, 1);
  border:none;
  border-radius:8px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2113 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:3719px;
  width:94px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u2113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2114 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:255px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 221, 221, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2115 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:3719px;
  width:255px;
  height:36px;
  display:flex;
}
#u2115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:36px;
  background:inherit;
  background-color:rgba(234, 155, 19, 1);
  border:none;
  border-radius:8px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2116 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:3719px;
  width:94px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u2116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:490px;
  height:614px;
  background:inherit;
  background-color:rgba(236, 236, 236, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2117 {
  border-width:0px;
  position:absolute;
  left:1342px;
  top:3597px;
  width:490px;
  height:614px;
  display:flex;
}
#u2117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2118 {
  border-width:0px;
  position:absolute;
  left:1355px;
  top:3630px;
  width:72px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u2118 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2118_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2119 {
  border-width:0px;
  position:absolute;
  left:1405px;
  top:4035px;
  width:140px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u2119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2120_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2120_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2120 {
  border-width:0px;
  position:absolute;
  left:1483px;
  top:3834px;
  width:300px;
  height:30px;
  display:flex;
}
#u2120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2120_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2120.disabled {
}
#u2121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2121 {
  border-width:0px;
  position:absolute;
  left:1403px;
  top:3840px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2121 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2121_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 237, 237, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2122 {
  border-width:0px;
  position:absolute;
  left:1343px;
  top:3659px;
  width:488px;
  height:50px;
  display:flex;
}
#u2122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FF0000;
}
#u2123 {
  border-width:0px;
  position:absolute;
  left:1353px;
  top:3676px;
  width:193px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#FF0000;
}
#u2123 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2123_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8C8B8B;
}
#u2124 {
  border-width:0px;
  position:absolute;
  left:1575px;
  top:3675px;
  width:150px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#8C8B8B;
}
#u2124 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2124_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:335px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2125 {
  border-width:0px;
  position:absolute;
  left:1405px;
  top:3786px;
  width:335px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2125_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2126_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2126_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2126 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:3899px;
  width:300px;
  height:30px;
  display:flex;
}
#u2126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2126_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2126.disabled {
}
#u2127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2127 {
  border-width:0px;
  position:absolute;
  left:1405px;
  top:3905px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2127 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2127_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2128_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#757575;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2128_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#757575;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(217, 217, 217, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#757575;
}
#u2128 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:3961px;
  width:300px;
  height:30px;
  display:flex;
  color:#757575;
}
#u2128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2128_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#757575;
}
#u2128.disabled {
}
#u2129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2129 {
  border-width:0px;
  position:absolute;
  left:1361px;
  top:3967px;
  width:124px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2129 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2129_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2130_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:491px;
  height:2px;
}
#u2130 {
  border-width:0px;
  position:absolute;
  left:1342px;
  top:3765px;
  width:490px;
  height:1px;
  display:flex;
  opacity:0.5;
}
#u2130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2130_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:15px;
}
#u2131 {
  border-width:0px;
  position:absolute;
  left:1391px;
  top:3737px;
  width:45px;
  height:17px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:15px;
}
#u2131 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2131_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
}
#u2132 {
  border-width:0px;
  position:absolute;
  left:1490px;
  top:3737px;
  width:45px;
  height:17px;
  display:flex;
  font-size:15px;
}
#u2132 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2132_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2133_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:53px;
  height:5px;
}
#u2133 {
  border-width:0px;
  position:absolute;
  left:1389px;
  top:3759px;
  width:50px;
  height:2px;
  display:flex;
}
#u2133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:490px;
  height:614px;
  background:inherit;
  background-color:rgba(236, 236, 236, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2134 {
  border-width:0px;
  position:absolute;
  left:1892px;
  top:3597px;
  width:490px;
  height:614px;
  display:flex;
}
#u2134 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2135 {
  border-width:0px;
  position:absolute;
  left:1905px;
  top:3630px;
  width:72px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u2135 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2135_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2136 {
  border-width:0px;
  position:absolute;
  left:1955px;
  top:4035px;
  width:140px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u2136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2137_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2137_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2137 {
  border-width:0px;
  position:absolute;
  left:2033px;
  top:3834px;
  width:300px;
  height:30px;
  display:flex;
}
#u2137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2137_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2137.disabled {
}
#u2138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2138 {
  border-width:0px;
  position:absolute;
  left:1909px;
  top:3840px;
  width:124px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2138 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2138_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 237, 237, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2139 {
  border-width:0px;
  position:absolute;
  left:1893px;
  top:3659px;
  width:488px;
  height:50px;
  display:flex;
}
#u2139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FF0000;
}
#u2140 {
  border-width:0px;
  position:absolute;
  left:1903px;
  top:3676px;
  width:193px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#FF0000;
}
#u2140 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2140_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8C8B8B;
}
#u2141 {
  border-width:0px;
  position:absolute;
  left:2125px;
  top:3675px;
  width:150px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#8C8B8B;
}
#u2141 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2141_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:335px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2142 {
  border-width:0px;
  position:absolute;
  left:1955px;
  top:3784px;
  width:335px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2142 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2142_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2143_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2143_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2143 {
  border-width:0px;
  position:absolute;
  left:2035px;
  top:3899px;
  width:300px;
  height:30px;
  display:flex;
}
#u2143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2143_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2143.disabled {
}
#u2144_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2144 {
  border-width:0px;
  position:absolute;
  left:1955px;
  top:3905px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2144 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2144_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2145_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#757575;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2145_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#757575;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(217, 217, 217, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#757575;
}
#u2145 {
  border-width:0px;
  position:absolute;
  left:2035px;
  top:3961px;
  width:300px;
  height:30px;
  display:flex;
  color:#757575;
}
#u2145 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2145_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#757575;
}
#u2145.disabled {
}
#u2146_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2146 {
  border-width:0px;
  position:absolute;
  left:1923px;
  top:3967px;
  width:112px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2146 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2146_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:491px;
  height:2px;
}
#u2147 {
  border-width:0px;
  position:absolute;
  left:1892px;
  top:3765px;
  width:490px;
  height:1px;
  display:flex;
  opacity:0.5;
}
#u2147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
}
#u2148 {
  border-width:0px;
  position:absolute;
  left:1941px;
  top:3737px;
  width:45px;
  height:17px;
  display:flex;
  font-size:15px;
}
#u2148 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2148_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2149_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:15px;
}
#u2149 {
  border-width:0px;
  position:absolute;
  left:2040px;
  top:3737px;
  width:45px;
  height:17px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:15px;
}
#u2149 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2149_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2150_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:53px;
  height:5px;
}
#u2150 {
  border-width:0px;
  position:absolute;
  left:2038px;
  top:3760px;
  width:50px;
  height:2px;
  display:flex;
}
#u2150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2151_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u2151 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4441px;
  width:1200px;
  height:768px;
  display:flex;
}
#u2151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u2152 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:4541px;
  width:982px;
  height:649px;
  display:flex;
}
#u2152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2153 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:4484px;
  width:1px;
  height:8px;
  display:flex;
}
#u2153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u2154 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4441px;
  width:202px;
  height:768px;
  display:flex;
}
#u2154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2155_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u2155 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:4452px;
  width:40px;
  height:40px;
  display:flex;
  color:#535252;
}
#u2155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u2156 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:4458px;
  width:87px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u2156 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2156_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u2157 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:4459px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u2157 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2157_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2158_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u2158 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:4466px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u2158 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2159_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u2159 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:4510px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u2159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2160 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:4510px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2160 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2160_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2161_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u2161 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:4541px;
  width:5px;
  height:5px;
  display:flex;
}
#u2161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u2162 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:4536px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u2162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2162_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2163 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4831px;
  width:201px;
  height:45px;
  display:flex;
}
#u2163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2164_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2164 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4591px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u2164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2165 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4593px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2165 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2165_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2166 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4634px;
  width:20px;
  height:20px;
  display:flex;
}
#u2166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2167 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4636px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2167 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2167_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2168 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4694px;
  width:20px;
  height:20px;
  display:flex;
}
#u2168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2169 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4696px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2169 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2169_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2170 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4744px;
  width:20px;
  height:20px;
  display:flex;
}
#u2170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2171 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4746px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2171 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2171_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2172 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4795px;
  width:20px;
  height:20px;
  display:flex;
}
#u2172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2173 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4798px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2173 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2173_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2174 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4844px;
  width:20px;
  height:20px;
  display:flex;
}
#u2174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2175 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4847px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2175 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2175_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2176 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:4891px;
  width:20px;
  height:20px;
  display:flex;
}
#u2176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2177 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:4894px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2177 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2177_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2178 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:4610px;
  width:800px;
  height:154px;
}
#u2179_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u2179 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2180_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u2180 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2181_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u2181 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2182_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
}
#u2182 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-size:15px;
}
#u2182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2183_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2183 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2184_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2184 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:40px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
  color:#1589E6;
}
#u2184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2185_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2185 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:40px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2186_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2186 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:40px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2187_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2187 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:78px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2188_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2188 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:78px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2189_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2189 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:78px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2190_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2190 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:78px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2191_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2191 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:116px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2192_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2192 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:116px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
  color:#0FB755;
}
#u2192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2193_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2193 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:116px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2194_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:38px;
}
#u2194 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:116px;
  width:200px;
  height:38px;
  display:flex;
  font-size:15px;
}
#u2194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:35px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2195 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:4555px;
  width:80px;
  height:35px;
  display:flex;
}
#u2195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2196_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2196_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B2B0B0;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u2196 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:4557px;
  width:140px;
  height:30px;
  display:flex;
  color:#B2B0B0;
}
#u2196 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2196_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B2B0B0;
}
#u2196.disabled {
}
.u2196_input_option {
}
#u2197_input {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2197_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#B5B3B3;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2197 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:4558px;
  width:188px;
  height:30px;
  display:flex;
  color:#B5B3B3;
}
#u2197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2197_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#B5B3B3;
}
#u2197.disabled {
}
#u2198_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1200px;
  height:768px;
}
#u2198 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5298px;
  width:1200px;
  height:768px;
  display:flex;
}
#u2198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2199_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:982px;
  height:649px;
}
#u2199 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:5398px;
  width:982px;
  height:649px;
  display:flex;
}
#u2199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2200_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2200 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:5341px;
  width:1px;
  height:8px;
  display:flex;
}
#u2200 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2201_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:768px;
}
#u2201 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5298px;
  width:202px;
  height:768px;
  display:flex;
}
#u2201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u2202 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:5309px;
  width:40px;
  height:40px;
  display:flex;
  color:#535252;
}
#u2202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u2203 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:5315px;
  width:87px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:26px;
  color:#FFFFFF;
}
#u2203 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2203_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2204_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#6A6969;
}
#u2204 {
  border-width:0px;
  position:absolute;
  left:1090px;
  top:5316px;
  width:68px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#6A6969;
}
#u2204 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2204_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2205_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:10px;
}
#u2205 {
  border-width:0px;
  position:absolute;
  left:1167px;
  top:5323px;
  width:18px;
  height:10px;
  display:flex;
  color:#6A6969;
}
#u2205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u2206 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:5367px;
  width:40px;
  height:40px;
  display:flex;
  font-size:14px;
}
#u2206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2207 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:5367px;
  width:65px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2207 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2207_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2208_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:5px;
}
#u2208 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:5398px;
  width:5px;
  height:5px;
  display:flex;
}
#u2208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u2209 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:5393px;
  width:28px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u2209 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2209_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:45px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2210 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:5733px;
  width:201px;
  height:45px;
  display:flex;
}
#u2210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2211_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2211 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5448px;
  width:20px;
  height:20px;
  display:flex;
  color:#FFFFFF;
}
#u2211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(21, 137, 230, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2212 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5450px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2212 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2212_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2213_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2213 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5491px;
  width:20px;
  height:20px;
  display:flex;
}
#u2213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2214 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5493px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2214 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2214_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2215_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2215 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5551px;
  width:20px;
  height:20px;
  display:flex;
}
#u2215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2216 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5553px;
  width:75px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2216 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2216_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2217 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5601px;
  width:20px;
  height:20px;
  display:flex;
}
#u2217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2218 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5603px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2218 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2218_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2219_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2219 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5652px;
  width:20px;
  height:20px;
  display:flex;
}
#u2219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2219_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2220 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5655px;
  width:60px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2220 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2220_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2221 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5701px;
  width:20px;
  height:20px;
  display:flex;
}
#u2221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2222_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2222 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5704px;
  width:90px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2222 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2222_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2223_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2223 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:5748px;
  width:20px;
  height:20px;
  display:flex;
}
#u2223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
}
#u2224 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:5751px;
  width:55px;
  height:17px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
}
#u2224 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2224_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2225 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:5419px;
  width:140px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u2225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2226 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:5419px;
  width:140px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u2226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2227_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:24px;
}
#u2227 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:5427px;
  width:26px;
  height:24px;
  display:flex;
}
#u2227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2228_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:24px;
}
#u2228 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:5427px;
  width:26px;
  height:24px;
  display:flex;
}
#u2228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:22px;
  color:#F70000;
}
#u2229 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:352px;
  width:330px;
  height:26px;
  display:flex;
  font-size:22px;
  color:#F70000;
}
#u2229 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2229_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:858px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:26px;
  color:#F70000;
}
#u2230 {
  border-width:0px;
  position:absolute;
  left:556px;
  top:801px;
  width:858px;
  height:31px;
  display:flex;
  font-size:26px;
  color:#F70000;
}
#u2230 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2230_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1010px;
  height:93px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:26px;
  color:#F70000;
}
#u2231 {
  border-width:0px;
  position:absolute;
  left:1381px;
  top:4275px;
  width:1010px;
  height:93px;
  display:flex;
  font-size:26px;
  color:#F70000;
}
#u2231 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2231_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
