ALTER TABLE `channel_payment`
    ADD COLUMN `top_lv` int UNSIGNED NOT NULL DEFAULT 9999 COMMENT '优先级' AFTER `created_at`;

ALTER TABLE `merchant_orders_err`
    ADD COLUMN `category_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道商' AFTER `merchant_id`,
    ADD COLUMN `channel_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道ID' AFTER `category_id`,
    ADD COLUMN `broker_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '代理ID' AFTER `channel_id`;
