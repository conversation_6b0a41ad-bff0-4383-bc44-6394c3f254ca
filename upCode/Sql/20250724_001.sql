CREATE TABLE `merchant_order_tg`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT,
    `merchant_id`    int                                     NOT NULL COMMENT '商户ID',
    `order_sn`       varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台订单号',
    `merchant_sn`    varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户订单号',
    `chat_id`        varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户群组ID',
    `message_id`     varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '群组消息ID',
    `sys_chat_id`    varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单异常群组ID',
    `sys_message_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单异常群组消息ID',
    `result`         varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上游查单结果',
    `created_at`     timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商户TG群组查单反馈';
