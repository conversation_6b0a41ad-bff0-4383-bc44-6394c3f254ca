<?php

namespace Vym\GoogleAuthenticator\Http\Controllers;

use App\Services\System\AdminLoginLogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Slowlyo\OwlAdmin\Admin;
use Slowlyo\OwlAdmin\Controllers\AuthController as AdminAuthController;
use Slowlyo\OwlAdmin\Renderers\Form;
use Symfony\Component\HttpFoundation\Response;
use Vym\GoogleAuthenticator\Utils\GoogleAuthenticator;

class AuthController extends AdminAuthController
{

    public static function getClientIp():string
    {
        $ips = request()->header('X-Forwarded-For');
        if (!$ips) $ips = request()->getClientIp();

        // 获取当前商户请求IP
        return Arr::first(array_filter(explode(',', $ips)));
    }

    public function login(Request $request)
    {
        if (Admin::config('admin.auth.login_captcha')) {
            if (!$request->has('captcha')) {
                return $this
                    ->response()
                    ->fail(admin_trans('admin.required', ['attribute' => admin_trans('admin.captcha')]));
            }

            if (strtolower(cache()->pull($request->sys_captcha)) != strtolower($request->captcha)) {
                return $this->response()->fail(admin_trans('admin.captcha_error'));
            }
        }

        try {
            $validator = Validator::make($request->all(), [
                'username' => 'required',
                'password' => 'required',
            ], [
                'username.required' => admin_trans('admin.required', ['attribute' => admin_trans('admin.username')]),
                'password.required' => admin_trans('admin.required', ['attribute' => admin_trans('admin.password')]),
            ]);

            if ($validator->fails()) {
                abort(Response::HTTP_BAD_REQUEST, $validator->errors()->first());
            }

            $user = Admin::adminUserModel()::query()->where('username', $request->username)->first();

            if ($google_state = Arr::get($user, 'google_state', 0)) {
                $secret    = $request->input('secret');
                $google2fa = new GoogleAuthenticator();
                $result    = $google2fa->verifyCode(Arr::get($user, 'google_secret', ''), $secret);
                if (!$result) {
                    abort(Response::HTTP_BAD_REQUEST, '谷歌验证码不正确');
                }
            }

            if ($user && Hash::check($request->password, $user->password)) {
                if (!$user->enabled) {
                    return $this->response()->fail(admin_trans('admin.user_disabled'));
                }

                $module = Admin::currentModule(true);
                $prefix = $module ? $module . '.' : '';
                /** @var \Slowlyo\OwlAdmin\Models\AdminUser $user */
                // 清空其它设备登录（保持唯一登录）
                $user->tokens()->delete();
                $token = $user->createToken($prefix . 'admin')->plainTextToken;

                $loginData  = [
                    'user_id'=>$user->id,
                    'ip'=>$this->getClientIp(),
                    'module'=>$module
                ];
                AdminLoginLogService::make()->addLoginLog($loginData);

                return $this->response()->success(compact('token', 'google_state'), admin_trans('admin.login_successful'));
            }

            abort(Response::HTTP_BAD_REQUEST, admin_trans('admin.login_failed'));
        } catch (\Exception $e) {
            return $this->response()->fail($e->getMessage());
        }
    }

    public function loginPage()
    {
        /** @noinspection all */
        $form = amis()
            ->Form()
            ->panelClassName('border-none')
            ->id('login-form')
            ->title()
            ->api(admin_url('/login'))
            ->initApi('/no-content')
            ->body([
                amis()->TextControl()->name('username')->placeholder(admin_trans('admin.username'))->required(),
                amis()
                    ->TextControl()
                    ->type('input-password')
                    ->name('password')
                    ->placeholder(admin_trans('admin.password'))
                    ->required(),
                amis()->InputGroupControl('captcha_group')->body([
                    amis()
                        ->TextControl('captcha', admin_trans('admin.captcha'))
                        ->placeholder(admin_trans('admin.captcha'))
                        ->required(),
                    amis()->HiddenControl()->name('sys_captcha'),
                    amis()->Service()->id('captcha-service')->api('get:' . admin_url('/captcha'))->body(
                        amis()
                            ->Image()
                            ->src('${captcha_img}')
                            ->height('1.917rem')
                            ->className('p-0 captcha-box')
                            ->imageClassName('rounded-r')
                            ->set(
                                'clickAction',
                                ['actionType' => 'reload', 'target' => 'captcha-service']
                            )
                    ),
                ])->visibleOn('${!!login_captcha}'),
                amis()->Flex()->alignItems('center')->items([
                    amis()->TextControl('secret', false)
                        ->type('input-verification-code')
                        ->set('separator', '${(index!=2) ? null : "-"}')
                        ->desc('<p style="font-size: 12px;text-align: center;">谷歌验证码</p>'),
                ]),

                amis()->CheckboxControl()->name('remember_me')->option(admin_trans('admin.remember_me'))->value(true),

                // 登录按钮
                amis()
                    ->VanillaAction()
                    ->actionType('submit')
                    ->label(admin_trans('admin.login'))
                    ->level('primary')
                    ->className('w-full'),
            ])
            // 清空默认的提交按钮
            ->actions([])
            ->onEvent([
                // 页面初始化事件
                'inited'     => [
                    'actions' => [
                        // 读取本地存储的登录参数
                        [
                            'actionType' => 'custom',
                            'script'     => <<<JS
let loginParams = localStorage.getItem(window.\$owl.getCacheKey('loginParams'))
    let hash = window.location.hash;
if(hash.includes('?redirect=')){

// 删除 ?redirect=xxx（无论 redirect= 后面是 & 结尾还是结尾）
hash = hash.replace(/[?&]redirect=[^&]*/g, '');

// 如果删除后 ? 后面没有参数了，把多余的 ? 也去掉
hash = hash.replace(/\?$/, '');

// 写回地址栏（不产生历史记录）
history.replaceState(null, null, window.location.pathname + window.location.search + hash);
}
if(loginParams){
    loginParams = JSON.parse(decodeURIComponent(window.atob(loginParams)))
    doAction({
        actionType: 'setValue',
        componentId: 'login-form',
        args: { value: loginParams }
    })
}
JS,
                        ],
                    ],
                ],
                // 登录成功事件
                'submitSucc' => [
                    'actions' => [
                        // 保存登录参数到本地, 并跳转到首页
                        [
                            'actionType' => 'custom',
                            'script'     => <<<JS
let _data = {}
if(event.data.remember_me){
    _data = { username: event.data.username, password: event.data.password}
}
window.\$owl.afterLoginSuccess(_data, event.data.result.data.token);

localStorage.setItem(window.\$owl.getCacheKey('google_state'),event.data.result.data.google_state)
JS,
                        ],
                    ],
                ],

                // 登录失败事件
                'submitFail' => [
                    'actions' => [
                        // 刷新验证码外层Service
                        ['actionType' => 'reload', 'componentId' => 'captcha-service'],
                    ],
                ],
            ]);

        $card = amis()->Panel()->className('w-96 m:w-full pt-3')->id('login-panel')->set('animations', [
            'enter' => [
                'delay'    => 0,
                'duration' => 0.5,
                'type'     => 'zoomIn',
            ],
        ])->body([
            amis()->Service()->api('/_settings')->body([
                amis()->Flex()->justify('space-between')->className('px-2.5 pb-2.5')->items([
                    amis()->Image()->src('${logo}')->width(40)->height(40)->visibleOn('${logo}'),
                    amis()
                        ->Tpl()
                        ->className('font-medium')
                        ->tpl('<div style="font-size: 24px">${app_name}</div>'),
                ]),
                $form,
            ]),
        ]);

        return amis()->Page()->className('login-bg')->css([
            '.captcha-box .cxd-Image--thumb' => [
                'padding' => '0',
                'cursor'  => 'pointer',
                'border'  => 'var(--Form-input-borderWidth) solid var(--Form-input-borderColor)',

                'border-top-right-radius'    => '4px',
                'border-bottom-right-radius' => '4px',
            ],
            '.cxd-Image-thumb'               => ['width' => 'auto'],
            '.login-bg'                      => [
                'background' => 'var(--owl-body-bg)',
            ],
        ])->body(
            amis()->Wrapper()->className("h-screen w-full flex items-center justify-center")->style([
                'position'           => 'static',
                'display'            => 'flex',
                'fontFamily'         => '',
                'backgroundSize'     => 'cover',
                'backgroundImage'    => 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr',
                'backgroundPosition' => '50% 50%',
                'flexWrap'           => 'nowrap',
                'fontSize'           => 12,
            ])->body($card)
        );
    }

    /**
     * 谷歌认证绑定
     *
     * @param null $action
     * @return JsonResponse|JsonResource|Form
     * @throws \Exception
     */
    public function googleAuth($action = null)
    {
        $admin     = admin_user();
        $google2fa = new GoogleAuthenticator();
        if ($action) {
            try {
                $key = request()->input('secret');
                if (!$admin->google_state) {
                    if (!$key) {
                        throw new  \Exception('请输入谷歌验证码');
                    }
                    $secretKey = Arr::get($admin, 'google_secret', '');
                    $verify    = $google2fa->verifyCode($secretKey, $key);
                    if (!$verify) {
                        throw new  \Exception('谷歌验证码不正确');
                    }
                    $admin->google_state = 1;
                    $admin->save();
                    return $this->response()->success([], '谷歌验证绑定成功');
                }
                return $this->response()->success([], '谷歌验证已绑定');
            } catch (\Throwable $e) {
                return $this->response()->fail($e->getMessage());
            }
        }

        if (!$admin->google_secret) {
            $admin->google_secret = $google2fa->createSecret();
            $admin->save();
        }
        $google_state = Arr::get($admin, 'google_state', 0);

        $moduleName = Admin::currentModule(true) ?? 'admin';
        $qrCodeUrl  = $google2fa->getQRCodeGoogleUrl(
            config('app.name', 'SuperPay') . ' ' . Str::ucfirst($moduleName),
            $admin->google_secret,
            $admin->username
        );

        $form = amis()->Form()
            ->mode('flex')
            ->api('post:/google_auth/setBuild')
            ->title(false)
            ->panelClassName('border-none')
            ->body([
                amis()->Flex()
                    ->justify('center')
                    ->items([
                        amis()->QRCode()->type('qr-code')->value($qrCodeUrl),
                    ]),
                amis()->TextControl('google_secret', '密&emsp;钥')->value($admin->google_secret)->required()->static(),

                amis()->Flex()->alignItems('center')->visible(!$google_state)->items([
                    amis()->TextControl('secret', false)
                        ->type('input-verification-code')
                        ->set('separator', '${(index!=2) ? null : "-"}')
                        ->desc('<p style="font-size: 12px;text-align: center;">谷歌验证码</p>')
                        ->required(),
                ]),
                // amis()->TextControl('secret', '验证码')->visible(!$google_state)->required(),

            ]);
        if ($google_state) {
            $form->actions([
                amis()->Flex()->justify('center')->items([
                    amis()->Button()->label('我已绑定，不再提示')
                        ->actionType('button')
                        ->level('warning')
                        ->close(true)
                        ->onEvent([
                            // 成功事件
                            'click' => [
                                'actions' => [
                                    // 保存登录参数到本地, 并跳转到首页
                                    [
                                        'actionType' => 'custom',
                                        'script'     => <<<JS
localStorage.setItem(window.\$owl.getCacheKey('google_state'),1)
JS,

                                    ],
                                ],
                            ],
                        ])
                ])
            ]);
        } else {
            $form->actions([
                amis()->Flex()->justify('center')->items([
                    amis()->Button()->label('立即绑定')->actionType('submit')->level('success')->close(true)
                ])
            ]);
        }

        return $form;
    }
}
