<?php

namespace Modules\Broker\Services;

use App\Models\Broker\BrokerBalance;
use Illuminate\Database\Eloquent\Builder;

class BalanceService extends BaseService
{
    protected string $modelName = BrokerBalance::class;

    /**
     * 排序
     *
     * @param Builder $query
     *
     * @return void
     */
    public function sortable($query)
    {
        if (request()->orderBy && request()->orderDir) {
            $query->orderBy(request()->orderBy, request()->orderDir ?? 'asc');
        } else {
            $query->orderByDesc($this->primaryKey());
        }
    }

    /**
     * 搜索
     *
     * @param Builder $query
     *
     * @return void
     */
    public function searchable($query)
    {
        $this->selectLike($query, request()->query());
        $this->loadBrokerId($query);
        $this->selectDateRange($query, 'created_at', request()->query());
        $query->when(request()->type, fn ($query)=>$query->where('type',request()->type));
    }
}
