<?php

namespace Modules\Broker\Services\System;

use App\Traits\Broker;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Slowlyo\OwlAdmin\Admin;
use Slowlyo\OwlAdmin\Models\AdminRole;
use Slowlyo\OwlAdmin\Services\AdminRoleService;

class AdminUserService extends \Slowlyo\OwlAdmin\Services\AdminUserService
{

    use Broker;


    /**
     * 列表 获取查询
     *
     * @return Builder
     */
    public function listQuery()
    {
        $query = $this->query();

        // 处理排序
        $this->sortable($query);

        // 自动加载 TableColumn 内的关联关系
        $this->loadRelations($query);

        // 处理查询
        $this->searchable($query);

        // 追加关联关系
        $this->addRelations($query);

        return $query;
    }


    /**
     * 添加关联关系
     *
     * 预留钩子, 方便处理只需要添加 [关联] 的情况
     *
     * @param        $query
     * @param string $scene 场景: list, detail, edit
     *
     * @return void
     */
    public function addRelations($query, string $scene = 'list')
    {
        $query->with(['roles']);
    }

    /**
     * 搜索
     *
     * @param $query
     *
     * @return void
     */
    public function searchable($query)
    {
        // 非超级管理员只能看到自己拥有的角色
        if (!Admin::user()->isAdministrator()) {
            $query->whereHas('roles', function ($query) {
                $query->whereIn('id', Admin::user()->roles()->pluck('id'));
            });
        }

        // 用户名搜索
        if ($username = request()->username) {
            $query->where('username', 'like', "%{$username}%");
        }

        // 真实姓名搜索
        if ($name = request()->name) {
            $query->where('name', 'like', "%{$name}%");
        }

        // 角色筛选
        if ($roles = request()->roles) {
            $roleIds = is_array($roles) ? $roles : [$roles];
            $query->whereHas('roles', function ($query) use ($roleIds) {
                $query->whereIn('id', $roleIds);
            });
        }

        // 状态筛选
        if (request()->filled('enabled')) {
            $query->where('enabled', request()->enabled);
        }

        // 创建时间范围筛选
        if ($created_at = request()->created_at) {
            $dates = safe_explode(',', $created_at);
            if (count($dates) == 2) {
                $query->whereBetween('created_at', [
                    $dates[0] . ' 00:00:00',
                    $dates[1] . ' 23:59:59'
                ]);
            }
        }

        $query->select(['id', 'name', 'username', 'avatar', 'enabled', 'created_at', 'master', 'broker_id']);

        $this->loadBrokerId($query);
    }


    /**
     * 保存数据
     * 处理模型属性和角色关联的保存
     *
     * @param array      $data       保存的数据
     * @param mixed|null $primaryKey 主键
     * @return bool
     */
    protected function saveData($data, $primaryKey = null)
    {
        $model   = $primaryKey ? $this->query()->find($primaryKey) : $this->getModel();
        $columns = $this->getTableColumns();
        $roles = Arr::pull($data, 'roles');
        // 加入代理ID
        $brokerId = Arr::get(admin_user(), 'broker_id', 0);
        Arr::set($data, 'broker_id', $brokerId);
        foreach ($data as $k => $v) {
            if (!in_array($k, $columns)) {
                continue;
            }

            $model->setAttribute($k, $v);
        }

        if ($model->save()) {
            $roles = collect($roles)->map(function ($role) use ($brokerId) {
                return [
                    'role_id'     => is_array($role)?Arr::get($role, 'id'):$role,
                    'broker_id' => $brokerId,
                ];
            })->toArray();

            $roles && $model->roles()->sync($roles);

            return true;
        }

        return false;
    }



    /**
     * 删除
     *
     * @param string $ids
     *
     * @return mixed
     */
    public function delete(string $ids)
    {
        DB::beginTransaction();
        try {

            $model = $this
                ->query()
                ->whereIn($this->primaryKey(), explode(',', $ids));

            admin_abort_if($model->clone()->whereHas('roles', fn($q) => $q->where('slug', AdminRole::SuperAdministrator))->exists(), admin_trans('admin.admin_user.cannot_delete'));

            foreach ($model->get() as $item) {
                $item->roles()->detach();
                $item->delete();
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();

            admin_abort($e->getMessage());
        }

        return true;
    }


    /**
     * 获取角色选项
     * 非超级管理员用户只能看到自己拥有的角色
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function roleOptions()
    {
        $query = AdminRoleService::make()->query();
        $query->where(function($query){
            $query->where('broker_id',Arr::get(admin_user(), 'broker_id'))
                ->orWhere('broker_id',0);
        });
        // 非超级管理员只能看到自己拥有的角色
        if (!Admin::user()->isAdministrator()) {
            $query->whereIn('id', Admin::user()->roles()->pluck('id'));
        }

        return $query->get(['id', 'name']);
    }
}
