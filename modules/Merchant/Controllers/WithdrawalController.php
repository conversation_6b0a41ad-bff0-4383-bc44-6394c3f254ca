<?php

namespace Modules\Merchant\Controllers;

use App\Models\Merchant\Merchant;
use App\Models\Merchant\MerchantWith;
use App\Support\Forms;
use App\Utils\E;
use Illuminate\Support\Arr;
use Modules\Merchant\Services\WithdrawalService;
use Slowlyo\OwlAdmin\Controllers\AdminController;
use Slowlyo\OwlAdmin\Renderers\Form;
use Slowlyo\OwlAdmin\Renderers\Page;
use Slowlyo\OwlAdmin\Support\Cores\AdminPipeline;

class WithdrawalController extends AdminController
{
    protected string $serviceName = WithdrawalService::class;


    public function list(): Page
    {
        $crud = $this->baseCRUD()
                     ->name(__CLASS__)
                     ->filterTogglable(false)
                     ->headerToolbar([
                         $this->createButton(true, 'md', '申请提现'),
                         ...$this->baseHeaderToolBar()
                     ])
                     ->bulkActions([])
                     ->filter(
                         $this->baseFilter()
                              ->actions([])
                              ->body([
                                  Forms::make()->selectLike([
                                      ['label' => '订单编号', 'value' => 'order_sn'],
                                  ]),
                                  Forms::make()->selectDateRange('created_at', [
                                      ['label' => '创建时间', 'value' => 'created_at'],
                                  ]),
                                  amis()->SelectControl('state', '订单状态')->options(MerchantWith::STATE)->size('sm')->clearable(),
                                  amis()->Button()->label(__('admin.reset'))->actionType('clear-and-submit')->className('mr-2 px-5'),
                                  amis()->Button()->label(__('admin.search'))->actionType('submit')->level('primary')->className('px-5'),
                              ])
                     )
                     ->columns([
                         amis()->TableColumn('id', 'ID')->fixed('left')->width(80),
                         amis()->TableColumn('order_sn', '订单编号')->align('center')->textOverflow('noWrap'),
                         amis()->TableColumn('state', '订单状态')->sortable()
                               ->type('status')
                               ->source(E::tableStatus(MerchantWith::STATE))->align('center')
                               ->align('center'),
                         amis()->TableColumn('amount', '提取金额')->type('number')->align('center')->sortable(),
                         amis()->TableColumn('okx', '欧意三档')->type('number')->align('center')->sortable(),
                         amis()->TableColumn('rise_rate', '上浮费率')->type('number')->align('center')->sortable(),
                         amis()->TableColumn('usdt', '下U数量')->sortable()->type('number')->suffix('U')->align('center'),
                         amis()->TableColumn('remark', '备注')->width(200)->textOverflow('ellipsis'),
                         amis()->TableColumn('created_at', '创建时间')->sortable()->type('datetime')->align('center')->textOverflow('noWrap'),
                     ]);
        return $this->baseList($crud);
    }

    /**
     * 获取新增页面
     *
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Resources\Json\JsonResource|\Slowlyo\OwlAdmin\Renderers\Form
     */
    public function create()
    {
        $this->isCreate = true;
        return $this->form(false)->panelClassName('p-0 border-none')->actions([])->mode('horizontal')->canAccessSuperData(false)->api($this->getStorePath());
    }

    public function form($isEdit = false): Form
    {
        bcscale(4);
        $merchant  = Merchant::query()->whereKey(Arr::get(admin_user(), 'merchant_id', 0))->first();
        $okxRate   = E::getOkxRate();
        $balance   = Arr::get($merchant, 'balance', 0);
        $freeze    = Arr::get($merchant, 'freeze', 0);
        $floatRate = Arr::get($merchant, 'rate', 0);
        $rate      = (float)bcadd($floatRate, $okxRate);
        $maxUsdt   = $rate>0?bcdiv($balance, $rate):0;
        return $this->baseForm(false)->title(false)->body([
            amis()->Tpl()->tpl("
                <div class='mb-10' style='display: flex;text-align: center;background: #FFEDED;'>
                    <p style='flex: 1;font-size: 16px;color:#FF0026' class='m-0 py-4'>可提现金额：{$balance} 元</p>
                    <p style='flex: 1;font-size: 16px;color:#8C8B8B' class='m-0 py-4'>冻结金额：{$freeze} 元</p>
                </div>
            "),
            amis()->GroupControl()->label('提现费率')->body([
                amis()->Tpl()->tpl("<p style='margin: 5px 0 0;'>{$rate}<span class='text-info'>（当前欧意三档 {$okxRate} + 上浮 {$floatRate}）</span></p>")
            ]),
            amis()->RadiosControl('type', '计算方式')->required()->value(0)->options([
                ['label' => '按金额', 'value' => 0],
                ['label' => '按数量', 'value' => 1]
            ]),
            amis()->HiddenControl('ra')->value($rate),
            amis()->NumberControl('amount', '提现金额')
                  ->min(0)
                  ->precision(2)
                  ->max($balance)
                  ->placeholder('请输入提现金额')
                  ->required()
                  ->hiddenOn('${type}')
                  ->onEvent([
                      'change' => [
                          'actions' => [
                              [
                                  'actionType'  => 'setValue',
                                  'componentId' => 'yjUsdt',
                                  'args'        => [
                                      'value' => '${ROUND(IF(type,ra*usdt,amount/ra),2)}',
                                  ]
                              ]
                          ]
                      ]
                  ]),
            amis()->NumberControl('usdt', '提现USDT数量')
                  ->min(0)
                  ->precision(2)
                  ->max($maxUsdt)
                  ->placeholder('请输入提现USDT数量')
                  ->required()
                  ->visibleOn('${type}')
                  ->onEvent([
                      'change' => [
                          'actions' => [
                              [
                                  'actionType'  => 'setValue',
                                  'componentId' => 'yjAmount',
                                  'args'        => [
                                      'value' => '${ROUND(IF(type,ra*usdt,amount/ra),2)}',
                                  ]
                              ]
                          ]
                      ]
                  ]),
            amis()->TextControl('password', '资金密码')
                  ->placeholder('请输入资金密码')
                  ->required(),

            amis()->NumberControl('usdt', '预计USDT数量')->id('yjUsdt')->precision(2)->disabled()->hiddenOn('${type}'),
            amis()->NumberControl('amount', '预计提现金额')->id('yjAmount')->precision(2)->disabled()->visibleOn('${type}'),
        ]);
    }

    public function detail(): Form
    {
        return $this->baseForm()->static()->body([]);
    }

    /**
     * 新增按钮
     *
     * @param bool|string $dialog     是否弹窗, 弹窗: true|dialog, 抽屉: drawer,
     * @param string      $dialogSize 弹窗大小, 默认: md, 可选值: xs | sm | md | lg | xl | full
     * @param string      $title      弹窗标题 & 按钮文字, 默认: 新增
     *
     * @return \Slowlyo\OwlAdmin\Renderers\DialogAction|\Slowlyo\OwlAdmin\Renderers\LinkAction
     */
    protected function createButton(bool|string $dialog = false, string $dialogSize = 'md', string $title = '')
    {
        $title  = $title ?: admin_trans('admin.create');
        $action = amis()->LinkAction()->link($this->getCreatePath());

        if ($dialog) {
            $form = amis()->Service()->schemaApi('get:' . $this->getCreatePath());

            if ($dialog === 'drawer') {
                $action = amis()->DrawerAction()->drawer(
                    amis()->Drawer()->closeOnEsc()->closeOnOutside()->title($title)->body($form)->size($dialogSize)
                );
            } else {
                $action = amis()->DialogAction()->dialog(
                    amis()->Dialog()->title($title)->body($form)->size($dialogSize)
                );
            }
        }

        $action->label($title)->icon('fa fa-add')->level('primary');

        return AdminPipeline::handle(AdminPipeline::PIPE_CREATE_ACTION, $action);
    }
}
