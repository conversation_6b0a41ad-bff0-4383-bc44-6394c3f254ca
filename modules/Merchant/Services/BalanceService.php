<?php

namespace Modules\Merchant\Services;

use App\Models\Merchant\MerchantBalance;
use Illuminate\Database\Eloquent\Builder;

class BalanceService extends BaseService
{
    protected string $modelName = MerchantBalance::class;

    /**
     * 排序
     *
     * @param Builder $query
     *
     * @return void
     */
    public function sortable($query)
    {
        if (request()->orderBy && request()->orderDir) {
            $query->orderBy(request()->orderBy, request()->orderDir ?? 'asc');
        } else {
            $query->orderByDesc($this->primaryKey());
        }
    }

    /**
     * 搜索
     *
     * @param Builder $query
     *
     * @return void
     */
    public function searchable($query)
    {
        $this->selectLike($query, request()->query());
        $this->selectDateRange($query, 'created_at',request()->query());
        $query->when(request()->type,fn($builder) => $builder->where('type', request()->type));
        $this->loadMerchantId($query);
    }
}
