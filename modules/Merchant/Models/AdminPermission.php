<?php

namespace Modules\Merchant\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class AdminPermission extends \Slowlyo\OwlAdmin\Models\AdminPermission
{
    protected $table = 'merchant_admin_permissions';
    
    public function menus(): BelongsToMany
    {
        return $this->belongsToMany(AdminMenu::class, 'merchant_admin_permission_menu', 'permission_id', 'menu_id')
            ->withTimestamps();
    }
}
