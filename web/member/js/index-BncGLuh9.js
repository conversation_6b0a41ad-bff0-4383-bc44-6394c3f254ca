import{s as w,N as X,l as H,m as _,g as Y,d as K,B as V,a$ as A,_ as x,b as $,k as E,n as z,v as q,r as J,Y as Q,a as U,R as Z,a5 as ee,aX as te,x as re}from"./bootstrap-DaIAlhlN.js";import{C as oe}from"./CheckOutlined-BuGcji7N.js";import{a4 as I,J as u,x as g,F,P as B,p as se}from"../jse/index-index-Dn0hz0U0.js";import{u as ne}from"./useRefs-CRLf0hMg.js";import{T as le}from"./index-BgRjLkzR.js";const ie=["normal","exception","active","success"],j=()=>({prefixCls:String,type:w(),percent:Number,format:K(),status:w(),showInfo:Y(),strokeWidth:Number,strokeLinecap:w(),strokeColor:_(),trailColor:String,width:Number,success:H(),gapDegree:Number,gapPosition:w(),size:X([String,Number,Array]),steps:Number,successPercent:Number,title:String,progressStatus:w()});function P(e){return!e||e<0?0:e>100?100:e}function D(e){let{success:t,successPercent:o}=e,r=o;return t&&"progress"in t&&(V(!1,"Progress","`success.progress` is deprecated. Please use `success.percent` instead."),r=t.progress),t&&"percent"in t&&(r=t.percent),r}function ae(e){let{percent:t,success:o,successPercent:r}=e;const s=P(D({success:o,successPercent:r}));return[s,P(P(t)-s)]}function ce(e){let{success:t={},strokeColor:o}=e;const{strokeColor:r}=t;return[r||A.green,o||null]}const W=(e,t,o)=>{var r,s,p,l;let n=-1,i=-1;if(t==="step"){const m=o.steps,d=o.strokeWidth;typeof e=="string"||typeof e=="undefined"?(n=e==="small"?2:14,i=d!=null?d:8):typeof e=="number"?[n,i]=[e,e]:[n=14,i=8]=e,n*=m}else if(t==="line"){const m=o==null?void 0:o.strokeWidth;typeof e=="string"||typeof e=="undefined"?i=m||(e==="small"?6:8):typeof e=="number"?[n,i]=[e,e]:[n=-1,i=8]=e}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e=="undefined"?[n,i]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[n,i]=[e,e]:(n=(s=(r=e[0])!==null&&r!==void 0?r:e[1])!==null&&s!==void 0?s:120,i=(l=(p=e[0])!==null&&p!==void 0?p:e[1])!==null&&l!==void 0?l:120));return{width:n,height:i}};var ue=function(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(o[r[s]]=e[r[s]]);return o};const de=()=>x(x({},j()),{strokeColor:_(),direction:w()}),ge=e=>{let t=[];return Object.keys(e).forEach(o=>{const r=parseFloat(o.replace(/%/g,""));isNaN(r)||t.push({key:r,value:e[o]})}),t=t.sort((o,r)=>o.key-r.key),t.map(o=>{let{key:r,value:s}=o;return`${s} ${r}%`}).join(", ")},fe=(e,t)=>{const{from:o=A.blue,to:r=A.blue,direction:s=t==="rtl"?"to left":"to right"}=e,p=ue(e,["from","to","direction"]);if(Object.keys(p).length!==0){const l=ge(p);return{backgroundImage:`linear-gradient(${s}, ${l})`}}return{backgroundImage:`linear-gradient(${s}, ${o}, ${r})`}},pe=I({compatConfig:{MODE:3},name:"ProgressLine",inheritAttrs:!1,props:de(),setup(e,t){let{slots:o,attrs:r}=t;const s=u(()=>{const{strokeColor:a,direction:f}=e;return a&&typeof a!="string"?fe(a,f):{backgroundColor:a}}),p=u(()=>e.strokeLinecap==="square"||e.strokeLinecap==="butt"?0:void 0),l=u(()=>e.trailColor?{backgroundColor:e.trailColor}:void 0),n=u(()=>{var a;return(a=e.size)!==null&&a!==void 0?a:[-1,e.strokeWidth||(e.size==="small"?6:8)]}),i=u(()=>W(n.value,"line",{strokeWidth:e.strokeWidth})),m=u(()=>{const{percent:a}=e;return x({width:`${P(a)}%`,height:`${i.value.height}px`,borderRadius:p.value},s.value)}),d=u(()=>D(e)),y=u(()=>{const{success:a}=e;return{width:`${P(d.value)}%`,height:`${i.value.height}px`,borderRadius:p.value,backgroundColor:a==null?void 0:a.strokeColor}}),v={width:i.value.width<0?"100%":i.value.width,height:`${i.value.height}px`};return()=>{var a;return g(F,null,[g("div",$($({},r),{},{class:[`${e.prefixCls}-outer`,r.class],style:[r.style,v]}),[g("div",{class:`${e.prefixCls}-inner`,style:l.value},[g("div",{class:`${e.prefixCls}-bg`,style:m.value},null),d.value!==void 0?g("div",{class:`${e.prefixCls}-success-bg`,style:y.value},null):null])]),(a=o.default)===null||a===void 0?void 0:a.call(o)])}}}),ve={percent:0,prefixCls:"vc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1},me=e=>{const t=B(null);return se(()=>{const o=Date.now();let r=!1;e.value.forEach(s=>{const p=(s==null?void 0:s.$el)||s;if(!p)return;r=!0;const l=p.style;l.transitionDuration=".3s, .3s, .3s, .06s",t.value&&o-t.value<100&&(l.transitionDuration="0s, 0s")}),r&&(t.value=Date.now())}),e},he={gapDegree:Number,gapPosition:{type:String},percent:{type:[Array,Number]},prefixCls:String,strokeColor:{type:[Object,String,Array]},strokeLinecap:{type:String},strokeWidth:Number,trailColor:String,trailWidth:Number,transition:String};var Ce=function(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(o[r[s]]=e[r[s]]);return o};let N=0;function R(e){return+e.replace("%","")}function T(e){return Array.isArray(e)?e:[e]}function M(e,t,o,r){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,p=arguments.length>5?arguments[5]:void 0;const l=50-r/2;let n=0,i=-l,m=0,d=-2*l;switch(p){case"left":n=-l,i=0,m=2*l,d=0;break;case"right":n=l,i=0,m=-2*l,d=0;break;case"bottom":i=l,d=2*l;break}const y=`M 50,50 m ${n},${i}
   a ${l},${l} 0 1 1 ${m},${-d}
   a ${l},${l} 0 1 1 ${-m},${d}`,v=Math.PI*2*l,a={stroke:o,strokeDasharray:`${t/100*(v-s)}px ${v}px`,strokeDashoffset:`-${s/2+e/100*(v-s)}px`,transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s"};return{pathString:y,pathStyle:a}}const ye=I({compatConfig:{MODE:3},name:"VCCircle",props:E(he,ve),setup(e){N+=1;const t=B(N),o=u(()=>T(e.percent)),r=u(()=>T(e.strokeColor)),[s,p]=ne();me(p);const l=()=>{const{prefixCls:n,strokeWidth:i,strokeLinecap:m,gapDegree:d,gapPosition:y}=e;let v=0;return o.value.map((a,f)=>{const c=r.value[f]||r.value[r.value.length-1],b=Object.prototype.toString.call(c)==="[object Object]"?`url(#${n}-gradient-${t.value})`:"",{pathString:C,pathStyle:S}=M(v,a,c,i,d,y);v+=a;const k={key:f,d:C,stroke:b,"stroke-linecap":m,"stroke-width":i,opacity:a===0?0:1,"fill-opacity":"0",class:`${n}-circle-path`,style:S};return g("path",$({ref:s(f)},k),null)})};return()=>{const{prefixCls:n,strokeWidth:i,trailWidth:m,gapDegree:d,gapPosition:y,trailColor:v,strokeLinecap:a,strokeColor:f}=e,c=Ce(e,["prefixCls","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","strokeColor"]),{pathString:b,pathStyle:C}=M(0,100,v,i,d,y);delete c.percent;const S=r.value.find(h=>Object.prototype.toString.call(h)==="[object Object]"),k={d:b,stroke:v,"stroke-linecap":a,"stroke-width":m||i,"fill-opacity":"0",class:`${n}-circle-trail`,style:C};return g("svg",$({class:`${n}-circle`,viewBox:"0 0 100 100"},c),[S&&g("defs",null,[g("linearGradient",{id:`${n}-gradient-${t.value}`,x1:"100%",y1:"0%",x2:"0%",y2:"0%"},[Object.keys(S).sort((h,O)=>R(h)-R(O)).map((h,O)=>g("stop",{key:O,offset:h,"stop-color":S[h]},null))])]),g("path",k,null),l().reverse()])}}}),be=()=>x(x({},j()),{strokeColor:_()}),$e=3,Se=e=>$e/e*100,ke=I({compatConfig:{MODE:3},name:"ProgressCircle",inheritAttrs:!1,props:E(be(),{trailColor:null}),setup(e,t){let{slots:o,attrs:r}=t;const s=u(()=>{var c;return(c=e.width)!==null&&c!==void 0?c:120}),p=u(()=>{var c;return(c=e.size)!==null&&c!==void 0?c:[s.value,s.value]}),l=u(()=>W(p.value,"circle")),n=u(()=>{if(e.gapDegree||e.gapDegree===0)return e.gapDegree;if(e.type==="dashboard")return 75}),i=u(()=>({width:`${l.value.width}px`,height:`${l.value.height}px`,fontSize:`${l.value.width*.15+6}px`})),m=u(()=>{var c;return(c=e.strokeWidth)!==null&&c!==void 0?c:Math.max(Se(l.value.width),6)}),d=u(()=>e.gapPosition||e.type==="dashboard"&&"bottom"||void 0),y=u(()=>ae(e)),v=u(()=>Object.prototype.toString.call(e.strokeColor)==="[object Object]"),a=u(()=>ce({success:e.success,strokeColor:e.strokeColor})),f=u(()=>({[`${e.prefixCls}-inner`]:!0,[`${e.prefixCls}-circle-gradient`]:v.value}));return()=>{var c;const b=g(ye,{percent:y.value,strokeWidth:m.value,trailWidth:m.value,strokeColor:a.value,strokeLinecap:e.strokeLinecap,trailColor:e.trailColor,prefixCls:e.prefixCls,gapDegree:n.value,gapPosition:d.value},null);return g("div",$($({},r),{},{class:[f.value,r.class],style:[r.style,i.value]}),[l.value.width<=20?g(le,null,{default:()=>[g("span",null,[b])],title:o.default}):g(F,null,[b,(c=o.default)===null||c===void 0?void 0:c.call(o)])])}}}),xe=()=>x(x({},j()),{steps:Number,strokeColor:X(),trailColor:String}),Pe=I({compatConfig:{MODE:3},name:"Steps",props:xe(),setup(e,t){let{slots:o}=t;const r=u(()=>Math.round(e.steps*((e.percent||0)/100))),s=u(()=>{var n;return(n=e.size)!==null&&n!==void 0?n:[e.size==="small"?2:14,e.strokeWidth||8]}),p=u(()=>W(s.value,"step",{steps:e.steps,strokeWidth:e.strokeWidth||8})),l=u(()=>{const{steps:n,strokeColor:i,trailColor:m,prefixCls:d}=e,y=[];for(let v=0;v<n;v+=1){const a=Array.isArray(i)?i[v]:i,f={[`${d}-steps-item`]:!0,[`${d}-steps-item-active`]:v<=r.value-1};y.push(g("div",{key:v,class:f,style:{backgroundColor:v<=r.value-1?a:m,width:`${p.value.width/n}px`,height:`${p.value.height}px`}},null))}return y});return()=>{var n;return g("div",{class:`${e.prefixCls}-steps-outer`},[l.value,(n=o.default)===null||n===void 0?void 0:n.call(o)])}}}),we=new Q("antProgressActive",{"0%":{transform:"translateX(-100%) scaleX(0)",opacity:.1},"20%":{transform:"translateX(-100%) scaleX(0)",opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}}),Oe=e=>{const{componentCls:t,iconCls:o}=e;return{[t]:x(x({},J(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize,marginInlineEnd:e.marginXS,marginBottom:e.marginXS},[`${t}-outer`]:{display:"inline-block",width:"100%"},[`&${t}-show-info`]:{[`${t}-outer`]:{marginInlineEnd:`calc(-2em - ${e.marginXS}px)`,paddingInlineEnd:`calc(2em + ${e.paddingXS}px)`}},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",overflow:"hidden",verticalAlign:"middle",backgroundColor:e.progressRemainingColor,borderRadius:e.progressLineRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorInfo}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",backgroundColor:e.colorInfo,borderRadius:e.progressLineRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",width:"2em",marginInlineStart:e.marginXS,color:e.progressInfoTextColor,lineHeight:1,whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[o]:{fontSize:e.fontSize}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.progressLineRadius,opacity:0,animationName:we,animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},Ie=e=>{const{componentCls:t,iconCls:o}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.progressRemainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.colorText,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[o]:{fontSize:`${e.fontSize/e.fontSizeSM}em`}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},De=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.progressRemainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.colorInfo}}}}}},je=e=>{const{componentCls:t,iconCls:o}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${o}`]:{fontSize:e.fontSizeSM}}}},We=z("Progress",e=>{const t=e.marginXXS/2,o=q(e,{progressLineRadius:100,progressInfoTextColor:e.colorText,progressDefaultColor:e.colorInfo,progressRemainingColor:e.colorFillSecondary,progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[Oe(o),Ie(o),De(o),je(o)]});var Ae=function(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(o[r[s]]=e[r[s]]);return o};const _e=I({compatConfig:{MODE:3},name:"AProgress",inheritAttrs:!1,props:E(j(),{type:"line",percent:0,showInfo:!0,trailColor:null,size:"default",strokeLinecap:"round"}),slots:Object,setup(e,t){let{slots:o,attrs:r}=t;const{prefixCls:s,direction:p}=U("progress",e),[l,n]=We(s),i=u(()=>Array.isArray(e.strokeColor)?e.strokeColor[0]:e.strokeColor),m=u(()=>{const{percent:f=0}=e,c=D(e);return parseInt(c!==void 0?c.toString():f.toString(),10)}),d=u(()=>{const{status:f}=e;return!ie.includes(f)&&m.value>=100?"success":f||"normal"}),y=u(()=>{const{type:f,showInfo:c,size:b}=e,C=s.value;return{[C]:!0,[`${C}-inline-circle`]:f==="circle"&&W(b,"circle").width<=20,[`${C}-${f==="dashboard"&&"circle"||f}`]:!0,[`${C}-status-${d.value}`]:!0,[`${C}-show-info`]:c,[`${C}-${b}`]:b,[`${C}-rtl`]:p.value==="rtl",[n.value]:!0}}),v=u(()=>typeof e.strokeColor=="string"||Array.isArray(e.strokeColor)?e.strokeColor:void 0),a=()=>{const{showInfo:f,format:c,type:b,percent:C,title:S}=e,k=D(e);if(!f)return null;let h;const O=c||(o==null?void 0:o.format)||(G=>`${G}%`),L=b==="line";return c||o!=null&&o.format||d.value!=="exception"&&d.value!=="success"?h=O(P(C),P(k)):d.value==="exception"?h=L?g(Z,null,null):g(ee,null,null):d.value==="success"&&(h=L?g(te,null,null):g(oe,null,null)),g("span",{class:`${s.value}-text`,title:S===void 0&&typeof h=="string"?h:void 0},[h])};return()=>{const{type:f,steps:c,title:b}=e,{class:C}=r,S=Ae(r,["class"]),k=a();let h;return f==="line"?h=c?g(Pe,$($({},e),{},{strokeColor:v.value,prefixCls:s.value,steps:c}),{default:()=>[k]}):g(pe,$($({},e),{},{strokeColor:i.value,prefixCls:s.value,direction:p.value}),{default:()=>[k]}):(f==="circle"||f==="dashboard")&&(h=g(ke,$($({},e),{},{prefixCls:s.value,strokeColor:i.value,progressStatus:d.value}),{default:()=>[k]})),l(g("div",$($({role:"progressbar"},S),{},{class:[y.value,C],title:b}),[h]))}}}),Me=re(_e);export{Me as P};
