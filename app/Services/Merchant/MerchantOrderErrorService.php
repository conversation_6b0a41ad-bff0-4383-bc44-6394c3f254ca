<?php

namespace App\Services\Merchant;

use App\Models\Merchant\MerchantOrderError;
use App\Services\System\AdminUserService;
use App\Services\TelegramService;
use App\Traits\QueryUtils;
use App\Utils\E;
use App\Utils\MyLog;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Slowlyo\OwlAdmin\Services\AdminService;

class MerchantOrderErrorService extends AdminService
{
    protected string $modelName = MerchantOrderError::class;

    use QueryUtils;

    /**
     * 添加关联关系
     *
     * 预留钩子, 方便处理只需要添加 [关联] 的情况
     *
     * @param        $query
     * @param string $scene 场景: list, detail, edit
     *
     * @return void
     */
    public function addRelations($query, string $scene = 'list'): void
    {
        $query->with(['category']);
    }
    /**
     * 排序
     *
     * @param Builder $query
     *
     * @return void
     */
    public function sortable($query)
    {
        if (request()->orderBy && request()->orderDir) {
            $query->orderBy(request()->orderBy, request()->orderDir ?? 'asc');
        } else {
            $query->orderByDesc($this->primaryKey());
        }
    }

    /**
     * 搜索
     *
     * @param Builder $query
     *
     * @return void
     */
    public function searchable($query)
    {
        $this->selectLike($query, request()->query());
        $this->selectDateRange($query, 'dateRange', request()->query());

        $merchant_id = $this->request->input('merchant_id');
        $merchantIds = AdminUserService::make()->getShowMerchantIds();
        if ($merchant_id) {
            $query->when(E::emptyNull($merchant_id), fn($query) => $query->where('merchant_id', $merchant_id));
        } else {
            $query->when($merchantIds, fn(Builder $query) => $query->whereIn('merchant_id', $merchantIds));
        }
        $category_id = $this->request->input('category_id');
        $query->when($category_id, fn(Builder $query) => $query->where('category_id', $category_id));
    }

    /**
     * @param       $orderSn
     * @param int   $mId
     * @param null  $mSn
     * @param array $ids
     * @param null  $mParams
     * @param null  $mResult
     * @param null  $rParams
     * @param null  $rResult
     * @param bool  $del
     * @return bool
     */
    public function logOrderUpdate($orderSn, int $mId = 0, $mSn = null, array $ids=[], $mParams = null, $mResult = null, $rParams = null, $rResult = null, $del = false): bool
    {
        if (!$orderSn) return false;
        if ($del) {
            return MerchantOrderError::query()->where('order_sn', $orderSn)->delete();
        }
        $logModel = MerchantOrderError::query()
            ->firstOrCreate(
                ['order_sn' => $orderSn],
                [
                    'order_sn'    => $orderSn,
                    'merchant_id' => $mId,
                    'merchant_sn' => $mSn,
                    'mt_params'   => null,
                    'mt_result'   => null,
                    'req_params'  => null,
                    'req_result'  => null,
                ]
            );
        if ($mParams) {
            $logModel->mt_params = $mParams;
        }
        if ($mResult) {
            $logModel->mt_result = $mResult;
        }
        if ($rParams) {
            $logModel->req_params = $rParams;
        }
        if ($rResult) {
            $logModel->req_result = $rResult;
        }
        if($category_id = Arr::get($ids, 'category_id',0)){
            $logModel->category_id  = $category_id;
        }
        if($broker_id = Arr::get($ids, 'broker_id',0)){
            $logModel->broker_id  = $broker_id;
        }
        if($channel_id = Arr::get($ids, 'channel_id',0)){
            $logModel->channel_id  = $channel_id;
        }

        $result = $logModel->save();
        // TG订单异常通知群组发送消息
        TelegramService::make()->orderFailTgNotice($logModel);
        return $result;
    }
}
