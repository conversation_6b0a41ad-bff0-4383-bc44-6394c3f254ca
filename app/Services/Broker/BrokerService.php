<?php

namespace App\Services\Broker;

use App\Models\Broker\Broker;
use App\Models\Broker\BrokerBalance;
use App\Services\PublicService;
use App\Services\System\AdminUserService;
use App\Traits\QueryUtils;
use App\Utils\E;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Modules\Broker\Models\AdminRole;
use Modules\Broker\Models\AdminUser;
use Slowlyo\OwlAdmin\Services\AdminService;

class BrokerService extends AdminService
{
    protected string $modelName = Broker::class;

    use QueryUtils;


    /**
     * 添加关联关系
     *
     * 预留钩子, 方便处理只需要添加 [关联] 的情况
     *
     * @param Builder $query
     * @param string  $scene 场景: list, detail, edit
     *
     * @return void
     */
    public function addRelations($query, string $scene = 'list')
    {
        if ($scene == 'list') {
            $query->withCount(['merchant as merchant_num']);
        }
    }

    /**
     * 排序
     *
     * @param Builder $query
     *
     * @return void
     */
    public function sortable($query)
    {
        if (request()->orderBy && request()->orderDir) {
            $query->orderBy(request()->orderBy, request()->orderDir ?? 'asc');
        } else {
            $query->orderByDesc($this->primaryKey());
        }
    }

    /**
     * 搜索
     *
     * @param Builder $query
     *
     * @return void
     */
    public function searchable($query)
    {
        $this->selectLike($query, request()->query());

        $brokerId = $this->request->input('broker_id');
        $brokerIds = AdminUserService::make()->getShowBrokerIds();
        if ($brokerId) {
            $query->when(E::emptyNull($brokerId), fn($query) => $query->where('id', $brokerId));
        } else {
            $query->when($brokerIds, fn(Builder $query) => $query->whereIn('id', $brokerIds));
        }
    }


    /**
     * 修改
     *
     * @param $primaryKey
     * @param $data
     *
     * @return bool
     */
    public function update($primaryKey, $data)
    {
        DB::beginTransaction();
        try {
            $this->saving($data, $primaryKey);

            $model = $this->query()->whereKey($primaryKey)->first();

            $ip_white      = Arr::get($data, 'ip_white');
            $reloadWhiteIp = false;
            if ($model->ip_white != $ip_white) {
                $reloadWhiteIp = true;
            }
            foreach ($data as $k => $v) {
                if (!$this->hasColumn($k)) {
                    continue;
                }

                $model->setAttribute($k, $v);
            }

            $result = $model->save();

            if ($result) {
                $this->savedForm($model, $data, true);
            }
            // 白名单有变化时，重新加载IP白名单
            $reloadWhiteIp && PublicService::make()->reloadWhiteIp();

            DB::commit();

        } catch (\Throwable $e) {
            DB::rollBack();

            admin_abort($e->getMessage());
        }

        return $result;
    }

    /**
     * 新增
     *
     * @param $data
     *
     * @return bool
     */
    public function store($data)
    {
        DB::beginTransaction();
        try {
            $this->saving($data);

            $model = $this->getModel();

            foreach ($data as $k => $v) {
                if (!$this->hasColumn($k)) {
                    continue;
                }

                $model->setAttribute($k, $v);
            }

            $result = $model->save();

            if ($result) {
                $this->savedForm($model, $data);
            }

            // 有配置IP白名单时，重新加载IP白名单
            if ($model->ip_white) {
                PublicService::make()->reloadWhiteIp();
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();

            admin_abort($e->getMessage());
        }

        return $result;
    }


    /**
     * saved 钩子 (执行于新增/修改后)
     *
     * 可以通过 $isEdit 来判断是新增还是修改
     *
     * @param      $model
     * @param      $data
     * @param bool $isEdit
     *
     * @return void
     */
    public function savedForm($model, $data, bool $isEdit = false): void
    {
        $passwd    = Arr::get($data, 'password');
        $adminUser = AdminUser::query()->where(['broker_id' => $model->id, 'master' => 1])->first();
        if (!$adminUser) {
            $adminUser            = new AdminUser;
            $adminUser->broker_id = $model->id;
            $adminUser->username  = $model->username;
            $adminUser->name      = $model->name;
            $adminUser->enabled   = Arr::get($data, 'state', Broker::STATE_ENABLED);
            $adminUser->master    = 1;
        }
        if ($passwd) {
            $adminUser->password = $passwd;
            $adminUser->save();

            if (!$isEdit) {
                $adminUser->roles()->sync([['role_id' => 1, 'broker_id' => $model->id]]);
            }
        } else {
            $adminUser->save();
        }

        AdminUser::query()->where(['broker_id' => $model->id])->update([
            'broker_id' => $adminUser->broker_id,
            'enabled'   => Arr::get($data, 'state', Broker::STATE_ENABLED)
        ]);
    }


    /**
     * saving 钩子 (执行于新增/修改前)
     *
     * 可以通过判断 $primaryKey 是否存在来判断是新增还是修改
     *
     * @param        $data
     * @param string $primaryKey
     *
     * @return void
     * @throws \Exception
     */
    public function saving(&$data, $primaryKey = '')
    {
        if (!$primaryKey) {
            $username = Arr::get($data, 'username');
            if (AdminUser::query()->where('username', $username)->exists()) {
                throw new \Exception("账号已经存在");
            }
        }
        $passwd = Arr::get($data, 'password');
        if ($passwd) {
            Arr::set($data, 'password', Hash::make($passwd));
        }

        if (!$primaryKey) {
            Arr::set($data, 'code', E::generateAppKey(isNumber: true));
        }
    }


    /**
     * deleted 钩子 (执行于删除后)
     *
     * @param $ids
     *
     * @return void
     */
    public function deleted($ids)
    {
        $list = AdminUser::query()->whereIn('broker_id', explode(',', $ids))->get();
        foreach ($list as $item) {
            $item->roles()->where('broker_admin_roles.broker_id', $item->broker_id)->delete();
            $item->delete();
        }
        AdminRole::query()->whereIn('broker_id', explode(',', $ids))->delete();
    }

    /**
     * 操作商户余额
     * @param int       $brokerId           代理ID
     * @param array     $data               数据
     * @param float|int $money              金额
     * @param bool      $isBalance          余额或冻结余额
     * @param bool      $add                加或减
     * @param bool      $balanceFreezeTrans 是否同时加减余额或冻结余额
     * @param bool      $addUp              是否累加余额
     * @return bool
     */
    public function changeMoney(int $brokerId, array $data, float|int $money, bool $isBalance, bool $add, bool $balanceFreezeTrans=false, bool $addUp = false): bool
    {
        if ($money <= 0) return $this->setError('金额必须大于0');

        DB::beginTransaction();
        try {
            bcscale(4);
            $model                 = Broker::query()->whereKey($brokerId)->lockForUpdate()->firstOrFail();
            $modelLog              = new BrokerBalance();
            $modelLog->admin_id    = Arr::get(admin_user(), 'id', 0);
            $modelLog->broker_id   = $model->id;
            $modelLog->merchant_id = Arr::get($data, 'merchant_id', 0);
            $modelLog->order_sn    = Arr::get($data, 'order_sn', E::makeOrderSn('BR'));
            $modelLog->channel_sn  = Arr::get($data, 'channel_sn');
            $modelLog->merchant_sn = Arr::get($data, 'merchant_sn');
            $modelLog->type        = Arr::get($data, 'type', BrokerBalance::TYPE_SYSTEM);
            $modelLog->remark      = Arr::get($data, 'remark');
            $modelLog->amount      = Arr::get($data, 'amount',0);
            $modelLog->rate      = Arr::get($data, 'rate',0);

            if ($add) {
                // 余额操作
                if ($isBalance) {
                    if ($balanceFreezeTrans) {
                        // 减冻结余额
                        if ($model->freeze < $money) throw new \Exception('冻结余额不足');
                        $modelLog->freeze_before = $model->freeze;
                        $modelLog->freeze        = -$money;
                        $modelLog->freeze_after  = bcadd($modelLog->freeze_before, $modelLog->freeze);
                        $model->decrement('freeze', $money);
                    }
                    $modelLog->money_before = $model->balance;
                    $model->increment('balance', $money);
                    // 加累计余额
                    $addUp && $model->increment('money', $money);
                    $modelLog->money       = $money;
                    $modelLog->money_after = bcadd($modelLog->money_before, $modelLog->money);
                } else {
                    // 加冻结余额
                    if ($balanceFreezeTrans) {
                        // 减余额
                        if ($model->balance < $money) throw new \Exception('余额不足');
                        $modelLog->money_before = $model->balance;
                        $model->decrement('balance', $money);
                        $modelLog->money       = -$money;
                        $modelLog->money_after = bcadd($modelLog->money_before, $modelLog->money);
                    }
                    $modelLog->freeze_before = $model->freeze;
                    $model->increment('freeze', $money);
                    $modelLog->freeze       = $money;
                    $modelLog->freeze_after = bcadd($modelLog->freeze_before, $modelLog->freeze);
                }
            } else {
                if ($isBalance) {
                    // 减余额
                    if ($model->balance < $money) throw new \Exception('余额不足');
                    if ($balanceFreezeTrans) {
                        // 加冻结余额
                        $modelLog->freeze_before = $model->freeze;
                        $modelLog->freeze        = $money;
                        $modelLog->freeze_after  = bcadd($modelLog->freeze_before, $modelLog->freeze);
                        $model->increment('freeze', $money);
                    }
                    $modelLog->money_before = $model->balance;
                    $modelLog->money        = -$money;
                    $modelLog->money_after  = bcadd($modelLog->money_before, $modelLog->money);
                    $model->decrement('balance', $money);
                } else {
                    // 减冻结余额
                    if ($model->freeze < $money) throw new \Exception('冻结余额不足');

                    if ($balanceFreezeTrans) {
                        $modelLog->money_before = $model->balance;
                        $modelLog->money        = $money;
                        $modelLog->money_after  = bcadd($modelLog->money_before, $modelLog->money);
                        $model->increment('balance', $money);
                    }
                    $modelLog->freeze_before = $model->freeze;
                    $modelLog->freeze        = -$money;
                    $modelLog->freeze_after  = bcadd($modelLog->freeze_before, $modelLog->freeze);
                    $model->decrement('freeze', $money);
                }
            }
            $modelLog->save();


            DB::commit();
            return true;
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            return $this->setError('商户不存在');
        } catch (\Throwable $e) {
            DB::rollBack();
            return $this->setError($e->getMessage());
        }
    }
}
