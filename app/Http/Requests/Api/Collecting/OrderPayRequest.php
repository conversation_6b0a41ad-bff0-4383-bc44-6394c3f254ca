<?php

namespace App\Http\Requests\Api\Collecting;

use App\Http\Enum\ApiCode;
use App\Traits\HttpUtils;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class OrderPayRequest extends FormRequest
{
    use  HttpUtils;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount'            => 'required|gt:0|lte:100000',
            'uid'               => 'required',
            'order_sn'          => 'required|max:32',
            'channel_code'      => 'sometimes|required|exists:channel_payment,code',
            'channel_type_code' => 'sometimes|required|exists:channel_payment_type,code',
            'notify_url'        => 'sometimes|url:http,https',
            'return_url'        => 'sometimes|url:http,https',
            'extend'            => 'sometimes',
            'remark'            => 'sometimes',
        ];
    }

    public function messages()
    {
        return [
            'amount.required'            => '订单金额必须',
            'amount.gt'                  => '金额必须大于零',
            'amount.lte'                 => '金额必须小于等于10万',
            'uid.required'               => '用户标识必须',
            'channel_code.required'      => '渠道编码必须',
            'channel_code.exists'        => '渠道编码不正确',
            'channel_type_code.required' => '渠道类型编码必须',
            'channel_type_code.exists'   => '渠道类型编码不正确',
            'order_sn.required'          => '商户订单号必须',
            'order_sn.max'               => '商户订单号最大32位',
            'notify_url.url'             => '请输入有效的异常通知地址',
            'return_url.url'             => '请输入有效的同步跳转地址',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        // 自定义错误处理逻辑

        throw new ValidationException($validator, $this->fail(message: $validator->errors()->first(), code: ApiCode::PARAMS_ERROR));
    }
}
