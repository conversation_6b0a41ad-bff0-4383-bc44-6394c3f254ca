<?php

namespace App\Utils\Collecting;

use App\Models\Channel\ChannelPayment;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * @method static \App\Utils\Collecting\Payment\Upay upay(ChannelPayment $payment=null,array $config = [])
 */
class Payment
{

    private static array $instance = [];

    private function __construct() { }

    public static function __callStatic($name, $arguments)
    {
        if (Arr::get(self::$instance, $name) === null) {

            $classname = '\\App\\Utils\\Collecting\\Payment\\' . Str::studly($name);
            if (!class_exists($classname)) {
                throw new Exception("类[{$classname}]不存在");
            }
            $class = new $classname(...$arguments);

            if (!($class instanceof PaymentAbstract)) {
                throw new Exception('必须实现[PaymentAbstract]');
            }
            Arr::set(self::$instance, $name, $class);
        }
        return Arr::get(self::$instance, $name);
    }

    // 禁止克隆

    public function __wakeup() { }

    // 禁止反序列化

    private function __clone() { }
}
