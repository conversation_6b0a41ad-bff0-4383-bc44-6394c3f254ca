<?php

namespace App\Utils\Collecting\Payment;

use App\Models\Merchant\MerchantOrder;
use App\Utils\Collecting\PaymentAbstract;
use App\Utils\E;
use App\Utils\MyLog;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class BaFangPay extends PaymentAbstract
{
    /**
     * 商户编号字段名称
     * @var string
     */
    protected string $merchantField = 'pay_memberid';


    /**
     * 渠道编码字段名称
     * @var string
     */
    protected string $channelCodeField = 'pay_bankcode';


    /**
     * 签名字段名称
     * 如不一致才保留，否则建议删除
     * @var string
     */
    protected string $signField = 'pay_md5sign';

    /**
     * 拉单接口
     * @var string
     */
    protected string $createUrl = '/Pay_Index.html';

    /**
     * 查单接口
     * @var string
     */
    protected string $queryUrl = '/Pay_Trade_query.html';

    protected string $requestType = 'form';

    /**
     * 创建订单
     *
     * @param array $data
     *
     * @return array
     * @throws \Throwable
     */
    public function pay(array $data): array
    {

        $orderSn = Arr::get($data, 'order_sn');
        $result  = $this->getNotifyAndReturnUrl($orderSn);
        $params  = [
            'pay_orderid'     => $orderSn,
            'pay_amount'      => (float)Arr::get($data, 'amount'),
            'pay_applydate'   => now()->toDateTimeString(),
            'pay_notifyurl'   => Arr::get($result, 'notify_url'),
            'pay_callbackurl' => Arr::get($result, 'return_url'),
            // 'pay_productname' => '手机 iPhone 16 Pro Max',
            // 'pay_ip'          => E::getClientIp()
        ];

        $response = $this->request(url: $this->createUrl, data: $params);

        if (Arr::get($response, 'status') != 1) {
            throw new Exception(Arr::get($response, 'msg'));
        }
        $jumpUrl = Arr::get($response, 'h5_url');
        if (!$jumpUrl) {
            throw new Exception('下单成功、支付链接为空');
        }
        return [
            'channel_sn' => Arr::get($response, 'mch_order_id'),
            'amount'     => (float)Arr::get($data, 'amount', 0),
            'jump_url'   => $jumpUrl,
            'log'        => [
                'url'      => $this->baseUrl . $this->createUrl,
                'request'  => $params,
                'response' => $response
            ]
        ];
    }

    /**
     * 订单查询
     *
     * @param string $orderSn 订单编号
     *
     * @return array
     * @throws \Throwable
     */
    public function query(string $orderSn): array
    {
        $params = [
            'pay_orderid' => $orderSn,
        ];


        $response = $this->request(url: $this->queryUrl, data: $params, query: true);
        if (Arr::get($response, 'returncode') != '00') {
            throw new Exception(Arr::get($response, 'message'));
        }
        $status = Arr::get($response, 'trade_state');
        $state  = MerchantOrder::STATE_WAIL;
        if ($status == 'SUCCESS') {
            $state = MerchantOrder::STATE_PAYMENT;
        }
        $paymentAmount = Arr::get($response, 'amount', 0);
        return [
            'order_sn' => Arr::get($response, 'orderid'),
            'amount'   => (float)$paymentAmount,
            'state'    => $state,
            'log'      => [
                'url'      => "{$this->baseUrl}{$this->queryUrl}",
                'request'  => $params,
                'response' => $response
            ]
        ];
    }

    /**
     * 异步通知
     *
     * @param MerchantOrder $order
     * @return array
     * @throws Exception
     */
    public function notify(MerchantOrder $order): array
    {
        $data = request()->post();
        MyLog::make('apiNotify', '回调接收参数')->addLog("订单编号:{$order->order_sn}")->addLog($data)->save();

        // 验证回调IP白名单
        $this->verifyNotifyIpWhite();

        $signString = Arr::get($data, 'sign');
        $result     = $this->verifySignature($data, $signString, $this->getMerchantSecret());
        if (!$result) {
            throw new Exception('验证签名错误');
        }
        if (Arr::get($data, 'returncode') != '00') {
            throw new Exception('支付失败');
        }
        return [
            'order_sn' => Arr::get($data, 'orderid'),
            'amount'   => (float)Arr::get($data, 'amount', 0),
            'result'   => 'SUCCESS'
        ];
    }

    /**
     * 生成签名
     *
     *
     * @param array  $data
     * @param string $key
     * @param string $scene
     * @return string 签名字符串
     */
    public function signature(array $data, string $key, string $scene): string
    {
        Arr::forget($data, [$this->signField, 'sign']);
        $filteredParams = collect($data)->filter(fn($value) => E::emptyNull($value))->toArray();

        ksort($filteredParams);


        $stringToSign = '';
        foreach ($filteredParams as $field => $value) {
            $stringToSign .= $field . '=' . $value . '&';
        }
        // 拼接字符串
        $stringToSign .= 'key=' . $key;
        // $stringToSign = http_build_query($filteredParams).'&key=' . $key;
        MyLog::make('apiSignature', '签名字符')->addLog($stringToSign)->save();
        return Str::upper(md5($stringToSign));
    }


    public function getTgRateChange($text): array
    {
        // 正则表达式
        $pattern = '/^(\d+)\s+(.+?)\s+费率:([-\d.]+)%\s*限额：(.+)$/m';
        // 执行匹配
        preg_match_all($pattern, $text, $matches, PREG_SET_ORDER);

        $data = [];
        foreach ($matches as $match) {

            $item  = [
                'code'     => Arr::get($match, 1),
                'name'     => Arr::get($match, 2),
                'quantity' => Arr::get($match, 3),
            ];
            $limit = Arr::get($match, 4);
            if ($limit === '无') {
                continue;
            }
            if (strpos($limit, '-')) {
                list($min, $max) = explode('-', trim($limit));
                $item['price'] = [
                    'type' => 'between',
                    'min'  => intval(trim($min)),
                    'max'  => intval(trim($max)),
                ];
            } else {
                $item['price'] = [
                    'type' => 'fixed',
                    'list' => array_unique(array_filter(explode(',', trim($limit)))),
                ];
            }

            $data[] = $item;
        }
        return $data;
    }
}
