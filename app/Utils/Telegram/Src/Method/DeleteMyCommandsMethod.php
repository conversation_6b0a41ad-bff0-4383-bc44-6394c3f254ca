<?php

namespace App\Utils\Telegram\Src\Method;

use Illuminate\Support\Arr;
use TgBotApi\BotApiBase\Method\Interfaces\DeleteMethodAliasInterface;

class DeleteMyCommandsMethod implements DeleteMethodAliasInterface
{
    public string $scope;

    public static function create($type, $chatId = null, $userId = null): static
    {

        $instance = new static();
        $instance->setScope($type, $chatId, $userId);
        return $instance;
    }


    public function setScope($type, $chatId = null, $userId = null): static
    {
        $data = [
            'type' => $type
        ];
        if ($chatId) {
            Arr::set($data, 'chat_id', $chatId);
        }
        if ($userId) {
            Arr::set($data, 'user_id', $userId);
        }
        $this->scope = json_encode($data);
        return $this;
    }
}
