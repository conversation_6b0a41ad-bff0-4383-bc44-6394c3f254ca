<?php

namespace App\Utils\Telegram\Src\Actions\Channel;

use App\Models\Merchant\MerchantOrderTg;
use App\Utils\Telegram\Src\Base\ActionAbstract;
use App\Utils\Telegram\Telegram;
use Exception;

class Order extends ActionAbstract
{

    /**
     * 上游查单结果回复商户群组
     * @param string $orderSn       平台订单号
     * @param string $resultMessage 上游查单结果
     * @return bool
     */
    public function answer(string $orderSn, string $resultMessage): bool
    {
        try {
            $model = MerchantOrderTg::query()->where(['order_sn' => $orderSn])->orderByDesc('id')->first();
            if (!$model) {
                throw new Exception('没有找到商户的查单');
            }

            $model->result = $resultMessage;
            $model->save();
            $text = <<<RS
查询订单号：{$model->merchant_sn}
查询结果：{$resultMessage}
RS;
            // 商户群组
            Telegram::merchant()->sendTextMessage($text, chatId: $model->chat_id, messageId: $model->message_id);

            // 订单异常监控群组
            if ($model->sys_chat_id && $model->sys_message_id) {
                Telegram::merchant()->sendTextMessage($text, chatId: $model->sys_chat_id, messageId: $model->sys_message_id);
            }

            $this->sendTextMessage("✅ 已通知商户", messageId: $this->getMessageId());
            return true;
        } catch (Exception $e) {
            $this->sendTextMessage("🚫 {$e->getMessage()}", messageId: $this->getMessageId());
            return $this->setError($e->getMessage());
        }
    }

}
