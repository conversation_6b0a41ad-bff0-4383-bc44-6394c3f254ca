<?php

namespace App\Utils\Telegram\Src\Type;



class UpdateType extends \TgBotApi\BotApiBase\Type\UpdateType
{

    /**
     * Optional. New incoming message of any kind — text, photo, sticker, etc.
     *
     * @var MessageType|null
     */
    public $message;

    /**
     * Optional. New version of a message that is known to the bot and was edited.
     *
     * @var MessageType|null
     */
    public $editedMessage;

}
