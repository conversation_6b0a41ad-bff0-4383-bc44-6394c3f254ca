<?php

namespace App\Utils\Telegram;

use App\Utils\Telegram\Src\Base\TelegramAbstract;
use Exception;
use Illuminate\Support\Arr;

/**
 *
 * @method static \App\Utils\Telegram\Src\Merchant merchant(array $data = [], string $token = '')
 * @method static \App\Utils\Telegram\Src\Channel channel(array $data = [], string $token = '')
 */
class Telegram
{

    private static array $instance = [];

    private function __construct() { }

    public static function __callStatic($name, $arguments)
    {
        if (Arr::get(self::$instance, $name) === null) {

            $classname = '\\App\\Utils\\Telegram\\Src\\' . ucfirst($name);
            if (!class_exists($classname)) {
                throw new Exception("类[{$classname}]不存在");
            }
            $class = new $classname(...$arguments);

            if (!($class instanceof TelegramAbstract)) {
                throw new Exception('必须继承[TelegramAbstract]');
            }
            Arr::set(self::$instance, $name, $class);
        }
        return Arr::get(self::$instance, $name);
    }


    public function __wakeup() { }

    // 禁止反序列化

    private function __clone() { }
}
