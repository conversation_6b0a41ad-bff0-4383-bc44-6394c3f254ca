<?php

namespace App\Admin\Controllers\Report;

use App\Services\Report\ProfitService;
use App\Support\Forms;
use App\Traits\ExportButton;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Slowlyo\OwlAdmin\Controllers\AdminController;
use Slowlyo\OwlAdmin\Renderers\Form;
use Slowlyo\OwlAdmin\Renderers\Page;

class ProfitController extends AdminController
{
    use ExportButton;
    protected string $serviceName = ProfitService::class;

    public function list(): Page
    {
        $crud = $this->baseCRUD()
            ->name(__CLASS__)
            ->filterTogglable(false)
            ->affixHeader(true)
            ->headerToolbar([
                $this->exportAction(true),
                ...$this->baseHeaderToolBar()
            ])
            ->bulkActions([])
            ->filter(
                $this->baseFilter()
                    ->target('statistics_profit_daily')
                    ->primaryField('date')
                    ->actions([])
                    ->body([
                        Forms::make()->selectDateRange('created_at', '时间'),
                        amis()->Button()->label(__('admin.reset'))->actionType('clear-and-submit')->className('mr-2 px-5'),
                        amis()->Button()->label(__('admin.search'))->actionType('submit')->level('primary')->className('px-5'),
                        amis()->Service()
                            ->initFetch(false)
                            ->id('statistics_profit_daily')
                            ->schemaApi('report/profit/statistics_profit_daily?'. http_build_query([
                                    'created_atDateRangeField' =>  '$created_atDateRangeField',
                                    'created_atDateRangeValue' =>  '$created_atDateRangeValue',
                                ]))
                    ])
            )
            ->columns([
                amis()->TableColumn('date', '日期')->sortable(),
                amis()->TableColumn('total_payment_amount', '充值金额')->sortable(),
                amis()->TableColumn('total_preset_amount', '外放手续费')->sortable()->align('center'),
                amis()->TableColumn('total_channel_amount', '渠道手续费')->sortable(),
                amis()->TableColumn('total_broker_amount', '代理利润')->sortable()->align('center'),
                amis()->TableColumn('total_money', '平台利润')->sortable()->align('center'),
            ]);
        return $this->baseList($crud);
    }


    public function form($isEdit = false): Form
    {
        return $this->baseForm()->body([]);
    }

    public function detail(): Form
    {
        return $this->baseForm()->static()->body([]);
    }

    /**
     * 数据汇总
     * @return JsonResponse|JsonResource
     */
    public function statisticsProfitDaily(): JsonResponse|JsonResource
    {
        $data = ProfitService::make()->statisticsProfitDaily();
        return $this->response()->success([
            amis('tag')->label("总充值余额：{$data['total_payment']}")->className('my-1 px-5 py-4 max-w-none'),
            amis('tag')->label("总外放手续费：{$data['total_preset']}")->className('my-1 px-5 py-4 max-w-none'),
            amis('tag')->label("总渠道手续费：{$data['total_channel']}")->className('my-1 px-5 py-4 max-w-none'),
            amis('tag')->label("总代理利润：{$data['total_broker']}")->className('my-1 px-5 py-4 max-w-none'),
            amis('tag')->label("总平台利润：{$data['total_money']}")->className('my-1 px-5 py-4 max-w-none'),

        ]);
    }

    /**
     * @param $row
     *
     * @return mixed
     */
    protected function exportMap($row)
    {
        return [
            '日期' =>  Arr::get($row, 'date',''),
            '充值金额' =>  Arr::get($row, 'total_payment_amount',0),
            '外放手续费' =>  Arr::get($row, 'total_preset_amount',0),
            '渠道手续费' =>  Arr::get($row, 'total_channel_amount',0),
            '代理利润' =>  Arr::get($row, 'total_broker_amount',0),
            '平台利润' =>  Arr::get($row, 'total_money',0),
        ];
    }
}
