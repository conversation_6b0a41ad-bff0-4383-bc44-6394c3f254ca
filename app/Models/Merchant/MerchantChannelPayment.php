<?php

namespace App\Models\Merchant;

use App\Models\Broker\Broker;
use App\Models\Channel\ChannelPayment;
use App\Traits\SerializeDate;
use Illuminate\Database\Eloquent\Model;

class MerchantChannelPayment extends Model
{
    use SerializeDate;
    /**
     * 商户状态：禁用
     */
    const STATE_DISABLED = 0;

    /**
     * 商户状态：启用
     */
    const STATE_ENABLED = 1;
    /**
     * 商户状态
     */
    const STATE = [
        ['label' => '关闭', 'value' => self::STATE_DISABLED, 'icon' => 'fail'],
        ['label' => '开启', 'value' => self::STATE_ENABLED, 'icon' => 'success'],
    ];
    protected $table = 'merchant_channel_payment';


    public function broker()
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function channel()
    {
        return $this->belongsTo(ChannelPayment::class, 'channel_id', 'id');
    }

    public function merchant()
    {
        return $this->belongsTo(Merchant::class, 'merchant_id', 'id');
    }
}
