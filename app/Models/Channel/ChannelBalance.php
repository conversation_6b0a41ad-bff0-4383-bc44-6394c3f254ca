<?php

namespace App\Models\Channel;

use App\Models\Broker\Broker;
use App\Models\Merchant\Merchant;
use App\Traits\SerializeDate;
use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed $broker_id
 */
class ChannelBalance extends Model
{
    use SerializeDate;
    public const UPDATED_AT = null;

    /**
     * 变动类型：后台操作
     */
    const TYPE_SYSTEM = 1;

    /**
     * 变动类型：渠道充值
     */
    const TYPE_RECHARGE = 2;

    /**
     * 变动类型：渠道费率
     */
    const TYPE_RATE = 3;

    /**
     * 变动类型
     */
    const TYPE = [
        ['label' => '后台操作', 'value' => self::TYPE_SYSTEM, 'icon' => 'fail'],
        ['label' => '渠道充值', 'value' => self::TYPE_RECHARGE, 'icon' => 'success'],
        ['label' => '渠道费率', 'value' => self::TYPE_RATE, 'icon' => 'warning'],
    ];

    protected $table = 'channel_balances';

    public function broker()
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }


    public function channel()
    {
        return $this->hasOne(ChannelPayment::class, 'id', 'channel_id');
    }


    public function category()
    {
        return $this->hasOne(ChannelCategory::class, 'id', 'category_id');
    }


    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'id', 'merchant_id');
    }
}
