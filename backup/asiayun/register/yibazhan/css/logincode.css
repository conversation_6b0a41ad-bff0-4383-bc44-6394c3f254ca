@charset "UTF-8";
.L_code {
  display: flex;
  justify-content: space-between;
  height: 100vh;
  overflow: hidden;
  margin: 0;
}

.L_code-left-side {
  max-width: 35%;
  min-width: 30%;
  overflow-y: hidden;
  width: 520px;
  position: relative;
}
.L_code-left-side::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 100%;
  background-image: var(--loginIllustration);
  background-size: cover;
  background-position: center top;
  background-repeat: no-repeat;
  z-index: 1;
}
.L_code-left-side .L_code-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px 0 0 40px;
  text-align: center;
  position: relative;
  z-index: 2;
  height: 100vh;
  overflow: hidden;
}
.L_code-left-side .L_code-content .L_code-logo {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
}

.L_code-right-side {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  background-image: var(--loginbg);
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100vh;
  overflow: hidden;
}
.L_code-right-side .L_code-right-form {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  background: #fff;
  padding: 40px 0;
  width: 720px;
  box-shadow: 0 6px 32px 0 rgba(15, 32, 71, 0.08);
  position: relative;
  z-index: 2;
  top: -24px;
}
.L_code-right-side .L_code-right-form .L_code-scan-login {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
  width: 280px;
  min-width: 280px;
  height: auto;
  border-right: 1px solid #f0f0f0;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer {
  width: 188px;
  height: 188px;
  position: relative;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner {
  position: absolute;
  top: -38px;
  border-radius: 2px;
  box-shadow: rgba(17, 17, 26, 0.1) 0px 0px 16px;
  white-space: nowrap;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner .codetip-arrow {
  border-color: transparent #fff #fff transparent;
  bottom: -2.8px;
  left: 5px;
  box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
  background: 0 0;
  border-style: solid;
  border-width: 4.24264069px;
  display: block;
  height: 8.48528137px;
  position: absolute;
  transform: rotate(45deg);
  width: 8.48528137px;
  z-index: 2;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner .codetip-title {
  display: flex;
  align-items: stretch;
  gap: 3px;
  border-bottom: 1px solid #e8e8e8;
  margin: 0;
  min-height: 32px;
  padding: 5px 16px 4px;
  position: relative;
  z-index: 3;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner .codetip-title .codetip-icon {
  width: 18px;
  height: 18px;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner .codetip-title .codetip-icon img {
  width: 100%;
  height: 100%;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner .codetip-title p, .L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .codetip-inner .codetip-title span {
  font-size: 14px;
  font-weight: 500;
  color: #000;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer #L_qrCode {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: relative;
  background: url(../../yibazhan/images/https://ossjm.oss-cn-hangzhou.aliyuncs.com/pub/img/user/reg_code_wrap.png) no-repeat;
  background-size: contain;
  padding: 8px;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .modality_code {
  position: absolute;
  background-color: hsla(0, 0%, 100%, 0.9);
  border-radius: 8px;
  inset: 0;
  z-index: 1;
  opacity: 1;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer #regenerateQrCode {
  opacity: 1;
  position: absolute;
  bottom: 45%;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  cursor: pointer;
  z-index: 2;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer #regenerateQrCode p {
  color: #1a1a1a;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .qrCodeContainer .loading-spinner {
  position: absolute;
  top: 45%;
  left: 40%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 6px solid rgba(0, 0, 0, 0.1);
  z-index: 2;
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s infinite linear;
  display: none;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .wechat-login {
  text-align: center;
  display: inline-flex;
}
.L_code-right-side .L_code-right-form .L_code-scan-login .wechat-login a {
  margin-left: 5px;
  color: var(--primary);
  cursor: pointer;
}
.L_code-right-side .L_code-right-form .L_code-login-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  padding: 0 40px 0 28px;
  overflow: hidden;
  overflow-y: auto;
}
.L_code-right-side .L_code-right-form .L_code-login-form .L_code-form-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  white-space: nowrap;
  width: 100%;
  padding-bottom: 5px;
  margin-bottom: 12px;
  position: relative;
  border-bottom: 3px solid #f6f6f6;
}
.L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab {
  padding-right: 10px;
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
}
.L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab .login-tab-separate {
  background: #d9d9d9;
  height: 16px;
  margin: 0 16px;
  width: 1px;
}
.L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab .login-tab-item {
  position: relative;
  font-size: 20px;
  font-weight: 900;
  color: #1a1a1a;
  cursor: pointer;
  transition: background 0.3s ease;
  -webkit-font-smoothing: initial;
}
.L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab .login-tab-item:last-child {
  margin-right: 0;
}
.L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab .login-tab-item.active {
  color: var(--primary) !important;
}
.L_code-right-side .L_code-right-form .L_code-login-form .transitionSwitching {
  position: absolute;
  bottom: -3px;
  height: 3px;
  background-color: var(--primary);
  transition: all 0.3s ease;
}
.L_code-right-side .L_code-right-form .L_code-login-form .ExtremeTest_captcha {
  height: 40px;
}
.L_code-right-side .L_code-right-form .L_code-login-form .tab-pane {
  display: none;
}
.L_code-right-side .L_code-right-form .L_code-login-form .tab-pane.active {
  display: block;
  animation: fadeIn 0.5s;
}
.L_code-right-side.no_wx .L_code-right-form {
  width: 476px;
  padding: 28px 0 40px;
  border-radius: 15px 0 15px 0;
}
.L_code-right-side.no_wx .L_code-right-form .L_code-scan-login {
  display: none;
}
.L_code-right-side.no_wx .L_code-right-form .L_code-login-form {
  padding: 0 30px 0 40px;
}
.L_code-right-side.no_wx .L_code-right-form .L_code-login-form .code_login-tab .login-tab-item {
  font-size: 18px;
  color: #8d94a1;
}
.L_code-right-side.no_wx .L_code-right-form .Oauth_login {
  gap: 15px;
}
.L_code-right-side.no_wx .L_code-right-form .Oauth_login .other-login {
  padding: 0;
}
.L_code-right-side.no_wx .L_code-right-form .Oauth_login .list-inline {
  padding: 0;
  gap: 10px;
  justify-content: flex-start;
}
.L_code-right-side.right .L_code-right-form {
  flex-direction: row-reverse;
}
.L_code-right-side.right .L_code-right-form .L_code-scan-login {
  border-left: 1px solid #f0f0f0;
  border-right: none;
}
.L_code-right-side.wx.right .L_code-login-form {
  padding: 0 28px 0 40px;
}
.L_code-right-side .form-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.L_code-right-side .form-group label {
  color: #505363;
  font-weight: 300;
  font-size: 14px;
}
.L_code-right-side .form-group .custom-dropdown {
  position: relative;
  width: fit-content;
}
.L_code-right-side .form-group .custom-dropdown-selection {
  position: relative;
  padding: 0 21px 0 11px;
  color: #505363;
  font-weight: normal;
  font-size: 14px;
  background-color: #fff;
  border: 1px solid #d4d9e2;
  border-right: none;
  border-radius: 0;
  transition: all 0.3s;
  line-height: 38px;
  text-align: left;
  width: 108px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.L_code-right-side .form-group .custom-dropdown-selection::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #adb5bd;
}
.L_code-right-side .form-group .custom-dropdown-list {
  display: none;
  position: absolute;
  width: max-content;
  padding: 0;
  list-style: none;
  border: 1px solid #ccc;
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
  background-color: #fff;
  z-index: 1;
}
.L_code-right-side .form-group .custom-dropdown-list li {
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.2s; /* 平滑背景颜色过渡 */
}
.L_code-right-side .form-group .custom-dropdown-list li:hover {
  background-color: #f0f0f0;
}
.L_code-right-side .mt-3 .btn-primary {
  height: 48px;
  font-weight: 500;
  transition: background-color 0.3s;
}
.L_code-right-side .mt-3 .btn-primary:hover {
  background-color: var(--primary-hover);
}
.L_code-right-side .Oauth_login {
  width: 100%;
  position: relative;
  display: flex;
  align-items: stretch;
  flex-direction: column;
  gap: 20px;
}
.L_code-right-side .Oauth_login .other-login {
  padding: 0 20px;
  width: 100%;
  color: #8f96a3;
  font-size: 12px;
  line-height: 18px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
.L_code-right-side .Oauth_login .other-login .gray-text {
  font-size: 14px;
  font-weight: 300;
  white-space: nowrap;
  padding: 0 4px;
}
.L_code-right-side .Oauth_login .other-login .gray-line {
  width: 100%;
  color: #8f96a3;
  font-size: 12px;
  line-height: 18px;
  text-align: center;
  height: 1px;
  background-color: rgba(143, 150, 163, 0.1);
}
.L_code-right-side .Oauth_login .list-inline {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  padding: 0 20px;
}
.L_code-right-side .Oauth_login .list-inline .social-list-item {
  height: 32px;
  width: 32px;
  background-image: none;
  border: none;
}
.L_code-right-side .Oauth_login .list-inline .social-list-item .icon {
  width: 100%;
  height: 100%;
}
.L_code-right-side .L_code-right-footer {
  position: absolute;
  left: 40px;
  bottom: 10px;
  display: flex;
  flex: 1;
  justify-content: flex-start;
  text-align: center;
  flex-wrap: wrap;
  gap: 0 8px;
}
.L_code-right-side .L_code-right-footer a {
  color: #999;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  font-family: auto;
}
.L_code-right-side .L_code-right-footer a:hover {
  color: #666;
}
.L_code-right-side .L_code-right-footer a.L_code_clause {
  color: var(--primary);
  cursor: pointer;
}

@keyframes codespin {
  to {
    transform: rotate(360deg);
  }
}
.form-control {
  padding: 7px 10px;
  line-height: 24px;
  height: fit-content;
  background-color: #fff;
  border: 1px solid #d4d9e2;
  border-radius: 0;
}

.btn {
  padding: 7px 10px;
  line-height: 26px;
  height: fit-content;
  background-color: var(--primary);
  border: none;
  border-radius: 0;
}

.btn-outline-light {
  padding: 7px 10px;
  line-height: 26px;
  height: fit-content;
  background-color: #fff;
  border: 1px solid #d4d9e2;
  border-radius: 0;
  color: var(--primary);
}

.input-group-append {
  height: 40px;
}
.input-group-append img {
  border: 1px solid #d4d9e2 !important;
  border-left: none;
}

.serviceTerms {
  margin: 14px 0 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}
.serviceTerms span {
  color: #9ea5b2;
}
.serviceTerms .custom-checkbox {
  width: 16px;
  height: 16px;
  background-color: #fff;
  border: 1.5px solid #9ea5b2;
  cursor: pointer;
  position: relative;
}
.serviceTerms .custom-checkbox:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 6px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  opacity: 0;
  transition: all 0.3s;
}
.serviceTerms .custom-checkbox.checked {
  background-color: var(--primary);
  border: 1.5px solid var(--primary);
}
.serviceTerms .custom-checkbox.checked:after {
  opacity: 1;
}

@keyframes antCheckboxEffect {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.5);
}

@media (max-width: 768px) {
  .L_code {
    display: block;
    height: auto;
    overflow: hidden;
    margin: 0;
  }
  .L_code-left-side {
    max-width: 100%;
    min-width: 100%;
    position: fixed;
    top: 0;
    height: 60px;
    z-index: 99;
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .L_code-left-side .GoBackToTheHomepage {
    color: rgb(153, 153, 153);
    font-size: 16px;
  }
  .L_code-left-side::before {
    display: none;
  }
  .L_code-left-side .L_code-content {
    height: 60px;
    padding: 0 20px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .L_code-right-side {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    background-image: var(--loginbg);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    height: auto;
    overflow: hidden;
    padding: 60px 0 0;
    min-height: 100vh;
  }
  .L_code-right-side .L_code-right-form {
    display: flex;
    flex-direction: column-reverse !important;
    justify-content: space-between;
    align-items: stretch;
    background: #fff;
    padding: 38px 22px 78px !important;
    width: 100%;
    box-shadow: 0 6px 32px 0 rgba(15, 32, 71, 0.08);
    position: unset;
    gap: 40px;
  }
  .L_code-right-side .L_code-right-form .L_code-scan-login {
    width: 100%;
    min-width: 100%;
    border-right: none !important;
    border-left: none !important;
  }
  .L_code-right-side.no_wx .L_code-right-form {
    width: 100%;
    padding: 0;
    border-radius: 0;
  }
  .L_code-right-side.no_wx .L_code-right-form .L_code-scan-login {
    display: none;
  }
  .L_code-right-content {
    width: 100%;
  }
  .text-register {
    position: relative;
    width: 100%;
    text-align: center;
    color: #505363;
    display: flex;
    justify-content: flex-end;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form {
    padding: 0 !important;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab .login-tab-separate {
    margin: 0 12px;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .code_login-tab .login-tab-item {
    font-size: 18px;
    color: rgb(141, 148, 161);
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .form-group {
    width: 100%;
    margin-bottom: 20px;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .form-group label {
    font-size: 16px;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .form-group .custom-dropdown {
    width: 100%;
    margin-bottom: 12px;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .form-group .custom-dropdown-selection {
    width: 100%;
    border-right: 1px solid rgb(212, 217, 226);
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .form-group .custom-dropdown-list {
    width: 100%;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .mt-3 .btn-primary {
    width: 100%;
    font-size: 16px;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .Oauth_login {
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  .L_code-right-side .L_code-right-form .L_code-login-form .Oauth_login .Oauth_login_txt {
    margin-right: 0;
  }
  .L_code-right-side .L_code-right-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 99;
    height: 40px;
    display: flex;
    background-color: rgb(240, 240, 240);
    flex: 1;
    align-items: center;
    justify-content: flex-start;
    padding: 0 20px;
  }
  .L_code-right-side .L_code-right-footer a {
    display: none;
  }
  .L_code-right-side .L_code-right-footer a.L_code_clause {
    display: block;
    color: rgb(153, 153, 153) !important;
  }
  .L_code-right-side .L_code-right-footer a.L_code_clause {
    color: var(--primary);
    cursor: pointer;
  }
}

/*# sourceMappingURL=logincode.css.map */
