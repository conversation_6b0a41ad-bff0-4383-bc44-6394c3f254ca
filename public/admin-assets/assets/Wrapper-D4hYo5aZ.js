import{t as p,l as i,b6 as u,D as y,E as f}from"./index-CEmnTf-r.js";var m=function(a){p(r,a);function r(){return a!==null&&a.apply(this,arguments)||this}return r.prototype.renderBody=function(){var e=this.props,t=e.children,n=e.body,s=e.render,d=e.disabled;return t?typeof t=="function"?t(this.props):t:n?s("body",n,{disabled:d}):null},r.prototype.render=function(){var e=this.props,t=e.className,n=e.size,s=e.classnames,d=e.style,o=e.data,l=e.wrap,c=e.id;return l===!1?this.renderBody():i.createElement("div",{className:s("Wrapper",n&&n!=="none"?"Wrapper--".concat(n):"",t),style:u(d,o),"data-id":c},this.renderBody())},r.propsList=["body","className","children","size"],r.defaultProps={className:"",size:"md"},r}(i.Component),b=function(a){p(r,a);function r(){return a!==null&&a.apply(this,arguments)||this}return r=y([f({type:"wrapper"})],r),r}(m);export{b as WrapperRenderer,m as default};
