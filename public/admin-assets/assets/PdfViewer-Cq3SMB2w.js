var op=Object.defineProperty;var pd=h=>{throw TypeError(h)};var lp=(h,t,e)=>t in h?op(h,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):h[t]=e;var V=(h,t,e)=>lp(h,typeof t!="symbol"?t+"":t,e),Th=(h,t,e)=>t.has(h)||pd("Cannot "+e);var r=(h,t,e)=>(Th(h,t,"read from private field"),e?e.call(h):t.get(h)),A=(h,t,e)=>t.has(h)?pd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(h):t.set(h,e),g=(h,t,e,s)=>(Th(h,t,"write to private field"),s?s.call(h,e):t.set(h,e),e),m=(h,t,e)=>(Th(h,t,"access private method"),e);var pe=(h,t,e,s)=>({set _(i){g(h,t,i,e)},get _(){return r(h,t,s)}});import{_ as ol,r as N,j as _t,eo as ht,ep as Wt,eq as dh,n as hp,er as cp,bN as dp,aK as Qr,l as ft,P as up,M as ll,bo as fp}from"./index-CEmnTf-r.js";var ma={};ma.d=(h,t)=>{for(var e in t)ma.o(t,e)&&!ma.o(h,e)&&Object.defineProperty(h,e,{enumerable:!0,get:t[e]})};ma.o=(h,t)=>Object.prototype.hasOwnProperty.call(h,t);var z=globalThis.pdfjsLib={};ma.d(z,{AbortException:()=>Bn,AnnotationEditorLayer:()=>Vc,AnnotationEditorParamsType:()=>G,AnnotationEditorType:()=>Q,AnnotationEditorUIManager:()=>In,AnnotationLayer:()=>dm,AnnotationMode:()=>ii,CMapCompressionType:()=>Fh,ColorPicker:()=>Gl,DOMSVGFactory:()=>sd,DrawLayer:()=>Yc,FeatureTest:()=>he,GlobalWorkerOptions:()=>Ws,ImageKind:()=>pl,InvalidPDFException:()=>Vd,MissingPDFException:()=>Hn,OPS:()=>Oe,Outliner:()=>Cc,PDFDataRangeTransport:()=>Nu,PDFDateString:()=>Jd,PDFWorker:()=>Jn,PasswordResponses:()=>bp,PermissionFlag:()=>mp,PixelsPerInch:()=>xi,RenderingCancelledException:()=>id,TextLayer:()=>$l,UnexpectedResponseException:()=>gh,Util:()=>F,VerbosityLevel:()=>uh,XfaLayer:()=>Bu,build:()=>Vg,createValidAbsoluteUrl:()=>_p,fetchData:()=>_h,getDocument:()=>Og,getFilenameFromUrl:()=>kp,getPdfFilenameFromUrl:()=>Mp,getXfaPageViewport:()=>Ip,isDataScheme:()=>nd,isPdfFile:()=>rd,noContextMenu:()=>de,normalizeUnicode:()=>Tp,renderTextLayer:()=>Tg,setLayerDimensions:()=>Mn,shadow:()=>J,updateTextLayer:()=>Rg,version:()=>Gg});const qt=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser"),Gd=[1,0,0,1,0,0],Dh=[.001,0,0,.001,0,0],pp=1e7,Rh=1.35,Le={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256},ii={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},gp="pdfjs_internal_editor_",Q={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},G={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35},mp={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},zt={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},pl={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},xt={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},Jr={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},uh={ERRORS:0,WARNINGS:1,INFOS:5},Fh={NONE:0,BINARY:1},Oe={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},bp={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let fh=uh.WARNINGS;function Ap(h){Number.isInteger(h)&&(fh=h)}function vp(){return fh}function ph(h){fh>=uh.INFOS&&console.log(`Info: ${h}`)}function q(h){fh>=uh.WARNINGS&&console.log(`Warning: ${h}`)}function at(h){throw new Error(h)}function Lt(h,t){h||at(t)}function yp(h){switch(h==null?void 0:h.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function _p(h,t=null,e=null){if(!h)return null;try{if(e&&typeof h=="string"){if(e.addDefaultProtocol&&h.startsWith("www.")){const i=h.match(/\./g);(i==null?void 0:i.length)>=2&&(h=`http://${h}`)}if(e.tryConvertEncoding)try{h=Cp(h)}catch{}}const s=t?new URL(h,t):new URL(h);if(yp(s))return s}catch{}return null}function J(h,t,e,s=!1){return Object.defineProperty(h,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const Ri=function(){function t(e,s){this.constructor===t&&at("Cannot initialize BaseException."),this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();class Oh extends Ri{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Nh extends Ri{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Vd extends Ri{constructor(t){super(t,"InvalidPDFException")}}class Hn extends Ri{constructor(t){super(t,"MissingPDFException")}}class gh extends Ri{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}}class Ep extends Ri{constructor(t){super(t,"FormatError")}}class Bn extends Ri{constructor(t){super(t,"AbortException")}}function Wd(h){(typeof h!="object"||(h==null?void 0:h.length)===void 0)&&at("Invalid argument for bytesToString");const t=h.length,e=8192;if(t<e)return String.fromCharCode.apply(null,h);const s=[];for(let i=0;i<t;i+=e){const n=Math.min(i+e,t),a=h.subarray(i,n);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function mh(h){typeof h!="string"&&at("Invalid argument for stringToBytes");const t=h.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=h.charCodeAt(s)&255;return e}function wp(h){return String.fromCharCode(h>>24&255,h>>16&255,h>>8&255,h&255)}function td(h){const t=Object.create(null);for(const[e,s]of h)t[e]=s;return t}function Sp(){const h=new Uint8Array(4);return h[0]=1,new Uint32Array(h.buffer,0,1)[0]===1}function xp(){try{return new Function(""),!0}catch{return!1}}class he{static get isLittleEndian(){return J(this,"isLittleEndian",Sp())}static get isEvalSupported(){return J(this,"isEvalSupported",xp())}static get isOffscreenCanvasSupported(){return J(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get platform(){return typeof navigator<"u"&&typeof(navigator==null?void 0:navigator.platform)=="string"?J(this,"platform",{isMac:navigator.platform.includes("Mac")}):J(this,"platform",{isMac:!1})}static get isCSSRoundSupported(){var t,e;return J(this,"isCSSRoundSupported",(e=(t=globalThis.CSS)==null?void 0:t.supports)==null?void 0:e.call(t,"width: round(1.5px, 1px)"))}}const Ph=Array.from(Array(256).keys(),h=>h.toString(16).padStart(2,"0"));var Xs,gl,Hh;class F{static makeHexColor(t,e,s){return`#${Ph[t]}${Ph[e]}${Ph[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){const s=t[0]*e[0]+t[1]*e[2]+e[4],i=t[0]*e[1]+t[1]*e[3]+e[5];return[s,i]}static applyInverseTransform(t,e){const s=e[0]*e[3]-e[1]*e[2],i=(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/s,n=(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/s;return[i,n]}static getAxialAlignedBoundingBox(t,e){const s=this.applyTransform(t,e),i=this.applyTransform(t.slice(2,4),e),n=this.applyTransform([t[0],t[3]],e),a=this.applyTransform([t[2],t[1]],e);return[Math.min(s[0],i[0],n[0],a[0]),Math.min(s[1],i[1],n[1],a[1]),Math.max(s[0],i[0],n[0],a[0]),Math.max(s[1],i[1],n[1],a[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],s=t[0]*e[0]+t[1]*e[2],i=t[0]*e[1]+t[1]*e[3],n=t[2]*e[0]+t[3]*e[2],a=t[2]*e[1]+t[3]*e[3],o=(s+a)/2,l=Math.sqrt((s+a)**2-4*(s*a-n*i))/2,c=o+l||1,d=o-l||1;return[Math.sqrt(c),Math.sqrt(d)]}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>a?null:[s,n,i,a]}static bezierBoundingBox(t,e,s,i,n,a,o,l,c){return c?(c[0]=Math.min(c[0],t,o),c[1]=Math.min(c[1],e,l),c[2]=Math.max(c[2],t,o),c[3]=Math.max(c[3],e,l)):c=[Math.min(t,o),Math.min(e,l),Math.max(t,o),Math.max(e,l)],m(this,Xs,Hh).call(this,t,s,n,o,e,i,a,l,3*(-t+3*(s-n)+o),6*(t-2*s+n),3*(s-t),c),m(this,Xs,Hh).call(this,t,s,n,o,e,i,a,l,3*(-e+3*(i-a)+l),6*(e-2*i+a),3*(i-e),c),c}}Xs=new WeakSet,gl=function(t,e,s,i,n,a,o,l,c,d){if(c<=0||c>=1)return;const u=1-c,f=c*c,p=f*c,b=u*(u*(u*t+3*c*e)+3*f*s)+p*i,v=u*(u*(u*n+3*c*a)+3*f*o)+p*l;d[0]=Math.min(d[0],b),d[1]=Math.min(d[1],v),d[2]=Math.max(d[2],b),d[3]=Math.max(d[3],v)},Hh=function(t,e,s,i,n,a,o,l,c,d,u,f){if(Math.abs(c)<1e-12){Math.abs(d)>=1e-12&&m(this,Xs,gl).call(this,t,e,s,i,n,a,o,l,-u/d,f);return}const p=d**2-4*u*c;if(p<0)return;const b=Math.sqrt(p),v=2*c;m(this,Xs,gl).call(this,t,e,s,i,n,a,o,l,(-d+b)/v,f),m(this,Xs,gl).call(this,t,e,s,i,n,a,o,l,(-d-b)/v,f)},A(F,Xs);function Cp(h){return decodeURIComponent(escape(h))}let Lh=null,gd=null;function Tp(h){return Lh||(Lh=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,gd=new Map([["ﬅ","ſt"]])),h.replaceAll(Lh,(t,e,s)=>e?e.normalize("NFKC"):gd.get(s))}function Rp(){if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.randomUUID)=="function")return crypto.randomUUID();const h=new Uint8Array(32);if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.getRandomValues)=="function")crypto.getRandomValues(h);else for(let t=0;t<32;t++)h[t]=Math.floor(Math.random()*255);return Wd(h)}const qd="pdfjs_internal_id_",gs={BEZIER_CURVE_TO:0,MOVE_TO:1,LINE_TO:2,QUADRATIC_CURVE_TO:3,RESTORE:4,SAVE:5,SCALE:6,TRANSFORM:7,TRANSLATE:8};class bh{constructor(){this.constructor===bh&&at("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,n){return"none"}destroy(t=!1){}}class Ah{constructor(){this.constructor===Ah&&at("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d")}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){at("Abstract method `_createCanvas` called.")}}class vh{constructor({baseUrl:t=null,isCompressed:e=!0}){this.constructor===vh&&at("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":""),s=this.isCompressed?Fh.BINARY:Fh.NONE;return this._fetchData(e,s).catch(i=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}_fetchData(t,e){at("Abstract method `_fetchData` called.")}}class yh{constructor({baseUrl:t=null}){this.constructor===yh&&at("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetchData(e).catch(s=>{throw new Error(`Unable to load font data at: ${e}`)})}_fetchData(t){at("Abstract method `_fetchData` called.")}}class ed{constructor(){this.constructor===ed&&at("Cannot initialize BaseSVGFactory.")}create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){at("Abstract method `_createSVG` called.")}}const bs="http://www.w3.org/2000/svg",Di=class Di{};V(Di,"CSS",96),V(Di,"PDF",72),V(Di,"PDF_TO_CSS_UNITS",Di.CSS/Di.PDF);let xi=Di;var Oi,Ke,Es,re,Wl,Ni,I,Gt,Gn,Vn,ml,Xd,Bh,Wn,ea,sa,Uh,ia;class Pp extends bh{constructor({docId:e,ownerDocument:s=globalThis.document}={}){super();A(this,I);A(this,Oi);A(this,Ke);A(this,Es);A(this,re);A(this,Wl);A(this,Ni,0);g(this,Es,e),g(this,re,s)}addFilter(e){if(!e)return"none";let s=r(this,I,Gt).get(e);if(s)return s;const[i,n,a]=m(this,I,ml).call(this,e),o=e.length===1?i:`${i}${n}${a}`;if(s=r(this,I,Gt).get(o),s)return r(this,I,Gt).set(e,s),s;const l=`g_${r(this,Es)}_transfer_map_${pe(this,Ni)._++}`,c=`url(#${l})`;r(this,I,Gt).set(e,c),r(this,I,Gt).set(o,c);const d=m(this,I,Wn).call(this,l);return m(this,I,sa).call(this,i,n,a,d),c}addHCMFilter(e,s){var b;const i=`${e}-${s}`,n="base";let a=r(this,I,Gn).get(n);if((a==null?void 0:a.key)===i||(a?((b=a.filter)==null||b.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},r(this,I,Gn).set(n,a)),!e||!s))return a.url;const o=m(this,I,ia).call(this,e);e=F.makeHexColor(...o);const l=m(this,I,ia).call(this,s);if(s=F.makeHexColor(...l),r(this,I,Vn).style.color="",e==="#000000"&&s==="#ffffff"||e===s)return a.url;const c=new Array(256);for(let v=0;v<=255;v++){const _=v/255;c[v]=_<=.03928?_/12.92:((_+.055)/1.055)**2.4}const d=c.join(","),u=`g_${r(this,Es)}_hcm_filter`,f=a.filter=m(this,I,Wn).call(this,u);m(this,I,sa).call(this,d,d,d,f),m(this,I,Bh).call(this,f);const p=(v,_)=>{const y=o[v]/255,E=l[v]/255,w=new Array(_+1);for(let x=0;x<=_;x++)w[x]=y+x/_*(E-y);return w.join(",")};return m(this,I,sa).call(this,p(0,5),p(1,5),p(2,5),f),a.url=`url(#${u})`,a.url}addAlphaFilter(e){let s=r(this,I,Gt).get(e);if(s)return s;const[i]=m(this,I,ml).call(this,[e]),n=`alpha_${i}`;if(s=r(this,I,Gt).get(n),s)return r(this,I,Gt).set(e,s),s;const a=`g_${r(this,Es)}_alpha_map_${pe(this,Ni)._++}`,o=`url(#${a})`;r(this,I,Gt).set(e,o),r(this,I,Gt).set(n,o);const l=m(this,I,Wn).call(this,a);return m(this,I,Uh).call(this,i,l),o}addLuminosityFilter(e){let s=r(this,I,Gt).get(e||"luminosity");if(s)return s;let i,n;if(e?([i]=m(this,I,ml).call(this,[e]),n=`luminosity_${i}`):n="luminosity",s=r(this,I,Gt).get(n),s)return r(this,I,Gt).set(e,s),s;const a=`g_${r(this,Es)}_luminosity_map_${pe(this,Ni)._++}`,o=`url(#${a})`;r(this,I,Gt).set(e,o),r(this,I,Gt).set(n,o);const l=m(this,I,Wn).call(this,a);return m(this,I,Xd).call(this,l),e&&m(this,I,Uh).call(this,i,l),o}addHighlightHCMFilter(e,s,i,n,a){var E;const o=`${s}-${i}-${n}-${a}`;let l=r(this,I,Gn).get(e);if((l==null?void 0:l.key)===o||(l?((E=l.filter)==null||E.remove(),l.key=o,l.url="none",l.filter=null):(l={key:o,url:"none",filter:null},r(this,I,Gn).set(e,l)),!s||!i))return l.url;const[c,d]=[s,i].map(m(this,I,ia).bind(this));let u=Math.round(.2126*c[0]+.7152*c[1]+.0722*c[2]),f=Math.round(.2126*d[0]+.7152*d[1]+.0722*d[2]),[p,b]=[n,a].map(m(this,I,ia).bind(this));f<u&&([u,f,p,b]=[f,u,b,p]),r(this,I,Vn).style.color="";const v=(w,x,S)=>{const C=new Array(256),R=(f-u)/S,k=w/255,D=(x-w)/(255*S);let H=0;for(let T=0;T<=S;T++){const j=Math.round(u+T*R),O=k+T*D;for(let $=H;$<=j;$++)C[$]=O;H=j+1}for(let T=H;T<256;T++)C[T]=C[H-1];return C.join(",")},_=`g_${r(this,Es)}_hcm_${e}_filter`,y=l.filter=m(this,I,Wn).call(this,_);return m(this,I,Bh).call(this,y),m(this,I,sa).call(this,v(p[0],b[0],5),v(p[1],b[1],5),v(p[2],b[2],5),y),l.url=`url(#${_})`,l.url}destroy(e=!1){e&&r(this,I,Gn).size!==0||(r(this,Ke)&&(r(this,Ke).parentNode.parentNode.remove(),g(this,Ke,null)),r(this,Oi)&&(r(this,Oi).clear(),g(this,Oi,null)),g(this,Ni,0))}}Oi=new WeakMap,Ke=new WeakMap,Es=new WeakMap,re=new WeakMap,Wl=new WeakMap,Ni=new WeakMap,I=new WeakSet,Gt=function(){return r(this,Oi)||g(this,Oi,new Map)},Gn=function(){return r(this,Wl)||g(this,Wl,new Map)},Vn=function(){if(!r(this,Ke)){const e=r(this,re).createElement("div"),{style:s}=e;s.visibility="hidden",s.contain="strict",s.width=s.height=0,s.position="absolute",s.top=s.left=0,s.zIndex=-1;const i=r(this,re).createElementNS(bs,"svg");i.setAttribute("width",0),i.setAttribute("height",0),g(this,Ke,r(this,re).createElementNS(bs,"defs")),e.append(i),i.append(r(this,Ke)),r(this,re).body.append(e)}return r(this,Ke)},ml=function(e){if(e.length===1){const c=e[0],d=new Array(256);for(let f=0;f<256;f++)d[f]=c[f]/255;const u=d.join(",");return[u,u,u]}const[s,i,n]=e,a=new Array(256),o=new Array(256),l=new Array(256);for(let c=0;c<256;c++)a[c]=s[c]/255,o[c]=i[c]/255,l[c]=n[c]/255;return[a.join(","),o.join(","),l.join(",")]},Xd=function(e){const s=r(this,re).createElementNS(bs,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),e.append(s)},Bh=function(e){const s=r(this,re).createElementNS(bs,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),e.append(s)},Wn=function(e){const s=r(this,re).createElementNS(bs,"filter");return s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("id",e),r(this,I,Vn).append(s),s},ea=function(e,s,i){const n=r(this,re).createElementNS(bs,s);n.setAttribute("type","discrete"),n.setAttribute("tableValues",i),e.append(n)},sa=function(e,s,i,n){const a=r(this,re).createElementNS(bs,"feComponentTransfer");n.append(a),m(this,I,ea).call(this,a,"feFuncR",e),m(this,I,ea).call(this,a,"feFuncG",s),m(this,I,ea).call(this,a,"feFuncB",i)},Uh=function(e,s){const i=r(this,re).createElementNS(bs,"feComponentTransfer");s.append(i),m(this,I,ea).call(this,i,"feFuncA",e)},ia=function(e){return r(this,I,Vn).style.color=e,ad(getComputedStyle(r(this,I,Vn)).getPropertyValue("color"))};class Lp extends Ah{constructor({ownerDocument:t=globalThis.document}={}){super(),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}async function _h(h,t="text"){if(na(h,document.baseURI)){const e=await fetch(h);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",h,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE){if(i.status===200||i.status===0){switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText);return}s(new Error(i.statusText))}},i.send(null)})}class Yd extends vh{_fetchData(t,e){return _h(t,this.isCompressed?"arraybuffer":"text").then(s=>({cMapData:s instanceof ArrayBuffer?new Uint8Array(s):mh(s),compressionType:e}))}}class Kd extends yh{_fetchData(t){return _h(t,"arraybuffer").then(e=>new Uint8Array(e))}}class sd extends ed{_createSVG(t){return document.createElementNS(bs,t)}}class tl{constructor({viewBox:t,scale:e,rotation:s,offsetX:i=0,offsetY:n=0,dontFlip:a=!1}){this.viewBox=t,this.scale=e,this.rotation=s,this.offsetX=i,this.offsetY=n;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let c,d,u,f;switch(s%=360,s<0&&(s+=360),s){case 180:c=-1,d=0,u=0,f=1;break;case 90:c=0,d=1,u=1,f=0;break;case 270:c=0,d=-1,u=-1,f=0;break;case 0:c=1,d=0,u=0,f=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}a&&(u=-u,f=-f);let p,b,v,_;c===0?(p=Math.abs(l-t[1])*e+i,b=Math.abs(o-t[0])*e+n,v=(t[3]-t[1])*e,_=(t[2]-t[0])*e):(p=Math.abs(o-t[0])*e+i,b=Math.abs(l-t[1])*e+n,v=(t[2]-t[0])*e,_=(t[3]-t[1])*e),this.transform=[c*e,d*e,u*e,f*e,p-c*e*o-u*e*l,b-d*e*o-f*e*l],this.width=v,this.height=_}get rawDims(){const{viewBox:t}=this;return J(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:n=!1}={}){return new tl({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}convertToViewportPoint(t,e){return F.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=F.applyTransform([t[0],t[1]],this.transform),s=F.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){return F.applyInverseTransform([t,e],this.transform)}}class id extends Ri{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function nd(h){const t=h.length;let e=0;for(;e<t&&h[e].trim()==="";)e++;return h.substring(e,e+5).toLowerCase()==="data:"}function rd(h){return typeof h=="string"&&/\.pdf$/i.test(h)}function kp(h){return[h]=h.split(/[#?]/,1),h.substring(h.lastIndexOf("/")+1)}function Mp(h,t="document.pdf"){if(typeof h!="string")return t;if(nd(h))return q('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const e=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,s=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=e.exec(h);let n=s.exec(i[1])||s.exec(i[2])||s.exec(i[3]);if(n&&(n=n[0],n.includes("%")))try{n=s.exec(decodeURIComponent(n))[0]}catch{}return n||t}class md{constructor(){V(this,"started",Object.create(null));V(this,"times",[])}time(t){t in this.started&&q(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||q(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:n}of this.times)t.push(`${s.padEnd(e)} ${n-i}ms
`);return t.join("")}}function na(h,t){try{const{protocol:e}=t?new URL(h,t):new URL(h);return e==="http:"||e==="https:"}catch{return!1}}function de(h){h.preventDefault()}function Qd(h){console.log("Deprecated API usage: "+h)}let bd;class Jd{static toDateObject(t){if(!t||typeof t!="string")return null;bd||(bd=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=bd.exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const c=e[7]||"Z";let d=parseInt(e[8],10);d=d>=0&&d<=23?d:0;let u=parseInt(e[9],10)||0;return u=u>=0&&u<=59?u:0,c==="-"?(a+=d,o+=u):c==="+"&&(a-=d,o-=u),new Date(Date.UTC(s,i,n,a,o,l))}}function Ip(h,{scale:t=1,rotation:e=0}){const{width:s,height:i}=h.attributes.style,n=[0,0,parseInt(s),parseInt(i)];return new tl({viewBox:n,scale:t,rotation:e})}function ad(h){if(h.startsWith("#")){const t=parseInt(h.slice(1),16);return[(t&16711680)>>16,(t&65280)>>8,t&255]}return h.startsWith("rgb(")?h.slice(4,-1).split(",").map(t=>parseInt(t)):h.startsWith("rgba(")?h.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(q(`Not a valid color format: "${h}"`),[0,0,0])}function Dp(h){const t=document.createElement("span");t.style.visibility="hidden",document.body.append(t);for(const e of h.keys()){t.style.color=e;const s=window.getComputedStyle(t).color;h.set(e,ad(s))}t.remove()}function mt(h){const{a:t,b:e,c:s,d:i,e:n,f:a}=h.getTransform();return[t,e,s,i,n,a]}function ms(h){const{a:t,b:e,c:s,d:i,e:n,f:a}=h.getTransform().invertSelf();return[t,e,s,i,n,a]}function Mn(h,t,e=!1,s=!0){if(t instanceof tl){const{pageWidth:i,pageHeight:n}=t.rawDims,{style:a}=h,o=he.isCSSRoundSupported,l=`var(--scale-factor) * ${i}px`,c=`var(--scale-factor) * ${n}px`,d=o?`round(${l}, 1px)`:`calc(${l})`,u=o?`round(${c}, 1px)`:`calc(${c})`;!e||t.rotation%180===0?(a.width=d,a.height=u):(a.width=u,a.height=d)}s&&h.setAttribute("data-main-rotation",t.rotation)}var Hi,Bi,Qe,Ui,ql,Zd,ee,tu,eu,bl,su,$h;const Xl=class Xl{constructor(t){A(this,ee);A(this,Hi,null);A(this,Bi,null);A(this,Qe);A(this,Ui,null);g(this,Qe,t)}render(){const t=g(this,Hi,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",de),t.addEventListener("pointerdown",m(Xl,ql,Zd));const e=g(this,Ui,document.createElement("div"));e.className="buttons",t.append(e);const s=r(this,Qe).toolbarPosition;if(s){const{style:i}=t,n=r(this,Qe)._uiManager.direction==="ltr"?1-s[0]:s[0];i.insetInlineEnd=`${100*n}%`,i.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}return m(this,ee,su).call(this),t}hide(){var t;r(this,Hi).classList.add("hidden"),(t=r(this,Bi))==null||t.hideDropdown()}show(){r(this,Hi).classList.remove("hidden")}addAltTextButton(t){m(this,ee,bl).call(this,t),r(this,Ui).prepend(t,r(this,ee,$h))}addColorPicker(t){g(this,Bi,t);const e=t.renderButton();m(this,ee,bl).call(this,e),r(this,Ui).prepend(e,r(this,ee,$h))}remove(){var t;r(this,Hi).remove(),(t=r(this,Bi))==null||t.destroy(),g(this,Bi,null)}};Hi=new WeakMap,Bi=new WeakMap,Qe=new WeakMap,Ui=new WeakMap,ql=new WeakSet,Zd=function(t){t.stopPropagation()},ee=new WeakSet,tu=function(t){r(this,Qe)._focusEventsAllowed=!1,t.preventDefault(),t.stopPropagation()},eu=function(t){r(this,Qe)._focusEventsAllowed=!0,t.preventDefault(),t.stopPropagation()},bl=function(t){t.addEventListener("focusin",m(this,ee,tu).bind(this),{capture:!0}),t.addEventListener("focusout",m(this,ee,eu).bind(this),{capture:!0}),t.addEventListener("contextmenu",de)},su=function(){const t=document.createElement("button");t.className="delete",t.tabIndex=0,t.setAttribute("data-l10n-id",`pdfjs-editor-remove-${r(this,Qe).editorType}-button`),m(this,ee,bl).call(this,t),t.addEventListener("click",e=>{r(this,Qe)._uiManager.delete()}),r(this,Ui).append(t)},$h=function(){const t=document.createElement("div");return t.className="divider",t},A(Xl,ql);let jh=Xl;var ya,ji,_a,Ci,iu,nu,ru;class Fp{constructor(t){A(this,Ci);A(this,ya,null);A(this,ji,null);A(this,_a);g(this,_a,t)}show(t,e,s){const[i,n]=m(this,Ci,nu).call(this,e,s),{style:a}=r(this,ji)||g(this,ji,m(this,Ci,iu).call(this));t.append(r(this,ji)),a.insetInlineEnd=`${100*i}%`,a.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){r(this,ji).remove()}}ya=new WeakMap,ji=new WeakMap,_a=new WeakMap,Ci=new WeakSet,iu=function(){const t=g(this,ji,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",de);const e=g(this,ya,document.createElement("div"));return e.className="buttons",t.append(e),m(this,Ci,ru).call(this),t},nu=function(t,e){let s=0,i=0;for(const n of t){const a=n.y+n.height;if(a<s)continue;const o=n.x+(e?n.width:0);if(a>s){i=o,s=a;continue}e?o>i&&(i=o):o<i&&(i=o)}return[e?1-i:i,s]},ru=function(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label"),t.addEventListener("contextmenu",de),t.addEventListener("click",()=>{r(this,_a).highlightSelection("floating_button")}),r(this,ya).append(t)};function Bl(h,t,e){for(const s of e)t.addEventListener(s,h[s].bind(h))}function Op(h){return Math.round(Math.min(255,Math.max(1,255*h))).toString(16).padStart(2,"0")}var Yl;class Np{constructor(){A(this,Yl,0)}get id(){return`${gp}${pe(this,Yl)._++}`}}Yl=new WeakMap;var Ea,Kl,Ce,wa,Gh;const dd=class dd{constructor(){A(this,wa);A(this,Ea,Rp());A(this,Kl,0);A(this,Ce,null)}static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',s=new OffscreenCanvas(1,3).getContext("2d"),i=new Image;i.src=t;const n=i.decode().then(()=>(s.drawImage(i,0,0,1,1,0,0,1,3),new Uint32Array(s.getImageData(0,0,1,1).data.buffer)[0]===0));return J(this,"_isSVGFittingCanvas",n)}async getFromFile(t){const{lastModified:e,name:s,size:i,type:n}=t;return m(this,wa,Gh).call(this,`${e}_${s}_${i}_${n}`,t)}async getFromUrl(t){return m(this,wa,Gh).call(this,t,t)}async getFromId(t){r(this,Ce)||g(this,Ce,new Map);const e=r(this,Ce).get(t);return e?e.bitmap?(e.refCounter+=1,e):e.file?this.getFromFile(e.file):this.getFromUrl(e.url):null}getSvgUrl(t){const e=r(this,Ce).get(t);return e!=null&&e.isSvg?e.svgUrl:null}deleteId(t){r(this,Ce)||g(this,Ce,new Map);const e=r(this,Ce).get(t);e&&(e.refCounter-=1,e.refCounter===0&&(e.bitmap=null))}isValidId(t){return t.startsWith(`image_${r(this,Ea)}_`)}};Ea=new WeakMap,Kl=new WeakMap,Ce=new WeakMap,wa=new WeakSet,Gh=async function(t,e){r(this,Ce)||g(this,Ce,new Map);let s=r(this,Ce).get(t);if(s===null)return null;if(s!=null&&s.bitmap)return s.refCounter+=1,s;try{s||(s={bitmap:null,id:`image_${r(this,Ea)}_${pe(this,Kl)._++}`,refCounter:0,isSvg:!1});let i;if(typeof e=="string"?(s.url=e,i=await _h(e,"blob")):i=s.file=e,i.type==="image/svg+xml"){const n=dd._isSVGFittingCanvas,a=new FileReader,o=new Image,l=new Promise((c,d)=>{o.onload=()=>{s.bitmap=o,s.isSvg=!0,c()},a.onload=async()=>{const u=s.svgUrl=a.result;o.src=await n?`${u}#svgView(preserveAspectRatio(none))`:u},o.onerror=a.onerror=d});a.readAsDataURL(i),await l}else s.bitmap=await createImageBitmap(i);s.refCounter=1}catch(i){console.error(i),s=null}return r(this,Ce).set(t,s),s&&r(this,Ce).set(s.id,s),s};let zh=dd;var Mt,ni,Sa,It;class Hp{constructor(t=128){A(this,Mt,[]);A(this,ni,!1);A(this,Sa);A(this,It,-1);g(this,Sa,t)}add({cmd:t,undo:e,post:s,mustExec:i,type:n=NaN,overwriteIfSameType:a=!1,keepUndo:o=!1}){if(i&&t(),r(this,ni))return;const l={cmd:t,undo:e,post:s,type:n};if(r(this,It)===-1){r(this,Mt).length>0&&(r(this,Mt).length=0),g(this,It,0),r(this,Mt).push(l);return}if(a&&r(this,Mt)[r(this,It)].type===n){o&&(l.undo=r(this,Mt)[r(this,It)].undo),r(this,Mt)[r(this,It)]=l;return}const c=r(this,It)+1;c===r(this,Sa)?r(this,Mt).splice(0,1):(g(this,It,c),c<r(this,Mt).length&&r(this,Mt).splice(c)),r(this,Mt).push(l)}undo(){if(r(this,It)===-1)return;g(this,ni,!0);const{undo:t,post:e}=r(this,Mt)[r(this,It)];t(),e==null||e(),g(this,ni,!1),g(this,It,r(this,It)-1)}redo(){if(r(this,It)<r(this,Mt).length-1){g(this,It,r(this,It)+1),g(this,ni,!0);const{cmd:t,post:e}=r(this,Mt)[r(this,It)];t(),e==null||e(),g(this,ni,!1)}}hasSomethingToUndo(){return r(this,It)!==-1}hasSomethingToRedo(){return r(this,It)<r(this,Mt).length-1}destroy(){g(this,Mt,null)}}Mt=new WeakMap,ni=new WeakMap,Sa=new WeakMap,It=new WeakMap;var Ql,au;class el{constructor(t){A(this,Ql);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=he.platform;for(const[s,i,n={}]of t)for(const a of s){const o=a.startsWith("mac+");e&&o?(this.callbacks.set(a.slice(4),{callback:i,options:n}),this.allKeys.add(a.split("+").at(-1))):!e&&!o&&(this.callbacks.set(a,{callback:i,options:n}),this.allKeys.add(a.split("+").at(-1)))}}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(m(this,Ql,au).call(this,e));if(!s)return;const{callback:i,options:{bubbles:n=!1,args:a=[],checker:o=null}}=s;o&&!o(t,e)||(i.bind(t,...a,e)(),n||(e.stopPropagation(),e.preventDefault()))}}Ql=new WeakSet,au=function(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e};const Jl=class Jl{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return Dp(t),J(this,"_colors",t)}convert(t){const e=ad(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((n,a)=>n===e[a]))return Jl._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?F.makeHexColor(...e):t}};V(Jl,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let Vh=Jl;var be,Ct,Ft,er,ws,sr,Ne,ir,$i,Je,Ss,zi,xa,Ca,Ze,nr,ri,ts,Zl,ai,Ta,Gi,Ra,rr,Ot,it,xs,Vi,Pa,La,ka,Ma,Ia,Da,Fa,Oa,Na,Ha,Ba,Ua,ja,$a,oi,es,Cs,za,P,Al,ou,lu,vl,hu,cu,du,Wh,qh,Xh,Yh,Kh,Kt,ti,uu,fu,Qh,pu,ra,Jh;const Kn=class Kn{constructor(t,e,s,i,n,a,o,l,c){A(this,P);A(this,be,null);A(this,Ct,new Map);A(this,Ft,new Map);A(this,er,null);A(this,ws,null);A(this,sr,null);A(this,Ne,new Hp);A(this,ir,0);A(this,$i,new Set);A(this,Je,null);A(this,Ss,null);A(this,zi,new Set);A(this,xa,!1);A(this,Ca,null);A(this,Ze,null);A(this,nr,null);A(this,ri,!1);A(this,ts,null);A(this,Zl,new Np);A(this,ai,!1);A(this,Ta,!1);A(this,Gi,null);A(this,Ra,null);A(this,rr,null);A(this,Ot,Q.NONE);A(this,it,new Set);A(this,xs,null);A(this,Vi,null);A(this,Pa,null);A(this,La,this.blur.bind(this));A(this,ka,this.focus.bind(this));A(this,Ma,this.copy.bind(this));A(this,Ia,this.cut.bind(this));A(this,Da,this.paste.bind(this));A(this,Fa,this.keydown.bind(this));A(this,Oa,this.keyup.bind(this));A(this,Na,this.onEditingAction.bind(this));A(this,Ha,this.onPageChanging.bind(this));A(this,Ba,this.onScaleChanging.bind(this));A(this,Ua,m(this,P,lu).bind(this));A(this,ja,this.onRotationChanging.bind(this));A(this,$a,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1});A(this,oi,[0,0]);A(this,es,null);A(this,Cs,null);A(this,za,null);g(this,Cs,t),g(this,za,e),g(this,er,s),this._eventBus=i,this._eventBus._on("editingaction",r(this,Na)),this._eventBus._on("pagechanging",r(this,Ha)),this._eventBus._on("scalechanging",r(this,Ba)),this._eventBus._on("rotationchanging",r(this,ja)),m(this,P,hu).call(this),m(this,P,qh).call(this),g(this,ws,n.annotationStorage),g(this,Ca,n.filterFactory),g(this,Vi,a),g(this,nr,o||null),g(this,xa,l),g(this,rr,c||null),this.viewParameters={realScale:xi.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1}static get _keyboardManager(){const t=Kn.prototype,e=a=>r(a,Cs).contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&a.hasSomethingToControl(),s=(a,{target:o})=>{if(o instanceof HTMLInputElement){const{type:l}=o;return l!=="text"&&l!=="number"}return!0},i=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return J(this,"_keyboardManager",new el([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&r(a,Cs).contains(o)&&!a.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&r(a,Cs).contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}destroy(){var t,e;m(this,P,Xh).call(this),m(this,P,Wh).call(this),this._eventBus._off("editingaction",r(this,Na)),this._eventBus._off("pagechanging",r(this,Ha)),this._eventBus._off("scalechanging",r(this,Ba)),this._eventBus._off("rotationchanging",r(this,ja));for(const s of r(this,Ft).values())s.destroy();r(this,Ft).clear(),r(this,Ct).clear(),r(this,zi).clear(),g(this,be,null),r(this,it).clear(),r(this,Ne).destroy(),(t=r(this,er))==null||t.destroy(),(e=r(this,ts))==null||e.hide(),g(this,ts,null),r(this,Ze)&&(clearTimeout(r(this,Ze)),g(this,Ze,null)),r(this,es)&&(clearTimeout(r(this,es)),g(this,es,null)),m(this,P,cu).call(this)}async mlGuess(t){var e;return((e=r(this,rr))==null?void 0:e.guess(t))||null}get hasMLManager(){return!!r(this,rr)}get hcmFilter(){return J(this,"hcmFilter",r(this,Vi)?r(this,Ca).addHCMFilter(r(this,Vi).foreground,r(this,Vi).background):"none")}get direction(){return J(this,"direction",getComputedStyle(r(this,Cs)).direction)}get highlightColors(){return J(this,"highlightColors",r(this,nr)?new Map(r(this,nr).split(",").map(t=>t.split("=").map(e=>e.trim()))):null)}get highlightColorNames(){return J(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setMainHighlightColorPicker(t){g(this,Ra,t)}editAltText(t){var e;(e=r(this,er))==null||e.editAltText(this,t)}onPageChanging({pageNumber:t}){g(this,ir,t-1)}focusMainContainer(){r(this,Cs).focus()}findParent(t,e){for(const s of r(this,Ft).values()){const{x:i,y:n,width:a,height:o}=s.div.getBoundingClientRect();if(t>=i&&t<=i+a&&e>=n&&e<=n+o)return s}return null}disableUserSelect(t=!1){r(this,za).classList.toggle("noUserSelect",t)}addShouldRescale(t){r(this,zi).add(t)}removeShouldRescale(t){r(this,zi).delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*xi.PDF_TO_CSS_UNITS;for(const e of r(this,zi))e.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:a}=e,o=e.toString(),c=m(this,P,Al).call(this,e).closest(".textLayer"),d=this.getSelectionBoxes(c);if(d){e.empty(),r(this,Ot)===Q.NONE&&(this._eventBus.dispatch("showannotationeditorui",{source:this,mode:Q.HIGHLIGHT}),this.showAllEditors("highlight",!0,!0));for(const u of r(this,Ft).values())if(u.hasTextLayer(c)){u.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:d,anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:a,text:o});break}}}addToAnnotationStorage(t){!t.isEmpty()&&r(this,ws)&&!r(this,ws).has(t.id)&&r(this,ws).setValue(t.id,t)}blur(){if(this.isShiftKeyDown=!1,r(this,ri)&&(g(this,ri,!1),m(this,P,vl).call(this,"main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of r(this,it))if(e.div.contains(t)){g(this,Gi,[e,t]),e._focusEventsAllowed=!1;break}}focus(){if(!r(this,Gi))return;const[t,e]=r(this,Gi);g(this,Gi,null),e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0}),e.focus()}addEditListeners(){m(this,P,qh).call(this),m(this,P,Yh).call(this)}removeEditListeners(){m(this,P,Xh).call(this),m(this,P,Kh).call(this)}copy(t){var s;if(t.preventDefault(),(s=r(this,be))==null||s.commitOrRemove(),!this.hasSelection)return;const e=[];for(const i of r(this,it)){const n=i.serialize(!0);n&&e.push(n)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}paste(t){t.preventDefault();const{clipboardData:e}=t;for(const n of e.items)for(const a of r(this,Ss))if(a.isHandlingMimeForPasting(n.type)){a.paste(n,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(n){q(`paste: "${n.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const n=[];for(const l of s){const c=i.deserialize(l);if(!c)return;n.push(c)}const a=()=>{for(const l of n)m(this,P,Qh).call(this,l);m(this,P,Jh).call(this,n)},o=()=>{for(const l of n)l.remove()};this.addCommands({cmd:a,undo:o,mustExec:!0})}catch(n){q(`paste: "${n.message}".`)}}keydown(t){!this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!0),r(this,Ot)!==Q.NONE&&!this.isEditorHandlingKeyboard&&Kn._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,r(this,ri)&&(g(this,ri,!1),m(this,P,vl).call(this,"main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu");break}}setEditingState(t){t?(m(this,P,du).call(this),m(this,P,Yh).call(this),m(this,P,Kt).call(this,{isEditing:r(this,Ot)!==Q.NONE,isEmpty:m(this,P,ra).call(this),hasSomethingToUndo:r(this,Ne).hasSomethingToUndo(),hasSomethingToRedo:r(this,Ne).hasSomethingToRedo(),hasSelectedEditor:!1})):(m(this,P,Wh).call(this),m(this,P,Kh).call(this),m(this,P,Kt).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!r(this,Ss)){g(this,Ss,t);for(const e of r(this,Ss))m(this,P,ti).call(this,e.defaultPropertiesToUpdate)}}getId(){return r(this,Zl).id}get currentLayer(){return r(this,Ft).get(r(this,ir))}getLayer(t){return r(this,Ft).get(t)}get currentPageIndex(){return r(this,ir)}addLayer(t){r(this,Ft).set(t.pageIndex,t),r(this,ai)?t.enable():t.disable()}removeLayer(t){r(this,Ft).delete(t.pageIndex)}updateMode(t,e=null,s=!1){if(r(this,Ot)!==t){if(g(this,Ot,t),t===Q.NONE){this.setEditingState(!1),m(this,P,fu).call(this);return}this.setEditingState(!0),m(this,P,uu).call(this),this.unselectAll();for(const i of r(this,Ft).values())i.updateMode(t);if(!e&&s){this.addNewEditorFromKeyboard();return}if(e){for(const i of r(this,Ct).values())if(i.annotationElementId===e){this.setSelected(i),i.enterInEditMode();break}}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==r(this,Ot)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){var s;if(r(this,Ss)){switch(t){case G.CREATE:this.currentLayer.addNewEditor();return;case G.HIGHLIGHT_DEFAULT_COLOR:(s=r(this,Ra))==null||s.updateColor(e);break;case G.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(r(this,Pa)||g(this,Pa,new Map)).set(t,e),this.showAllEditors("highlight",e);break}for(const i of r(this,it))i.updateParams(t,e);for(const i of r(this,Ss))i.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){var n;for(const a of r(this,Ct).values())a.editorType===t&&a.show(e);(((n=r(this,Pa))==null?void 0:n.get(G.HIGHLIGHT_SHOW_ALL))??!0)!==e&&m(this,P,ti).call(this,[[G.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(r(this,Ta)!==t){g(this,Ta,t);for(const e of r(this,Ft).values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}getEditors(t){const e=[];for(const s of r(this,Ct).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return r(this,Ct).get(t)}addEditor(t){r(this,Ct).set(t.id,t)}removeEditor(t){var e;t.div.contains(document.activeElement)&&(r(this,Ze)&&clearTimeout(r(this,Ze)),g(this,Ze,setTimeout(()=>{this.focusMainContainer(),g(this,Ze,null)},0))),r(this,Ct).delete(t.id),this.unselect(t),(!t.annotationElementId||!r(this,$i).has(t.annotationElementId))&&((e=r(this,ws))==null||e.remove(t.id))}addDeletedAnnotationElement(t){r(this,$i).add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return r(this,$i).has(t)}removeDeletedAnnotationElement(t){r(this,$i).delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}setActiveEditor(t){r(this,be)!==t&&(g(this,be,t),t&&m(this,P,ti).call(this,t.propertiesToUpdate))}updateUI(t){r(this,P,pu)===t&&m(this,P,ti).call(this,t.propertiesToUpdate)}toggleSelected(t){if(r(this,it).has(t)){r(this,it).delete(t),t.unselect(),m(this,P,Kt).call(this,{hasSelectedEditor:this.hasSelection});return}r(this,it).add(t),t.select(),m(this,P,ti).call(this,t.propertiesToUpdate),m(this,P,Kt).call(this,{hasSelectedEditor:!0})}setSelected(t){for(const e of r(this,it))e!==t&&e.unselect();r(this,it).clear(),r(this,it).add(t),t.select(),m(this,P,ti).call(this,t.propertiesToUpdate),m(this,P,Kt).call(this,{hasSelectedEditor:!0})}isSelected(t){return r(this,it).has(t)}get firstSelectedEditor(){return r(this,it).values().next().value}unselect(t){t.unselect(),r(this,it).delete(t),m(this,P,Kt).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return r(this,it).size!==0}get isEnterHandled(){return r(this,it).size===1&&this.firstSelectedEditor.isEnterHandled}undo(){r(this,Ne).undo(),m(this,P,Kt).call(this,{hasSomethingToUndo:r(this,Ne).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:m(this,P,ra).call(this)})}redo(){r(this,Ne).redo(),m(this,P,Kt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:r(this,Ne).hasSomethingToRedo(),isEmpty:m(this,P,ra).call(this)})}addCommands(t){r(this,Ne).add(t),m(this,P,Kt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:m(this,P,ra).call(this)})}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const t=[...r(this,it)],e=()=>{for(const i of t)i.remove()},s=()=>{for(const i of t)m(this,P,Qh).call(this,i)};this.addCommands({cmd:e,undo:s,mustExec:!0})}commitOrRemove(){var t;(t=r(this,be))==null||t.commitOrRemove()}hasSomethingToControl(){return r(this,be)||this.hasSelection}selectAll(){for(const t of r(this,it))t.commit();m(this,P,Jh).call(this,r(this,Ct).values())}unselectAll(){if(!(r(this,be)&&(r(this,be).commitOrRemove(),r(this,Ot)!==Q.NONE))&&this.hasSelection){for(const t of r(this,it))t.unselect();r(this,it).clear(),m(this,P,Kt).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;r(this,oi)[0]+=t,r(this,oi)[1]+=e;const[i,n]=r(this,oi),a=[...r(this,it)],o=1e3;r(this,es)&&clearTimeout(r(this,es)),g(this,es,setTimeout(()=>{g(this,es,null),r(this,oi)[0]=r(this,oi)[1]=0,this.addCommands({cmd:()=>{for(const l of a)r(this,Ct).has(l.id)&&l.translateInPage(i,n)},undo:()=>{for(const l of a)r(this,Ct).has(l.id)&&l.translateInPage(-i,-n)},mustExec:!1})},o));for(const l of a)l.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),g(this,Je,new Map);for(const t of r(this,it))r(this,Je).set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!r(this,Je))return!1;this.disableUserSelect(!1);const t=r(this,Je);g(this,Je,null);let e=!1;for(const[{x:i,y:n,pageIndex:a},o]of t)o.newX=i,o.newY=n,o.newPageIndex=a,e||(e=i!==o.savedX||n!==o.savedY||a!==o.savedPageIndex);if(!e)return!1;const s=(i,n,a,o)=>{if(r(this,Ct).has(i.id)){const l=r(this,Ft).get(o);l?i._setParentAndPosition(l,n,a):(i.pageIndex=o,i.x=n,i.y=a)}};return this.addCommands({cmd:()=>{for(const[i,{newX:n,newY:a,newPageIndex:o}]of t)s(i,n,a,o)},undo:()=>{for(const[i,{savedX:n,savedY:a,savedPageIndex:o}]of t)s(i,n,a,o)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(r(this,Je))for(const s of r(this,Je).keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){var t;return((t=this.getActive())==null?void 0:t.shouldGetKeyboardEvents())||r(this,it).size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return r(this,be)===t}getActive(){return r(this,be)}getMode(){return r(this,Ot)}get imageManager(){return J(this,"imageManager",new zh)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let c=0,d=e.rangeCount;c<d;c++)if(!t.contains(e.getRangeAt(c).commonAncestorContainer))return null;const{x:s,y:i,width:n,height:a}=t.getBoundingClientRect();let o;switch(t.getAttribute("data-main-rotation")){case"90":o=(c,d,u,f)=>({x:(d-i)/a,y:1-(c+u-s)/n,width:f/a,height:u/n});break;case"180":o=(c,d,u,f)=>({x:1-(c+u-s)/n,y:1-(d+f-i)/a,width:u/n,height:f/a});break;case"270":o=(c,d,u,f)=>({x:1-(d+f-i)/a,y:(c-s)/n,width:f/a,height:u/n});break;default:o=(c,d,u,f)=>({x:(c-s)/n,y:(d-i)/a,width:u/n,height:f/a});break}const l=[];for(let c=0,d=e.rangeCount;c<d;c++){const u=e.getRangeAt(c);if(!u.collapsed)for(const{x:f,y:p,width:b,height:v}of u.getClientRects())b===0||v===0||l.push(o(f,p,b,v))}return l.length===0?null:l}addChangedExistingAnnotation({annotationElementId:t,id:e}){(r(this,sr)||g(this,sr,new Map)).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){var e;(e=r(this,sr))==null||e.delete(t)}renderAnnotationElement(t){var i;const e=(i=r(this,sr))==null?void 0:i.get(t.data.id);if(!e)return;const s=r(this,ws).getRawValue(e);s&&(r(this,Ot)===Q.NONE&&!s.hasBeenModified||s.renderAnnotationElement(t))}};be=new WeakMap,Ct=new WeakMap,Ft=new WeakMap,er=new WeakMap,ws=new WeakMap,sr=new WeakMap,Ne=new WeakMap,ir=new WeakMap,$i=new WeakMap,Je=new WeakMap,Ss=new WeakMap,zi=new WeakMap,xa=new WeakMap,Ca=new WeakMap,Ze=new WeakMap,nr=new WeakMap,ri=new WeakMap,ts=new WeakMap,Zl=new WeakMap,ai=new WeakMap,Ta=new WeakMap,Gi=new WeakMap,Ra=new WeakMap,rr=new WeakMap,Ot=new WeakMap,it=new WeakMap,xs=new WeakMap,Vi=new WeakMap,Pa=new WeakMap,La=new WeakMap,ka=new WeakMap,Ma=new WeakMap,Ia=new WeakMap,Da=new WeakMap,Fa=new WeakMap,Oa=new WeakMap,Na=new WeakMap,Ha=new WeakMap,Ba=new WeakMap,Ua=new WeakMap,ja=new WeakMap,$a=new WeakMap,oi=new WeakMap,es=new WeakMap,Cs=new WeakMap,za=new WeakMap,P=new WeakSet,Al=function({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t},ou=function(){const t=document.getSelection();if(!t||t.isCollapsed)return;const s=m(this,P,Al).call(this,t).closest(".textLayer"),i=this.getSelectionBoxes(s);i&&(r(this,ts)||g(this,ts,new Fp(this)),r(this,ts).show(s,i,this.direction==="ltr"))},lu=function(){var n,a,o;const t=document.getSelection();if(!t||t.isCollapsed){r(this,xs)&&((n=r(this,ts))==null||n.hide(),g(this,xs,null),m(this,P,Kt).call(this,{hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===r(this,xs))return;if(!m(this,P,Al).call(this,t).closest(".textLayer")){r(this,xs)&&((a=r(this,ts))==null||a.hide(),g(this,xs,null),m(this,P,Kt).call(this,{hasSelectedText:!1}));return}if((o=r(this,ts))==null||o.hide(),g(this,xs,e),m(this,P,Kt).call(this,{hasSelectedText:!0}),!(r(this,Ot)!==Q.HIGHLIGHT&&r(this,Ot)!==Q.NONE)&&(r(this,Ot)===Q.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),g(this,ri,this.isShiftKeyDown),!this.isShiftKeyDown)){const l=c=>{c.type==="pointerup"&&c.button!==0||(window.removeEventListener("pointerup",l),window.removeEventListener("blur",l),c.type==="pointerup"&&m(this,P,vl).call(this,"main_toolbar"))};window.addEventListener("pointerup",l),window.addEventListener("blur",l)}},vl=function(t=""){r(this,Ot)===Q.HIGHLIGHT?this.highlightSelection(t):r(this,xa)&&m(this,P,ou).call(this)},hu=function(){document.addEventListener("selectionchange",r(this,Ua))},cu=function(){document.removeEventListener("selectionchange",r(this,Ua))},du=function(){window.addEventListener("focus",r(this,ka)),window.addEventListener("blur",r(this,La))},Wh=function(){window.removeEventListener("focus",r(this,ka)),window.removeEventListener("blur",r(this,La))},qh=function(){window.addEventListener("keydown",r(this,Fa)),window.addEventListener("keyup",r(this,Oa))},Xh=function(){window.removeEventListener("keydown",r(this,Fa)),window.removeEventListener("keyup",r(this,Oa))},Yh=function(){document.addEventListener("copy",r(this,Ma)),document.addEventListener("cut",r(this,Ia)),document.addEventListener("paste",r(this,Da))},Kh=function(){document.removeEventListener("copy",r(this,Ma)),document.removeEventListener("cut",r(this,Ia)),document.removeEventListener("paste",r(this,Da))},Kt=function(t){Object.entries(t).some(([s,i])=>r(this,$a)[s]!==i)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(r(this,$a),t)}),r(this,Ot)===Q.HIGHLIGHT&&t.hasSelectedEditor===!1&&m(this,P,ti).call(this,[[G.HIGHLIGHT_FREE,!0]]))},ti=function(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})},uu=function(){if(!r(this,ai)){g(this,ai,!0);for(const t of r(this,Ft).values())t.enable();for(const t of r(this,Ct).values())t.enable()}},fu=function(){if(this.unselectAll(),r(this,ai)){g(this,ai,!1);for(const t of r(this,Ft).values())t.disable();for(const t of r(this,Ct).values())t.disable()}},Qh=function(t){const e=r(this,Ft).get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))},pu=function(){let t=null;for(t of r(this,it));return t},ra=function(){if(r(this,Ct).size===0)return!0;if(r(this,Ct).size===1)for(const t of r(this,Ct).values())return t.isEmpty();return!1},Jh=function(t){for(const e of r(this,it))e.unselect();r(this,it).clear();for(const e of t)e.isEmpty()||(r(this,it).add(e),e.select());m(this,P,Kt).call(this,{hasSelectedEditor:this.hasSelection})},V(Kn,"TRANSLATE_SMALL",1),V(Kn,"TRANSLATE_BIG",10);let In=Kn;var Ts,Rs,He,Ps,Be,ar,Ls,Ga,Zh;const As=class As{constructor(t){A(this,Ga);A(this,Ts,"");A(this,Rs,!1);A(this,He,null);A(this,Ps,null);A(this,Be,null);A(this,ar,!1);A(this,Ls,null);g(this,Ls,t)}static initialize(t){As._l10nPromise||(As._l10nPromise=t)}async render(){const t=g(this,He,document.createElement("button"));t.className="altText";const e=await As._l10nPromise.get("pdfjs-editor-alt-text-button-label");t.textContent=e,t.setAttribute("aria-label",e),t.tabIndex="0",t.addEventListener("contextmenu",de),t.addEventListener("pointerdown",i=>i.stopPropagation());const s=i=>{i.preventDefault(),r(this,Ls)._uiManager.editAltText(r(this,Ls))};return t.addEventListener("click",s,{capture:!0}),t.addEventListener("keydown",i=>{i.target===t&&i.key==="Enter"&&(g(this,ar,!0),s(i))}),await m(this,Ga,Zh).call(this),t}finish(){r(this,He)&&(r(this,He).focus({focusVisible:r(this,ar)}),g(this,ar,!1))}isEmpty(){return!r(this,Ts)&&!r(this,Rs)}get data(){return{altText:r(this,Ts),decorative:r(this,Rs)}}set data({altText:t,decorative:e}){r(this,Ts)===t&&r(this,Rs)===e||(g(this,Ts,t),g(this,Rs,e),m(this,Ga,Zh).call(this))}toggle(t=!1){r(this,He)&&(!t&&r(this,Be)&&(clearTimeout(r(this,Be)),g(this,Be,null)),r(this,He).disabled=!t)}destroy(){var t;(t=r(this,He))==null||t.remove(),g(this,He,null),g(this,Ps,null)}};Ts=new WeakMap,Rs=new WeakMap,He=new WeakMap,Ps=new WeakMap,Be=new WeakMap,ar=new WeakMap,Ls=new WeakMap,Ga=new WeakSet,Zh=async function(){var i;const t=r(this,He);if(!t)return;if(!r(this,Ts)&&!r(this,Rs)){t.classList.remove("done"),(i=r(this,Ps))==null||i.remove();return}t.classList.add("done"),As._l10nPromise.get("pdfjs-editor-alt-text-edit-button-label").then(n=>{t.setAttribute("aria-label",n)});let e=r(this,Ps);if(!e){g(this,Ps,e=document.createElement("span")),e.className="tooltip",e.setAttribute("role","tooltip");const n=e.id=`alt-text-tooltip-${r(this,Ls).id}`;t.setAttribute("aria-describedby",n);const a=100;t.addEventListener("mouseenter",()=>{g(this,Be,setTimeout(()=>{g(this,Be,null),r(this,Ps).classList.add("show"),r(this,Ls)._reportTelemetry({action:"alt_text_tooltip"})},a))}),t.addEventListener("mouseleave",()=>{var o;r(this,Be)&&(clearTimeout(r(this,Be)),g(this,Be,null)),(o=r(this,Ps))==null||o.classList.remove("show")})}e.innerText=r(this,Rs)?await As._l10nPromise.get("pdfjs-editor-alt-text-decorative-tooltip"):r(this,Ts),e.parentNode||t.append(e);const s=r(this,Ls).getImageForAltText();s==null||s.setAttribute("aria-describedby",e.id)},V(As,"_l10nPromise",null);let Ul=As;var Ue,Qt,or,Wi,Nt,qi,lr,hr,Vt,Va,Xi,li,Wa,Yi,ks,ss,cr,dr,Te,qa,th,X,tc,Xa,ec,sc,gu,mu,ic,nc,rc,bu,Au,vu,yu,ac,aa;const lt=class lt{constructor(t){A(this,X);A(this,Ue,null);A(this,Qt,null);A(this,or,!1);A(this,Wi,!1);A(this,Nt,null);A(this,qi,null);A(this,lr,this.focusin.bind(this));A(this,hr,this.focusout.bind(this));A(this,Vt,null);A(this,Va,"");A(this,Xi,!1);A(this,li,null);A(this,Wa,!1);A(this,Yi,!1);A(this,ks,!1);A(this,ss,null);A(this,cr,0);A(this,dr,0);A(this,Te,null);V(this,"_initialOptions",Object.create(null));V(this,"_isVisible",!0);V(this,"_uiManager",null);V(this,"_focusEventsAllowed",!0);V(this,"_l10nPromise",null);A(this,qa,!1);A(this,th,lt._zIndex++);this.constructor===lt&&at("Cannot initialize AnnotationEditor."),this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:n,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[n,a];const[o,l]=this.parentDimensions;this.x=t.x/o,this.y=t.y/l,this.isAttachedToDOM=!1,this.deleted=!1}static get _resizerKeyboardManager(){const t=lt.prototype._resizeWithKeyboard,e=In.TRANSLATE_SMALL,s=In.TRANSLATE_BIG;return J(this,"_resizerKeyboardManager",new el([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],lt.prototype._stopResizingWithKeyboard]]))}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return J(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new Bp({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e,s){if(lt._l10nPromise||(lt._l10nPromise=new Map(["pdfjs-editor-alt-text-button-label","pdfjs-editor-alt-text-edit-button-label","pdfjs-editor-alt-text-decorative-tooltip","pdfjs-editor-resizer-label-topLeft","pdfjs-editor-resizer-label-topMiddle","pdfjs-editor-resizer-label-topRight","pdfjs-editor-resizer-label-middleRight","pdfjs-editor-resizer-label-bottomRight","pdfjs-editor-resizer-label-bottomMiddle","pdfjs-editor-resizer-label-bottomLeft","pdfjs-editor-resizer-label-middleLeft"].map(n=>[n,t.get(n.replaceAll(/([A-Z])/g,a=>`-${a.toLowerCase()}`))]))),s!=null&&s.strings)for(const n of s.strings)lt._l10nPromise.set(n,t.get(n));if(lt._borderLineWidth!==-1)return;const i=getComputedStyle(document.documentElement);lt._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){at("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return r(this,qa)}set _isDraggable(t){var e;g(this,qa,t),(e=this.div)==null||e.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(t*2),this.y+=this.width*t/(e*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(t*2),this.y-=this.width*t/(e*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=r(this,th)}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):m(this,X,aa).call(this),this.parent=t}focusin(t){this._focusEventsAllowed&&(r(this,Xi)?g(this,Xi,!1):this.parent.setSelected(this))}focusout(t){var s;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const e=t.relatedTarget;e!=null&&e.closest(`#${this.id}`)||(t.preventDefault(),(s=this.parent)!=null&&s.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[n,a]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/n,this.y=(e+i)/a,this.fixAndSetPosition()}translate(t,e){m(this,X,tc).call(this,this.parentDimensions,t,e)}translateInPage(t,e){r(this,li)||g(this,li,[this.x,this.y]),m(this,X,tc).call(this,this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}drag(t,e){r(this,li)||g(this,li,[this.x,this.y]);const[s,i]=this.parentDimensions;if(this.x+=t/s,this.y+=e/i,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:c,y:d}=this.div.getBoundingClientRect();this.parent.findNewParent(this,c,d)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:n,y:a}=this;const[o,l]=this.getBaseTranslation();n+=o,a+=l,this.div.style.left=`${(100*n).toFixed(2)}%`,this.div.style.top=`${(100*a).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}get _hasBeenMoved(){return!!r(this,li)&&(r(this,li)[0]!==this.x||r(this,li)[1]!==this.y)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=lt,i=s/t,n=s/e;switch(this.rotation){case 90:return[-i,n];case 180:return[i,n];case 270:return[i,-n];default:return[-i,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const[e,s]=this.pageDimensions;let{x:i,y:n,width:a,height:o}=this;if(a*=e,o*=s,i*=e,n*=s,this._mustFixPosition)switch(t){case 0:i=Math.max(0,Math.min(e-a,i)),n=Math.max(0,Math.min(s-o,n));break;case 90:i=Math.max(0,Math.min(e-o,i)),n=Math.min(s,Math.max(a,n));break;case 180:i=Math.min(e,Math.max(a,i)),n=Math.min(s,Math.max(o,n));break;case 270:i=Math.min(e,Math.max(o,i)),n=Math.max(0,Math.min(s-a,n));break}this.x=i/=e,this.y=n/=s;const[l,c]=this.getBaseTranslation();i+=l,n+=c;const{style:d}=this.div;d.left=`${(100*i).toFixed(2)}%`,d.top=`${(100*n).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(t,e){var s;return m(s=lt,Xa,ec).call(s,t,e,this.parentRotation)}pageTranslationToScreen(t,e){var s;return m(s=lt,Xa,ec).call(s,t,e,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this,i=e*t,n=s*t;return he.isCSSRoundSupported?[Math.round(i),Math.round(n)]:[i,n]}setDims(t,e){const[s,i]=this.parentDimensions;this.div.style.width=`${(100*t/s).toFixed(2)}%`,r(this,Wi)||(this.div.style.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),n=!r(this,Wi)&&e.endsWith("%");if(i&&n)return;const[a,o]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/a).toFixed(2)}%`),!r(this,Wi)&&!n&&(t.height=`${(100*parseFloat(e)/o).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}altTextFinish(){var t;(t=r(this,Qt))==null||t.finish()}async addEditToolbar(){return r(this,Vt)||r(this,Yi)?r(this,Vt):(g(this,Vt,new jh(this)),this.div.append(r(this,Vt).render()),r(this,Qt)&&r(this,Vt).addAltTextButton(await r(this,Qt).render()),r(this,Vt))}removeEditToolbar(){var t;r(this,Vt)&&(r(this,Vt).remove(),g(this,Vt,null),(t=r(this,Qt))==null||t.destroy())}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){r(this,Qt)||(Ul.initialize(lt._l10nPromise),g(this,Qt,new Ul(this)),await this.addEditToolbar())}get altTextData(){var t;return(t=r(this,Qt))==null?void 0:t.data}set altTextData(t){r(this,Qt)&&(r(this,Qt).data=t)}hasAltText(){var t;return!((t=r(this,Qt))!=null&&t.isEmpty())}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.tabIndex=r(this,or)?-1:0,this._isVisible||this.div.classList.add("hidden"),this.setInForeground(),this.div.addEventListener("focusin",r(this,lr)),this.div.addEventListener("focusout",r(this,hr));const[t,e]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`,this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`);const[s,i]=this.getInitialTranslation();return this.translate(s,i),Bl(this,this.div,["pointerdown"]),this.div}pointerdown(t){const{isMac:e}=he.platform;if(t.button!==0||t.ctrlKey&&e){t.preventDefault();return}if(g(this,Xi,!0),this._isDraggable){m(this,X,bu).call(this,t);return}m(this,X,rc).call(this,t)}moveInDOM(){r(this,ss)&&clearTimeout(r(this,ss)),g(this,ss,setTimeout(()=>{var t;g(this,ss,null),(t=this.parent)==null||t.moveEditorInDOM(this)},0))}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition()}getRect(t,e,s=this.rotation){const i=this.parentScale,[n,a]=this.pageDimensions,[o,l]=this.pageTranslation,c=t/i,d=e/i,u=this.x*n,f=this.y*a,p=this.width*n,b=this.height*a;switch(s){case 0:return[u+c+o,a-f-d-b+l,u+c+p+o,a-f-d+l];case 90:return[u+d+o,a-f+c+l,u+d+b+o,a-f+c+p+l];case 180:return[u-c-p+o,a-f+d+l,u-c+o,a-f+d+b+l];case 270:return[u-d-b+o,a-f-c-p+l,u-d+o,a-f-c+l];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,n,a]=t,o=n-s,l=a-i;switch(this.rotation){case 0:return[s,e-a,o,l];case 90:return[s,e-i,l,o];case 180:return[n,e-i,o,l];case 270:return[n,e-a,l,o];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){g(this,Yi,!0)}disableEditMode(){g(this,Yi,!1)}isInEditMode(){return r(this,Yi)}shouldGetKeyboardEvents(){return r(this,ks)}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var t,e;(t=this.div)==null||t.addEventListener("focusin",r(this,lr)),(e=this.div)==null||e.addEventListener("focusout",r(this,hr))}rotate(t){}serialize(t=!1,e=null){at("An editor must be serializable")}static deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s});i.rotation=t.rotation;const[n,a]=i.pageDimensions,[o,l,c,d]=i.getRectInCurrentCoords(t.rect,a);return i.x=o/n,i.y=l/a,i.width=c/n,i.height=d/a,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){if(this.div.removeEventListener("focusin",r(this,lr)),this.div.removeEventListener("focusout",r(this,hr)),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),r(this,ss)&&(clearTimeout(r(this,ss)),g(this,ss,null)),m(this,X,aa).call(this),this.removeEditToolbar(),r(this,Te)){for(const t of r(this,Te).values())clearTimeout(t);g(this,Te,null)}this.parent=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(m(this,X,gu).call(this),r(this,Nt).classList.remove("hidden"),Bl(this,this.div,["keydown"]))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),g(this,qi,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const e=r(this,Nt).children;if(!r(this,Ue)){g(this,Ue,Array.from(e));const a=m(this,X,Au).bind(this),o=m(this,X,vu).bind(this);for(const l of r(this,Ue)){const c=l.getAttribute("data-resizer-name");l.setAttribute("role","spinbutton"),l.addEventListener("keydown",a),l.addEventListener("blur",o),l.addEventListener("focus",m(this,X,yu).bind(this,c)),lt._l10nPromise.get(`pdfjs-editor-resizer-label-${c}`).then(d=>l.setAttribute("aria-label",d))}}const s=r(this,Ue)[0];let i=0;for(const a of e){if(a===s)break;i++}const n=(360-this.rotation+this.parentRotation)%360/90*(r(this,Ue).length/4);if(n!==i){if(n<i)for(let o=0;o<i-n;o++)r(this,Nt).append(r(this,Nt).firstChild);else if(n>i)for(let o=0;o<n-i;o++)r(this,Nt).firstChild.before(r(this,Nt).lastChild);let a=0;for(const o of e){const c=r(this,Ue)[a++].getAttribute("data-resizer-name");lt._l10nPromise.get(`pdfjs-editor-resizer-label-${c}`).then(d=>o.setAttribute("aria-label",d))}}m(this,X,ac).call(this,0),g(this,ks,!0),r(this,Nt).firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}_resizeWithKeyboard(t,e){r(this,ks)&&m(this,X,nc).call(this,r(this,Va),{movementX:t,movementY:e})}_stopResizingWithKeyboard(){m(this,X,aa).call(this),this.div.focus()}select(){var t,e;if(this.makeResizable(),(t=this.div)==null||t.classList.add("selectedEditor"),!r(this,Vt)){this.addEditToolbar().then(()=>{var s,i;(s=this.div)!=null&&s.classList.contains("selectedEditor")&&((i=r(this,Vt))==null||i.show())});return}(e=r(this,Vt))==null||e.show()}unselect(){var t,e,s,i;(t=r(this,Nt))==null||t.classList.add("hidden"),(e=this.div)==null||e.classList.remove("selectedEditor"),(s=this.div)!=null&&s.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),(i=r(this,Vt))==null||i.hide()}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return r(this,Wa)}set isEditing(t){g(this,Wa,t),this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){g(this,Wi,!0);const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){r(this,Te)||g(this,Te,new Map);const{action:s}=t;let i=r(this,Te).get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),r(this,Te).delete(s),r(this,Te).size===0&&g(this,Te,null)},lt._telemetryTimeout),r(this,Te).set(s,i);return}t.type||(t.type=this.editorType),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),g(this,or,!1)}disable(){this.div&&(this.div.tabIndex=-1),g(this,or,!0)}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(!e)e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);else if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;e.nodeName==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}};Ue=new WeakMap,Qt=new WeakMap,or=new WeakMap,Wi=new WeakMap,Nt=new WeakMap,qi=new WeakMap,lr=new WeakMap,hr=new WeakMap,Vt=new WeakMap,Va=new WeakMap,Xi=new WeakMap,li=new WeakMap,Wa=new WeakMap,Yi=new WeakMap,ks=new WeakMap,ss=new WeakMap,cr=new WeakMap,dr=new WeakMap,Te=new WeakMap,qa=new WeakMap,th=new WeakMap,X=new WeakSet,tc=function([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this.fixAndSetPosition()},Xa=new WeakSet,ec=function(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}},sc=function(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}},gu=function(){if(r(this,Nt))return;g(this,Nt,document.createElement("div")),r(this,Nt).classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"];for(const e of t){const s=document.createElement("div");r(this,Nt).append(s),s.classList.add("resizer",e),s.setAttribute("data-resizer-name",e),s.addEventListener("pointerdown",m(this,X,mu).bind(this,e)),s.addEventListener("contextmenu",de),s.tabIndex=-1}this.div.prepend(r(this,Nt))},mu=function(t,e){var b;e.preventDefault();const{isMac:s}=he.platform;if(e.button!==0||e.ctrlKey&&s)return;(b=r(this,Qt))==null||b.toggle(!1);const i=m(this,X,nc).bind(this,t),n=this._isDraggable;this._isDraggable=!1;const a={passive:!0,capture:!0};this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",i,a),window.addEventListener("contextmenu",de);const o=this.x,l=this.y,c=this.width,d=this.height,u=this.parent.div.style.cursor,f=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const p=()=>{var v;this.parent.togglePointerEvents(!0),(v=r(this,Qt))==null||v.toggle(!0),this._isDraggable=n,window.removeEventListener("pointerup",p),window.removeEventListener("blur",p),window.removeEventListener("pointermove",i,a),window.removeEventListener("contextmenu",de),this.parent.div.style.cursor=u,this.div.style.cursor=f,m(this,X,ic).call(this,o,l,c,d)};window.addEventListener("pointerup",p),window.addEventListener("blur",p)},ic=function(t,e,s,i){const n=this.x,a=this.y,o=this.width,l=this.height;n===t&&a===e&&o===s&&l===i||this.addCommands({cmd:()=>{this.width=o,this.height=l,this.x=n,this.y=a;const[c,d]=this.parentDimensions;this.setDims(c*o,d*l),this.fixAndSetPosition()},undo:()=>{this.width=s,this.height=i,this.x=t,this.y=e;const[c,d]=this.parentDimensions;this.setDims(c*s,d*i),this.fixAndSetPosition()},mustExec:!0})},nc=function(t,e){const[s,i]=this.parentDimensions,n=this.x,a=this.y,o=this.width,l=this.height,c=lt.MIN_SIZE/s,d=lt.MIN_SIZE/i,u=M=>Math.round(M*1e4)/1e4,f=m(this,X,sc).call(this,this.rotation),p=(M,U)=>[f[0]*M+f[2]*U,f[1]*M+f[3]*U],b=m(this,X,sc).call(this,360-this.rotation),v=(M,U)=>[b[0]*M+b[2]*U,b[1]*M+b[3]*U];let _,y,E=!1,w=!1;switch(t){case"topLeft":E=!0,_=(M,U)=>[0,0],y=(M,U)=>[M,U];break;case"topMiddle":_=(M,U)=>[M/2,0],y=(M,U)=>[M/2,U];break;case"topRight":E=!0,_=(M,U)=>[M,0],y=(M,U)=>[0,U];break;case"middleRight":w=!0,_=(M,U)=>[M,U/2],y=(M,U)=>[0,U/2];break;case"bottomRight":E=!0,_=(M,U)=>[M,U],y=(M,U)=>[0,0];break;case"bottomMiddle":_=(M,U)=>[M/2,U],y=(M,U)=>[M/2,0];break;case"bottomLeft":E=!0,_=(M,U)=>[0,U],y=(M,U)=>[M,0];break;case"middleLeft":w=!0,_=(M,U)=>[0,U/2],y=(M,U)=>[M,U/2];break}const x=_(o,l),S=y(o,l);let C=p(...S);const R=u(n+C[0]),k=u(a+C[1]);let D=1,H=1,[T,j]=this.screenToPageTranslation(e.movementX,e.movementY);if([T,j]=v(T/s,j/i),E){const M=Math.hypot(o,l);D=H=Math.max(Math.min(Math.hypot(S[0]-x[0]-T,S[1]-x[1]-j)/M,1/o,1/l),c/o,d/l)}else w?D=Math.max(c,Math.min(1,Math.abs(S[0]-x[0]-T)))/o:H=Math.max(d,Math.min(1,Math.abs(S[1]-x[1]-j)))/l;const O=u(o*D),$=u(l*H);C=p(...y(O,$));const W=R-C[0],Z=k-C[1];this.width=O,this.height=$,this.x=W,this.y=Z,this.setDims(s*O,i*$),this.fixAndSetPosition()},rc=function(t){const{isMac:e}=he.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)},bu=function(t){const e=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let s,i;e&&(this.div.classList.add("moving"),s={passive:!0,capture:!0},g(this,cr,t.clientX),g(this,dr,t.clientY),i=a=>{const{clientX:o,clientY:l}=a,[c,d]=this.screenToPageTranslation(o-r(this,cr),l-r(this,dr));g(this,cr,o),g(this,dr,l),this._uiManager.dragSelectedEditors(c,d)},window.addEventListener("pointermove",i,s));const n=()=>{window.removeEventListener("pointerup",n),window.removeEventListener("blur",n),e&&(this.div.classList.remove("moving"),window.removeEventListener("pointermove",i,s)),g(this,Xi,!1),this._uiManager.endDragSession()||m(this,X,rc).call(this,t)};window.addEventListener("pointerup",n),window.addEventListener("blur",n)},Au=function(t){lt._resizerKeyboardManager.exec(this,t)},vu=function(t){var e;r(this,ks)&&((e=t.relatedTarget)==null?void 0:e.parentNode)!==r(this,Nt)&&m(this,X,aa).call(this)},yu=function(t){g(this,Va,r(this,ks)?t:"")},ac=function(t){if(r(this,Ue))for(const e of r(this,Ue))e.tabIndex=t},aa=function(){if(g(this,ks,!1),m(this,X,ac).call(this,-1),r(this,qi)){const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=r(this,qi);m(this,X,ic).call(this,t,e,s,i),g(this,qi,null)}},A(lt,Xa),V(lt,"_borderLineWidth",-1),V(lt,"_colorManager",new Vh),V(lt,"_zIndex",1),V(lt,"_telemetryTimeout",1e3);let nt=lt;class Bp extends nt{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}const Ad=3285377520,xe=4294901760,Ye=65535;class _u{constructor(t){this.h1=t?t&4294967295:Ad,this.h2=t?t&4294967295:Ad}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(t.length*2),s=0;for(let v=0,_=t.length;v<_;v++){const y=t.charCodeAt(v);y<=255?e[s++]=y:(e[s++]=y>>>8,e[s++]=y&255)}}else if(ArrayBuffer.isView(t))e=t.slice(),s=e.byteLength;else throw new Error("Invalid data format, must be a string or TypedArray.");const i=s>>2,n=s-i*4,a=new Uint32Array(e.buffer,0,i);let o=0,l=0,c=this.h1,d=this.h2;const u=3432918353,f=461845907,p=u&Ye,b=f&Ye;for(let v=0;v<i;v++)v&1?(o=a[v],o=o*u&xe|o*p&Ye,o=o<<15|o>>>17,o=o*f&xe|o*b&Ye,c^=o,c=c<<13|c>>>19,c=c*5+3864292196):(l=a[v],l=l*u&xe|l*p&Ye,l=l<<15|l>>>17,l=l*f&xe|l*b&Ye,d^=l,d=d<<13|d>>>19,d=d*5+3864292196);switch(o=0,n){case 3:o^=e[i*4+2]<<16;case 2:o^=e[i*4+1]<<8;case 1:o^=e[i*4],o=o*u&xe|o*p&Ye,o=o<<15|o>>>17,o=o*f&xe|o*b&Ye,i&1?c^=o:d^=o}this.h1=c,this.h2=d}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=t*3981806797&xe|t*36045&Ye,e=e*4283543511&xe|((e<<16|t>>>16)*2950163797&xe)>>>16,t^=e>>>1,t=t*444984403&xe|t*60499&Ye,e=e*3301882366&xe|((e<<16|t>>>16)*3120437893&xe)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const oc=Object.freeze({map:null,hash:"",transfer:void 0});var Ki,Ht,eh,Eu;class od{constructor(){A(this,eh);A(this,Ki,!1);A(this,Ht,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=r(this,Ht).get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return r(this,Ht).get(t)}remove(t){if(r(this,Ht).delete(t),r(this,Ht).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of r(this,Ht).values())if(e instanceof nt)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=r(this,Ht).get(t);let i=!1;if(s!==void 0)for(const[n,a]of Object.entries(e))s[n]!==a&&(i=!0,s[n]=a);else i=!0,r(this,Ht).set(t,e);i&&m(this,eh,Eu).call(this),e instanceof nt&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return r(this,Ht).has(t)}getAll(){return r(this,Ht).size>0?td(r(this,Ht)):null}setAll(t){for(const[e,s]of Object.entries(t))this.setValue(e,s)}get size(){return r(this,Ht).size}resetModified(){r(this,Ki)&&(g(this,Ki,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new wu(this)}get serializable(){if(r(this,Ht).size===0)return oc;const t=new Map,e=new _u,s=[],i=Object.create(null);let n=!1;for(const[a,o]of r(this,Ht)){const l=o instanceof nt?o.serialize(!1,i):o;l&&(t.set(a,l),e.update(`${a}:${JSON.stringify(l)}`),n||(n=!!l.bitmap))}if(n)for(const a of t.values())a.bitmap&&s.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:oc}get editorStats(){let t=null;const e=new Map;for(const s of r(this,Ht).values()){if(!(s instanceof nt))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:n}=i;e.has(n)||e.set(n,Object.getPrototypeOf(s).constructor),t||(t=Object.create(null));const a=t[n]||(t[n]=new Map);for(const[o,l]of Object.entries(i)){if(o==="type")continue;let c=a.get(o);c||(c=new Map,a.set(o,c));const d=c.get(l)??0;c.set(l,d+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}}Ki=new WeakMap,Ht=new WeakMap,eh=new WeakSet,Eu=function(){r(this,Ki)||(g(this,Ki,!0),typeof this.onSetModified=="function"&&this.onSetModified())};var Ya;class wu extends od{constructor(e){super();A(this,Ya);const{map:s,hash:i,transfer:n}=e.serializable,a=structuredClone(s,n?{transfer:n}:null);g(this,Ya,{map:a,hash:i,transfer:n})}get print(){at("Should not call PrintAnnotationStorage.print")}get serializable(){return r(this,Ya)}}Ya=new WeakMap;var ur;class Up{constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){A(this,ur,new Set);this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),r(this,ur).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(!(!t||r(this,ur).has(t.loadedName))){if(Lt(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:s,src:i,style:n}=t,a=new FontFace(s,i,n);this.addNativeFontFace(a);try{await a.load(),r(this,ur).add(s),e==null||e(t)}catch{q(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}return}at("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{await s.loaded}catch(i){throw q(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){var e;const t=!!((e=this._document)!=null&&e.fonts);return J(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){let t=!1;return(qt||typeof navigator<"u"&&typeof(navigator==null?void 0:navigator.userAgent)=="string"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0),J(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){function e(){for(Lt(!i.done,"completeRequest() cannot be called twice."),i.done=!0;s.length>0&&s[0].done;){const n=s.shift();setTimeout(n.callback,0)}}const{loadingRequests:s}=this,i={done:!1,complete:e,callback:t};return s.push(i),i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return J(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function s(x,S){return x.charCodeAt(S)<<24|x.charCodeAt(S+1)<<16|x.charCodeAt(S+2)<<8|x.charCodeAt(S+3)&255}function i(x,S,C,R){const k=x.substring(0,S),D=x.substring(S+C);return k+R+D}let n,a;const o=this._document.createElement("canvas");o.width=1,o.height=1;const l=o.getContext("2d");let c=0;function d(x,S){if(++c>30){q("Load test font never loaded."),S();return}if(l.font="30px "+x,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0){S();return}setTimeout(d.bind(null,x,S))}const u=`lt${Date.now()}${this.loadTestFontId++}`;let f=this._loadTestFont;f=i(f,976,u.length,u);const b=16,v=1482184792;let _=s(f,b);for(n=0,a=u.length-3;n<a;n+=4)_=_-v+s(u,n)|0;n<u.length&&(_=_-v+s(u+"XXX",n)|0),f=i(f,b,4,wp(_));const y=`url(data:font/opentype;base64,${btoa(f)});`,E=`@font-face {font-family:"${u}";src:${y}}`;this.insertRule(E);const w=this._document.createElement("div");w.style.visibility="hidden",w.style.width=w.style.height="10px",w.style.position="absolute",w.style.top=w.style.left="0px";for(const x of[t.loadedName,u]){const S=this._document.createElement("span");S.textContent="Hi",S.style.fontFamily=x,w.append(S)}this._document.body.append(w),d(u,()=>{w.remove(),e.complete()})}}ur=new WeakMap;class jp{constructor(t,{disableFontFace:e=!1,inspectFont:s=null}){this.compiledGlyphs=Object.create(null);for(const i in t)this[i]=t[i];this.disableFontFace=e===!0,this._inspectFont=s}createNativeFontFace(){var e;if(!this.data||this.disableFontFace)return null;let t;if(!this.cssFontInfo)t=new FontFace(this.loadedName,this.data,{});else{const s={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(s.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,s)}return(e=this._inspectFont)==null||e.call(this,this),t}createFontFaceRule(){var i;if(!this.data||this.disableFontFace)return null;const t=Wd(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let s;if(!this.cssFontInfo)s=`@font-face {font-family:"${this.loadedName}";src:${e}}`;else{let n=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(n+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),s=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${n}src:${e}}`}return(i=this._inspectFont)==null||i.call(this,this,e),s}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];let s;try{s=t.get(this.loadedName+"_path_"+e)}catch(n){q(`getPathGenerator - ignoring character: "${n}".`)}if(!Array.isArray(s)||s.length===0)return this.compiledGlyphs[e]=function(n,a){};const i=[];for(let n=0,a=s.length;n<a;)switch(s[n++]){case gs.BEZIER_CURVE_TO:{const[o,l,c,d,u,f]=s.slice(n,n+6);i.push(p=>p.bezierCurveTo(o,l,c,d,u,f)),n+=6}break;case gs.MOVE_TO:{const[o,l]=s.slice(n,n+2);i.push(c=>c.moveTo(o,l)),n+=2}break;case gs.LINE_TO:{const[o,l]=s.slice(n,n+2);i.push(c=>c.lineTo(o,l)),n+=2}break;case gs.QUADRATIC_CURVE_TO:{const[o,l,c,d]=s.slice(n,n+4);i.push(u=>u.quadraticCurveTo(o,l,c,d)),n+=4}break;case gs.RESTORE:i.push(o=>o.restore());break;case gs.SAVE:i.push(o=>o.save());break;case gs.SCALE:Lt(i.length===2,"Scale command is only valid at the third position.");break;case gs.TRANSFORM:{const[o,l,c,d,u,f]=s.slice(n,n+6);i.push(p=>p.transform(o,l,c,d,u,f)),n+=6}break;case gs.TRANSLATE:{const[o,l]=s.slice(n,n+2);i.push(c=>c.translate(o,l)),n+=2}break}return this.compiledGlyphs[e]=function(a,o){i[0](a),i[1](a),a.scale(o,-o);for(let l=2,c=i.length;l<c;l++)i[l](a)}}}if(qt){var lc=Promise.withResolvers(),ba=null;(async()=>{const t=await ol(()=>import("./__vite-browser-external-BIHI7g3E.js"),[]),e=await ol(()=>import("./__vite-browser-external-BIHI7g3E.js"),[]),s=await ol(()=>import("./__vite-browser-external-BIHI7g3E.js"),[]),i=await ol(()=>import("./__vite-browser-external-BIHI7g3E.js"),[]);let n,a;return new Map(Object.entries({fs:t,http:e,https:s,url:i,canvas:n,path2d:a}))})().then(t=>{ba=t,lc.resolve()},t=>{q(`loadPackages: ${t}`),ba=new Map,lc.resolve()})}class ps{static get promise(){return lc.promise}static get(t){return ba==null?void 0:ba.get(t)}}const Su=function(h){return ps.get("fs").promises.readFile(h).then(e=>new Uint8Array(e))};class $p extends bh{}class zp extends Ah{_createCanvas(t,e){return ps.get("canvas").createCanvas(t,e)}}class Gp extends vh{_fetchData(t,e){return Su(t).then(s=>({cMapData:s,compressionType:e}))}}class Vp extends yh{_fetchData(t){return Su(t)}}const te={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function hc(h,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),h.clip(i)}class sl{constructor(){this.constructor===sl&&at("Cannot initialize BaseShadingPattern.")}getPattern(){at("Abstract method `getPattern` called.")}}class Wp extends sl{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let n;if(i===te.STROKE||i===te.FILL){const a=e.current.getClippedPathBoundingBox(i,mt(t))||[0,0,0,0],o=Math.ceil(a[2]-a[0])||1,l=Math.ceil(a[3]-a[1])||1,c=e.cachedCanvases.getCanvas("pattern",o,l,!0),d=c.context;d.clearRect(0,0,d.canvas.width,d.canvas.height),d.beginPath(),d.rect(0,0,d.canvas.width,d.canvas.height),d.translate(-a[0],-a[1]),s=F.transform(s,[1,0,0,1,a[0],a[1]]),d.transform(...e.baseTransform),this.matrix&&d.transform(...this.matrix),hc(d,this._bbox),d.fillStyle=this._createGradient(d),d.fill(),n=t.createPattern(c.canvas,"no-repeat");const u=new DOMMatrix(s);n.setTransform(u)}else hc(t,this._bbox),n=this._createGradient(t);return n}}function kh(h,t,e,s,i,n,a,o){const l=t.coords,c=t.colors,d=h.data,u=h.width*4;let f;l[e+1]>l[s+1]&&(f=e,e=s,s=f,f=n,n=a,a=f),l[s+1]>l[i+1]&&(f=s,s=i,i=f,f=a,a=o,o=f),l[e+1]>l[s+1]&&(f=e,e=s,s=f,f=n,n=a,a=f);const p=(l[e]+t.offsetX)*t.scaleX,b=(l[e+1]+t.offsetY)*t.scaleY,v=(l[s]+t.offsetX)*t.scaleX,_=(l[s+1]+t.offsetY)*t.scaleY,y=(l[i]+t.offsetX)*t.scaleX,E=(l[i+1]+t.offsetY)*t.scaleY;if(b>=E)return;const w=c[n],x=c[n+1],S=c[n+2],C=c[a],R=c[a+1],k=c[a+2],D=c[o],H=c[o+1],T=c[o+2],j=Math.round(b),O=Math.round(E);let $,W,Z,M,U,Xt,Se,ue;for(let vt=j;vt<=O;vt++){if(vt<_){const et=vt<b?0:(b-vt)/(b-_);$=p-(p-v)*et,W=w-(w-C)*et,Z=x-(x-R)*et,M=S-(S-k)*et}else{let et;vt>E?et=1:_===E?et=0:et=(_-vt)/(_-E),$=v-(v-y)*et,W=C-(C-D)*et,Z=R-(R-H)*et,M=k-(k-T)*et}let pt;vt<b?pt=0:vt>E?pt=1:pt=(b-vt)/(b-E),U=p-(p-y)*pt,Xt=w-(w-D)*pt,Se=x-(x-H)*pt,ue=S-(S-T)*pt;const $t=Math.round(Math.min($,U)),Ie=Math.round(Math.max($,U));let ie=u*vt+$t*4;for(let et=$t;et<=Ie;et++)pt=($-et)/($-U),pt<0?pt=0:pt>1&&(pt=1),d[ie++]=W-(W-Xt)*pt|0,d[ie++]=Z-(Z-Se)*pt|0,d[ie++]=M-(M-ue)*pt|0,d[ie++]=255}}function qp(h,t,e){const s=t.coords,i=t.colors;let n,a;switch(t.type){case"lattice":const o=t.verticesPerRow,l=Math.floor(s.length/o)-1,c=o-1;for(n=0;n<l;n++){let d=n*o;for(let u=0;u<c;u++,d++)kh(h,e,s[d],s[d+1],s[d+o],i[d],i[d+1],i[d+o]),kh(h,e,s[d+o+1],s[d+1],s[d+o],i[d+o+1],i[d+1],i[d+o])}break;case"triangles":for(n=0,a=s.length;n<a;n+=3)kh(h,e,s[n],s[n+1],s[n+2],i[n],i[n+1],i[n+2]);break;default:throw new Error("illegal figure")}}class Xp extends sl{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,s){const o=Math.floor(this._bounds[0]),l=Math.floor(this._bounds[1]),c=Math.ceil(this._bounds[2])-o,d=Math.ceil(this._bounds[3])-l,u=Math.min(Math.ceil(Math.abs(c*t[0]*1.1)),3e3),f=Math.min(Math.ceil(Math.abs(d*t[1]*1.1)),3e3),p=c/u,b=d/f,v={coords:this._coords,colors:this._colors,offsetX:-o,offsetY:-l,scaleX:1/p,scaleY:1/b},_=u+2*2,y=f+2*2,E=s.getCanvas("mesh",_,y,!1),w=E.context,x=w.createImageData(u,f);if(e){const C=x.data;for(let R=0,k=C.length;R<k;R+=4)C[R]=e[0],C[R+1]=e[1],C[R+2]=e[2],C[R+3]=255}for(const C of this._figures)qp(x,C,v);return w.putImageData(x,2,2),{canvas:E.canvas,offsetX:o-2*p,offsetY:l-2*b,scaleX:p,scaleY:b}}getPattern(t,e,s,i){hc(t,this._bbox);let n;if(i===te.SHADING)n=F.singularValueDecompose2dScale(mt(t));else if(n=F.singularValueDecompose2dScale(e.baseTransform),this.matrix){const o=F.singularValueDecompose2dScale(this.matrix);n=[n[0]*o[0],n[1]*o[1]]}const a=this._createMeshCanvas(n,i===te.SHADING?null:this._background,e.cachedCanvases);return i!==te.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class Yp extends sl{getPattern(){return"hotpink"}}function Kp(h){switch(h[0]){case"RadialAxial":return new Wp(h);case"Mesh":return new Xp(h);case"Dummy":return new Yp}throw new Error(`Unknown IR type: ${h[0]}`)}const vd={COLORED:1,UNCOLORED:2},sh=class sh{constructor(t,e,s,i,n){this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=s,this.canvasGraphicsFactory=i,this.baseTransform=n}createPatternCanvas(t){const e=this.operatorList,s=this.bbox,i=this.xstep,n=this.ystep,a=this.paintType,o=this.tilingType,l=this.color,c=this.canvasGraphicsFactory;ph("TilingType: "+o);const d=s[0],u=s[1],f=s[2],p=s[3],b=F.singularValueDecompose2dScale(this.matrix),v=F.singularValueDecompose2dScale(this.baseTransform),_=[b[0]*v[0],b[1]*v[1]],y=this.getSizeAndScale(i,this.ctx.canvas.width,_[0]),E=this.getSizeAndScale(n,this.ctx.canvas.height,_[1]),w=t.cachedCanvases.getCanvas("pattern",y.size,E.size,!0),x=w.context,S=c.createCanvasGraphics(x);S.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(S,a,l);let C=d,R=u,k=f,D=p;return d<0&&(C=0,k+=Math.abs(d)),u<0&&(R=0,D+=Math.abs(u)),x.translate(-(y.scale*C),-(E.scale*R)),S.transform(y.scale,0,0,E.scale,0,0),x.save(),this.clipBbox(S,C,R,k,D),S.baseTransform=mt(S.ctx),S.executeOperatorList(e),S.endDrawing(),{canvas:w.canvas,scaleX:y.scale,scaleY:E.scale,offsetX:C,offsetY:R}}getSizeAndScale(t,e,s){t=Math.abs(t);const i=Math.max(sh.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*s);return n>=i?n=i:s=n/t,{scale:s,size:n}}clipBbox(t,e,s,i,n){const a=i-e,o=n-s;t.ctx.rect(e,s,a,o),t.current.updateRectMinMax(mt(t.ctx),[e,s,i,n]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,n=t.current;switch(e){case vd.COLORED:const a=this.ctx;i.fillStyle=a.fillStyle,i.strokeStyle=a.strokeStyle,n.fillColor=a.fillStyle,n.strokeColor=a.strokeStyle;break;case vd.UNCOLORED:const o=F.makeHexColor(s[0],s[1],s[2]);i.fillStyle=o,i.strokeStyle=o,n.fillColor=o,n.strokeColor=o;break;default:throw new Ep(`Unsupported paint type: ${e}`)}}getPattern(t,e,s,i){let n=s;i!==te.SHADING&&(n=F.transform(n,e.baseTransform),this.matrix&&(n=F.transform(n,this.matrix)));const a=this.createPatternCanvas(e);let o=new DOMMatrix(n);o=o.translate(a.offsetX,a.offsetY),o=o.scale(1/a.scaleX,1/a.scaleY);const l=t.createPattern(a.canvas,"repeat");return l.setTransform(o),l}};V(sh,"MAX_PATTERN_SIZE",3e3);let cc=sh;function Qp({src:h,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:n=4294967295,inverseDecode:a=!1}){const o=he.isLittleEndian?4278190080:255,[l,c]=a?[n,o]:[o,n],d=s>>3,u=s&7,f=h.length;e=new Uint32Array(e.buffer);let p=0;for(let b=0;b<i;b++){for(const _=t+d;t<_;t++){const y=t<f?h[t]:255;e[p++]=y&128?c:l,e[p++]=y&64?c:l,e[p++]=y&32?c:l,e[p++]=y&16?c:l,e[p++]=y&8?c:l,e[p++]=y&4?c:l,e[p++]=y&2?c:l,e[p++]=y&1?c:l}if(u===0)continue;const v=t<f?h[t++]:255;for(let _=0;_<u;_++)e[p++]=v&1<<7-_?c:l}return{srcPos:t,destPos:p}}const yd=16,_d=100,Jp=15,Ed=10,wd=1e3,oe=16;function Zp(h,t){if(h._removeMirroring)throw new Error("Context is already forwarding operations.");h.__originalSave=h.save,h.__originalRestore=h.restore,h.__originalRotate=h.rotate,h.__originalScale=h.scale,h.__originalTranslate=h.translate,h.__originalTransform=h.transform,h.__originalSetTransform=h.setTransform,h.__originalResetTransform=h.resetTransform,h.__originalClip=h.clip,h.__originalMoveTo=h.moveTo,h.__originalLineTo=h.lineTo,h.__originalBezierCurveTo=h.bezierCurveTo,h.__originalRect=h.rect,h.__originalClosePath=h.closePath,h.__originalBeginPath=h.beginPath,h._removeMirroring=()=>{h.save=h.__originalSave,h.restore=h.__originalRestore,h.rotate=h.__originalRotate,h.scale=h.__originalScale,h.translate=h.__originalTranslate,h.transform=h.__originalTransform,h.setTransform=h.__originalSetTransform,h.resetTransform=h.__originalResetTransform,h.clip=h.__originalClip,h.moveTo=h.__originalMoveTo,h.lineTo=h.__originalLineTo,h.bezierCurveTo=h.__originalBezierCurveTo,h.rect=h.__originalRect,h.closePath=h.__originalClosePath,h.beginPath=h.__originalBeginPath,delete h._removeMirroring},h.save=function(){t.save(),this.__originalSave()},h.restore=function(){t.restore(),this.__originalRestore()},h.translate=function(s,i){t.translate(s,i),this.__originalTranslate(s,i)},h.scale=function(s,i){t.scale(s,i),this.__originalScale(s,i)},h.transform=function(s,i,n,a,o,l){t.transform(s,i,n,a,o,l),this.__originalTransform(s,i,n,a,o,l)},h.setTransform=function(s,i,n,a,o,l){t.setTransform(s,i,n,a,o,l),this.__originalSetTransform(s,i,n,a,o,l)},h.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},h.rotate=function(s){t.rotate(s),this.__originalRotate(s)},h.clip=function(s){t.clip(s),this.__originalClip(s)},h.moveTo=function(e,s){t.moveTo(e,s),this.__originalMoveTo(e,s)},h.lineTo=function(e,s){t.lineTo(e,s),this.__originalLineTo(e,s)},h.bezierCurveTo=function(e,s,i,n,a,o){t.bezierCurveTo(e,s,i,n,a,o),this.__originalBezierCurveTo(e,s,i,n,a,o)},h.rect=function(e,s,i,n){t.rect(e,s,i,n),this.__originalRect(e,s,i,n)},h.closePath=function(){t.closePath(),this.__originalClosePath()},h.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}class tg{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function hl(h,t,e,s,i,n,a,o,l,c){const[d,u,f,p,b,v]=mt(h);if(u===0&&f===0){const E=a*d+b,w=Math.round(E),x=o*p+v,S=Math.round(x),C=(a+l)*d+b,R=Math.abs(Math.round(C)-w)||1,k=(o+c)*p+v,D=Math.abs(Math.round(k)-S)||1;return h.setTransform(Math.sign(d),0,0,Math.sign(p),w,S),h.drawImage(t,e,s,i,n,0,0,R,D),h.setTransform(d,u,f,p,b,v),[R,D]}if(d===0&&p===0){const E=o*f+b,w=Math.round(E),x=a*u+v,S=Math.round(x),C=(o+c)*f+b,R=Math.abs(Math.round(C)-w)||1,k=(a+l)*u+v,D=Math.abs(Math.round(k)-S)||1;return h.setTransform(0,Math.sign(u),Math.sign(f),0,w,S),h.drawImage(t,e,s,i,n,0,0,D,R),h.setTransform(d,u,f,p,b,v),[D,R]}h.drawImage(t,e,s,i,n,a,o,l,c);const _=Math.hypot(d,u),y=Math.hypot(f,p);return[_*l,y*c]}function eg(h){const{width:t,height:e}=h;if(t>wd||e>wd)return null;const s=1e3,i=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),n=t+1;let a=new Uint8Array(n*(e+1)),o,l,c;const d=t+7&-8;let u=new Uint8Array(d*e),f=0;for(const y of h.data){let E=128;for(;E>0;)u[f++]=y&E?0:255,E>>=1}let p=0;for(f=0,u[f]!==0&&(a[0]=1,++p),l=1;l<t;l++)u[f]!==u[f+1]&&(a[l]=u[f]?2:1,++p),f++;for(u[f]!==0&&(a[l]=2,++p),o=1;o<e;o++){f=o*d,c=o*n,u[f-d]!==u[f]&&(a[c]=u[f]?1:8,++p);let y=(u[f]?4:0)+(u[f-d]?8:0);for(l=1;l<t;l++)y=(y>>2)+(u[f+1]?4:0)+(u[f-d+1]?8:0),i[y]&&(a[c+l]=i[y],++p),f++;if(u[f-d]!==u[f]&&(a[c+l]=u[f]?2:4,++p),p>s)return null}for(f=d*(e-1),c=o*n,u[f]!==0&&(a[c]=8,++p),l=1;l<t;l++)u[f]!==u[f+1]&&(a[c+l]=u[f]?4:8,++p),f++;if(u[f]!==0&&(a[c+l]=4,++p),p>s)return null;const b=new Int32Array([0,n,-1,0,-n,0,0,0,1]),v=new Path2D;for(o=0;p&&o<=e;o++){let y=o*n;const E=y+t;for(;y<E&&!a[y];)y++;if(y===E)continue;v.moveTo(y%n,o);const w=y;let x=a[y];do{const S=b[x];do y+=S;while(!a[y]);const C=a[y];C!==5&&C!==10?(x=C,a[y]=0):(x=C&51*x>>4,a[y]&=x>>2|x<<2),v.lineTo(y%n,y/n|0),a[y]||--p}while(w!==y);--o}return u=null,a=null,function(y){y.save(),y.scale(1/t,-1/e),y.translate(0,-e),y.fill(v),y.beginPath(),y.restore()}}class Sd{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=Gd,this.textMatrixScale=1,this.fontMatrix=Dh,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=zt.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,s){[e,s]=F.applyTransform([e,s],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,s),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,s)}updateRectMinMax(t,e){const s=F.applyTransform(e,t),i=F.applyTransform(e.slice(2),t),n=F.applyTransform([e[0],e[3]],t),a=F.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,s[0],i[0],n[0],a[0]),this.minY=Math.min(this.minY,s[1],i[1],n[1],a[1]),this.maxX=Math.max(this.maxX,s[0],i[0],n[0],a[0]),this.maxY=Math.max(this.maxY,s[1],i[1],n[1],a[1])}updateScalingPathMinMax(t,e){F.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.minY=Math.min(this.minY,e[1]),this.maxX=Math.max(this.maxX,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,s,i,n,a,o,l,c,d){const u=F.bezierBoundingBox(e,s,i,n,a,o,l,c,d);d||this.updateRectMinMax(t,u)}getPathBoundingBox(t=te.FILL,e=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(t===te.STROKE){e||at("Stroke bounding box must include transform.");const i=F.singularValueDecompose2dScale(e),n=i[0]*this.lineWidth/2,a=i[1]*this.lineWidth/2;s[0]-=n,s[1]-=a,s[2]+=n,s[3]+=a}return s}updateClipFromPath(){const t=F.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(t=te.FILL,e=null){return F.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function xd(h,t){if(typeof ImageData<"u"&&t instanceof ImageData){h.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%oe,n=(e-i)/oe,a=i===0?n:n+1,o=h.createImageData(s,oe);let l=0,c;const d=t.data,u=o.data;let f,p,b,v;if(t.kind===pl.GRAYSCALE_1BPP){const _=d.byteLength,y=new Uint32Array(u.buffer,0,u.byteLength>>2),E=y.length,w=s+7>>3,x=4294967295,S=he.isLittleEndian?4278190080:255;for(f=0;f<a;f++){for(b=f<n?oe:i,c=0,p=0;p<b;p++){const C=_-l;let R=0;const k=C>w?s:C*8-7,D=k&-8;let H=0,T=0;for(;R<D;R+=8)T=d[l++],y[c++]=T&128?x:S,y[c++]=T&64?x:S,y[c++]=T&32?x:S,y[c++]=T&16?x:S,y[c++]=T&8?x:S,y[c++]=T&4?x:S,y[c++]=T&2?x:S,y[c++]=T&1?x:S;for(;R<k;R++)H===0&&(T=d[l++],H=128),y[c++]=T&H?x:S,H>>=1}for(;c<E;)y[c++]=0;h.putImageData(o,0,f*oe)}}else if(t.kind===pl.RGBA_32BPP){for(p=0,v=s*oe*4,f=0;f<n;f++)u.set(d.subarray(l,l+v)),l+=v,h.putImageData(o,0,p),p+=oe;f<a&&(v=s*i*4,u.set(d.subarray(l,l+v)),h.putImageData(o,0,p))}else if(t.kind===pl.RGB_24BPP)for(b=oe,v=s*b,f=0;f<a;f++){for(f>=n&&(b=i,v=s*b),c=0,p=v;p--;)u[c++]=d[l++],u[c++]=d[l++],u[c++]=d[l++],u[c++]=255;h.putImageData(o,0,f*oe)}else throw new Error(`bad image kind: ${t.kind}`)}function Cd(h,t){if(t.bitmap){h.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%oe,n=(e-i)/oe,a=i===0?n:n+1,o=h.createImageData(s,oe);let l=0;const c=t.data,d=o.data;for(let u=0;u<a;u++){const f=u<n?oe:i;({srcPos:l}=Qp({src:c,srcPos:l,dest:d,width:s,height:f,nonBlackColor:0})),h.putImageData(o,0,u*oe)}}function Zr(h,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)h[s]!==void 0&&(t[s]=h[s]);h.setLineDash!==void 0&&(t.setLineDash(h.getLineDash()),t.lineDashOffset=h.lineDashOffset)}function cl(h){if(h.strokeStyle=h.fillStyle="#000000",h.fillRule="nonzero",h.globalAlpha=1,h.lineWidth=1,h.lineCap="butt",h.lineJoin="miter",h.miterLimit=10,h.globalCompositeOperation="source-over",h.font="10px sans-serif",h.setLineDash!==void 0&&(h.setLineDash([]),h.lineDashOffset=0),!qt){const{filter:t}=h;t!=="none"&&t!==""&&(h.filter="none")}}function Td(h,t){if(t)return!0;const e=F.singularValueDecompose2dScale(h);e[0]=Math.fround(e[0]),e[1]=Math.fround(e[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*xi.PDF_TO_CSS_UNITS);return e[0]<=s&&e[1]<=s}const sg=["butt","round","square"],ig=["miter","round","bevel"],ng={},Rd={};var Ti,dc,uc;const ud=class ud{constructor(t,e,s,i,n,{optionalContentConfig:a,markedContentStack:o=null},l,c){A(this,Ti);this.ctx=t,this.current=new Sd(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=n,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=o||[],this.optionalContentConfig=a,this.cachedCanvases=new tg(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=l,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=c,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height,o=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,n,a),this.ctx.fillStyle=o,s){const l=this.cachedCanvases.getCanvas("transparent",n,a);this.compositeCtx=this.ctx,this.transparentCanvas=l.canvas,this.ctx=l.context,this.ctx.save(),this.ctx.transform(...mt(this.compositeCtx))}this.ctx.save(),cl(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=mt(this.ctx)}executeOperatorList(t,e,s,i){const n=t.argsArray,a=t.fnArray;let o=e||0;const l=n.length;if(l===o)return o;const c=l-o>Ed&&typeof s=="function",d=c?Date.now()+Jp:0;let u=0;const f=this.commonObjs,p=this.objs;let b;for(;;){if(i!==void 0&&o===i.nextBreakPoint)return i.breakIt(o,s),o;if(b=a[o],b!==Oe.dependency)this[b].apply(this,n[o]);else for(const v of n[o]){const _=v.startsWith("g_")?f:p;if(!_.has(v))return _.get(v,s),o}if(o++,o===l)return o;if(c&&++u>Ed){if(Date.now()>d)return s(),o;u=0}}}endDrawing(){m(this,Ti,dc).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement<"u"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),m(this,Ti,uc).call(this)}_scaleImage(t,e){const s=t.width,i=t.height;let n=Math.max(Math.hypot(e[0],e[1]),1),a=Math.max(Math.hypot(e[2],e[3]),1),o=s,l=i,c="prescale1",d,u;for(;n>2&&o>1||a>2&&l>1;){let f=o,p=l;n>2&&o>1&&(f=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o/2),n/=o/f),a>2&&l>1&&(p=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l)/2,a/=l/p),d=this.cachedCanvases.getCanvas(c,f,p),u=d.context,u.clearRect(0,0,f,p),u.drawImage(t,0,0,o,l,0,0,f,p),t=d.canvas,o=f,l=p,c=c==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:o,paintHeight:l}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,n=this.current.fillColor,a=this.current.patternFill,o=mt(e);let l,c,d,u;if((t.bitmap||t.data)&&t.count>1){const k=t.bitmap||t.data.buffer;c=JSON.stringify(a?o:[o.slice(0,4),n]),l=this._cachedBitmapsMap.get(k),l||(l=new Map,this._cachedBitmapsMap.set(k,l));const D=l.get(c);if(D&&!a){const H=Math.round(Math.min(o[0],o[2])+o[4]),T=Math.round(Math.min(o[1],o[3])+o[5]);return{canvas:D,offsetX:H,offsetY:T}}d=D}d||(u=this.cachedCanvases.getCanvas("maskCanvas",s,i),Cd(u.context,t));let f=F.transform(o,[1/s,0,0,-1/i,0,0]);f=F.transform(f,[1,0,0,1,0,-i]);const[p,b,v,_]=F.getAxialAlignedBoundingBox([0,0,s,i],f),y=Math.round(v-p)||1,E=Math.round(_-b)||1,w=this.cachedCanvases.getCanvas("fillCanvas",y,E),x=w.context,S=p,C=b;x.translate(-S,-C),x.transform(...f),d||(d=this._scaleImage(u.canvas,ms(x)),d=d.img,l&&a&&l.set(c,d)),x.imageSmoothingEnabled=Td(mt(x),t.interpolate),hl(x,d,0,0,d.width,d.height,0,0,s,i),x.globalCompositeOperation="source-in";const R=F.transform(ms(x),[1,0,0,1,-S,-C]);return x.fillStyle=a?n.getPattern(e,this,R,te.FILL):n,x.fillRect(0,0,s,i),l&&!a&&(this.cachedCanvases.delete("fillCanvas"),l.set(c,w.canvas)),{canvas:w.canvas,offsetX:Math.round(S),offsetY:Math.round(C)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=sg[t]}setLineJoin(t){this.ctx.lineJoin=ig[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.current.fillAlpha=s,this.ctx.globalAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx,this.ctx=i.context;const n=this.ctx;n.setTransform(...mt(this.suspendedCtx)),Zr(this.suspendedCtx,n),Zp(n,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),Zr(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const n=i[0],a=i[1],o=i[2]-n,l=i[3]-a;o===0||l===0||(this.genericComposeSMask(e.context,s,o,l,e.subtype,e.backdrop,e.transferMap,n,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,n,a,o,l,c,d,u){let f=t.canvas,p=l-d,b=c-u;if(a){if(p<0||b<0||p+s>f.width||b+i>f.height){const _=this.cachedCanvases.getCanvas("maskExtension",s,i),y=_.context;y.drawImage(f,-p,-b),a.some(E=>E!==0)&&(y.globalCompositeOperation="destination-atop",y.fillStyle=F.makeHexColor(...a),y.fillRect(0,0,s,i),y.globalCompositeOperation="source-over"),f=_.canvas,p=b=0}else if(a.some(_=>_!==0)){t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const _=new Path2D;_.rect(p,b,s,i),t.clip(_),t.globalCompositeOperation="destination-atop",t.fillStyle=F.makeHexColor(...a),t.fillRect(p,b,s,i),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),n==="Alpha"&&o?e.filter=this.filterFactory.addAlphaFilter(o):n==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(o));const v=new Path2D;v.rect(l,c,s,i),e.clip(v),e.globalCompositeOperation="destination-in",e.drawImage(f,p,b,s,i,l,c,s,i),e.restore()}save(){this.inSMaskMode?(Zr(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),Zr(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(t,e,s,i,n,a){this.ctx.transform(t,e,s,i,n,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){const i=this.ctx,n=this.current;let a=n.x,o=n.y,l,c;const d=mt(i),u=d[0]===0&&d[3]===0||d[1]===0&&d[2]===0,f=u?s.slice(0):null;for(let p=0,b=0,v=t.length;p<v;p++)switch(t[p]|0){case Oe.rectangle:a=e[b++],o=e[b++];const _=e[b++],y=e[b++],E=a+_,w=o+y;i.moveTo(a,o),_===0||y===0?i.lineTo(E,w):(i.lineTo(E,o),i.lineTo(E,w),i.lineTo(a,w)),u||n.updateRectMinMax(d,[a,o,E,w]),i.closePath();break;case Oe.moveTo:a=e[b++],o=e[b++],i.moveTo(a,o),u||n.updatePathMinMax(d,a,o);break;case Oe.lineTo:a=e[b++],o=e[b++],i.lineTo(a,o),u||n.updatePathMinMax(d,a,o);break;case Oe.curveTo:l=a,c=o,a=e[b+4],o=e[b+5],i.bezierCurveTo(e[b],e[b+1],e[b+2],e[b+3],a,o),n.updateCurvePathMinMax(d,l,c,e[b],e[b+1],e[b+2],e[b+3],a,o,f),b+=6;break;case Oe.curveTo2:l=a,c=o,i.bezierCurveTo(a,o,e[b],e[b+1],e[b+2],e[b+3]),n.updateCurvePathMinMax(d,l,c,a,o,e[b],e[b+1],e[b+2],e[b+3],f),a=e[b+2],o=e[b+3],b+=4;break;case Oe.curveTo3:l=a,c=o,a=e[b+2],o=e[b+3],i.bezierCurveTo(e[b],e[b+1],a,o,a,o),n.updateCurvePathMinMax(d,l,c,e[b],e[b+1],a,o,a,o,f),b+=4;break;case Oe.closePath:i.closePath();break}u&&n.updateScalingPathMinMax(d,f),n.setCurrentPoint(a,o)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,s=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof s=="object"&&(s!=null&&s.getPattern)?(e.save(),e.strokeStyle=s.getPattern(e,this,ms(e),te.STROKE),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(t=!0){const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;let n=!1;i&&(e.save(),e.fillStyle=s.getPattern(e,this,ms(e),te.FILL),n=!0);const a=this.current.getClippedPathBoundingBox();this.contentVisible&&a!==null&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),n&&e.restore(),t&&this.consumePath(a)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=ng}eoClip(){this.pendingClip=Rd}beginText(){this.current.textMatrix=Gd,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0){e.beginPath();return}e.save(),e.beginPath();for(const s of t)e.setTransform(...s.transform),e.translate(s.x,s.y),s.addToPath(e,s.fontSize);e.restore(),e.clip(),e.beginPath(),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){var d;const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||Dh,(i.fontMatrix[0]===0||i.fontMatrix[3]===0)&&q("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const n=s.loadedName||"sans-serif",a=((d=s.systemFontInfo)==null?void 0:d.css)||`"${n}", ${s.fallbackName}`;let o="normal";s.black?o="900":s.bold&&(o="bold");const l=s.italic?"italic":"normal";let c=e;e<yd?c=yd:e>_d&&(c=_d),this.current.fontSizeScale=e/c,this.ctx.font=`${l} ${o} ${c}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,s,i,n,a){this.current.textMatrix=[t,e,s,i,n,a],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,i){const n=this.ctx,a=this.current,o=a.font,l=a.textRenderingMode,c=a.fontSize/a.fontSizeScale,d=l&zt.FILL_STROKE_MASK,u=!!(l&zt.ADD_TO_PATH_FLAG),f=a.patternFill&&!o.missingFile;let p;(o.disableFontFace||u||f)&&(p=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||f?(n.save(),n.translate(e,s),n.beginPath(),p(n,c),i&&n.setTransform(...i),(d===zt.FILL||d===zt.FILL_STROKE)&&n.fill(),(d===zt.STROKE||d===zt.FILL_STROKE)&&n.stroke(),n.restore()):((d===zt.FILL||d===zt.FILL_STROKE)&&n.fillText(t,e,s),(d===zt.STROKE||d===zt.FILL_STROKE)&&n.strokeText(t,e,s)),u&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:mt(n),x:e,y:s,fontSize:c,addToPath:p})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return J(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const n=this.ctx,a=e.fontSizeScale,o=e.charSpacing,l=e.wordSpacing,c=e.fontDirection,d=e.textHScale*c,u=t.length,f=s.vertical,p=f?1:-1,b=s.defaultVMetrics,v=i*e.fontMatrix[0],_=e.textRenderingMode===zt.FILL&&!s.disableFontFace&&!e.patternFill;n.save(),n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),c>0?n.scale(d,-1):n.scale(d,1);let y;if(e.patternFill){n.save();const C=e.fillColor.getPattern(n,this,ms(n),te.FILL);y=mt(n),n.restore(),n.fillStyle=C}let E=e.lineWidth;const w=e.textMatrixScale;if(w===0||E===0){const C=e.textRenderingMode&zt.FILL_STROKE_MASK;(C===zt.STROKE||C===zt.FILL_STROKE)&&(E=this.getSinglePixelWidth())}else E/=w;if(a!==1&&(n.scale(a,a),E/=a),n.lineWidth=E,s.isInvalidPDFjsFont){const C=[];let R=0;for(const k of t)C.push(k.unicode),R+=k.width;n.fillText(C.join(""),0,0),e.x+=R*v*d,n.restore(),this.compose();return}let x=0,S;for(S=0;S<u;++S){const C=t[S];if(typeof C=="number"){x+=p*C*i/1e3;continue}let R=!1;const k=(C.isSpace?l:0)+o,D=C.fontChar,H=C.accent;let T,j,O=C.width;if(f){const W=C.vmetric||b,Z=-(C.vmetric?W[1]:O*.5)*v,M=W[2]*v;O=W?-W[0]:O,T=Z/a,j=(x+M)/a}else T=x/a,j=0;if(s.remeasure&&O>0){const W=n.measureText(D).width*1e3/i*a;if(O<W&&this.isFontSubpixelAAEnabled){const Z=O/W;R=!0,n.save(),n.scale(Z,1),T/=Z}else O!==W&&(T+=(O-W)/2e3*i/a)}if(this.contentVisible&&(C.isInFont||s.missingFile)){if(_&&!H)n.fillText(D,T,j);else if(this.paintChar(D,T,j,y),H){const W=T+i*H.offset.x/a,Z=j-i*H.offset.y/a;this.paintChar(H.fontChar,W,Z,y)}}const $=f?O*v-k*c:O*v+k*c;x+=$,R&&n.restore()}f?e.y-=x:e.x+=x*d,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,n=s.fontSize,a=s.fontDirection,o=i.vertical?1:-1,l=s.charSpacing,c=s.wordSpacing,d=s.textHScale*a,u=s.fontMatrix||Dh,f=t.length,p=s.textRenderingMode===zt.INVISIBLE;let b,v,_,y;if(!(p||n===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),e.transform(...s.textMatrix),e.translate(s.x,s.y),e.scale(d,a),b=0;b<f;++b){if(v=t[b],typeof v=="number"){y=o*v*n/1e3,this.ctx.translate(y,0),s.x+=y*d;continue}const E=(v.isSpace?c:0)+l,w=i.charProcOperatorList[v.operatorListId];if(!w){q(`Type3 character "${v.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=v,this.save(),e.scale(n,n),e.transform(...u),this.executeOperatorList(w),this.restore()),_=F.applyTransform([v.width,0],u)[0]*n+E,e.translate(_,0),s.x+=_*d}e.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,n,a){this.ctx.rect(s,i,n-s,a-i),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=t[1],i=this.baseTransform||mt(this.ctx),n={createCanvasGraphics:a=>new ud(a,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new cc(t,s,this.ctx,n,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,s){const i=F.makeHexColor(t,e,s);this.ctx.strokeStyle=i,this.current.strokeColor=i}setFillRGBColor(t,e,s){const i=F.makeHexColor(t,e,s);this.ctx.fillStyle=i,this.current.fillColor=i,this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=Kp(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,ms(e),te.SHADING);const i=ms(e);if(i){const{width:n,height:a}=e.canvas,[o,l,c,d]=F.getAxialAlignedBoundingBox([0,0,n,a],i);this.ctx.fillRect(o,l,c-o,d-l)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){at("Should not call beginInlineImage")}beginImageData(){at("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=mt(this.ctx),e)){const s=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],s,i),this.current.updateRectMinMax(mt(this.ctx),e),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||ph("TODO: Support non-isolated groups."),t.knockout&&q("Knockout groups not supported.");const s=mt(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=F.getAxialAlignedBoundingBox(t.bbox,mt(e));const n=[0,0,e.canvas.width,e.canvas.height];i=F.intersect(i,n)||[0,0,0,0];const a=Math.floor(i[0]),o=Math.floor(i[1]),l=Math.max(Math.ceil(i[2])-a,1),c=Math.max(Math.ceil(i[3])-o,1);this.current.startNewPathAndClipBox([0,0,l,c]);let d="groupAt"+this.groupLevel;t.smask&&(d+="_smask_"+this.smaskCounter++%2);const u=this.cachedCanvases.getCanvas(d,l,c),f=u.context;f.translate(-a,-o),f.transform(...s),t.smask?this.smaskStack.push({canvas:u.canvas,context:f,offsetX:a,offsetY:o,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,o),e.save()),Zr(e,f),this.ctx=f,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=mt(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const n=F.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(n)}}beginAnnotation(t,e,s,i,n){if(m(this,Ti,dc).call(this),cl(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const a=e[2]-e[0],o=e[3]-e[1];if(n&&this.annotationCanvasMap){s=s.slice(),s[4]-=e[0],s[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=a,e[3]=o;const[l,c]=F.singularValueDecompose2dScale(mt(this.ctx)),{viewportScale:d}=this,u=Math.ceil(a*this.outputScaleX*d),f=Math.ceil(o*this.outputScaleY*d);this.annotationCanvas=this.canvasFactory.create(u,f);const{canvas:p,context:b}=this.annotationCanvas;this.annotationCanvasMap.set(t,p),this.annotationCanvas.savedCtx=this.ctx,this.ctx=b,this.ctx.save(),this.ctx.setTransform(l,0,0,-c,0,o*c),cl(this.ctx)}else cl(this.ctx),this.ctx.rect(e[0],e[1],a,o),this.ctx.clip(),this.endPath()}this.current=new Sd(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),m(this,Ti,uc).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const s=this.ctx,i=this.processingType3;if(i&&(i.compiled===void 0&&(i.compiled=eg(t)),i.compiled)){i.compiled(s);return}const n=this._createMaskCanvas(t),a=n.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(a,n.offsetX,n.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,n,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const o=this.ctx;o.save();const l=mt(o);o.transform(e,s,i,n,0,0);const c=this._createMaskCanvas(t);o.setTransform(1,0,0,1,c.offsetX-l[4],c.offsetY-l[5]);for(let d=0,u=a.length;d<u;d+=2){const f=F.transform(l,[e,s,i,n,a[d],a[d+1]]),[p,b]=F.applyTransform([0,0],f);o.drawImage(c.canvas,p,b)}o.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const n of t){const{data:a,width:o,height:l,transform:c}=n,d=this.cachedCanvases.getCanvas("maskCanvas",o,l),u=d.context;u.save();const f=this.getObject(a,n);Cd(u,f),u.globalCompositeOperation="source-in",u.fillStyle=i?s.getPattern(u,this,ms(e),te.FILL):s,u.fillRect(0,0,o,l),u.restore(),e.save(),e.transform(...c),e.scale(1,-1),hl(e,d.canvas,0,0,o,l,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);if(!e){q("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){q("Dependent image isn't ready yet");return}const a=n.width,o=n.height,l=[];for(let c=0,d=i.length;c<d;c+=2)l.push({transform:[e,0,0,s,i[c],i[c+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(n,l)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,n=this.cachedCanvases.getCanvas("inlineImage",s,i),a=n.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;if(this.save(),!qt){const{filter:o}=i;o!=="none"&&o!==""&&(i.filter="none")}i.scale(1/e,-1/s);let n;if(t.bitmap)n=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)n=t;else{const l=this.cachedCanvases.getCanvas("inlineImage",e,s).context;xd(l,t),n=this.applyTransferMapsToCanvas(l)}const a=this._scaleImage(n,ms(i));i.imageSmoothingEnabled=Td(mt(i),t.interpolate),hl(i,a.img,0,0,a.paintWidth,a.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const n=t.width,a=t.height,l=this.cachedCanvases.getCanvas("inlineImage",n,a).context;xd(l,t),i=this.applyTransferMapsToCanvas(l)}for(const n of e)s.save(),s.transform(...n.transform),s.scale(1,-1),hl(s,i,n.x,n.y,n.w,n.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(t);const s=this.ctx;this.pendingClip&&(e||(this.pendingClip===Rd?s.clip("evenodd"):s.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=mt(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:n}=this.ctx.getTransform();let a,o;if(s===0&&i===0){const l=Math.abs(e),c=Math.abs(n);if(l===c)if(t===0)a=o=1/l;else{const d=l*t;a=o=d<1?1/d:1}else if(t===0)a=1/l,o=1/c;else{const d=l*t,u=c*t;a=d<1?1/d:1,o=u<1?1/u:1}}else{const l=Math.abs(e*n-s*i),c=Math.hypot(e,s),d=Math.hypot(i,n);if(t===0)a=d/l,o=c/l;else{const u=t*l;a=d>u?d/u:1,o=c>u?c/u:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=o}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:s}=this.current,[i,n]=this.getScaleForStroking();if(e.lineWidth=s||1,i===1&&n===1){e.stroke();return}const a=e.getLineDash();if(t&&e.save(),e.scale(i,n),a.length>0){const o=Math.max(i,n);e.setLineDash(a.map(l=>l/o)),e.lineDashOffset/=o}e.stroke(),t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}};Ti=new WeakSet,dc=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},uc=function(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}};let Qn=ud;for(const h in Oe)Qn.prototype[h]!==void 0&&(Qn.prototype[Oe[h]]=Qn.prototype[h]);var Ka,Qa;class Ws{static get workerPort(){return r(this,Ka)}static set workerPort(t){if(!(typeof Worker<"u"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");g(this,Ka,t)}static get workerSrc(){return r(this,Qa)}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");g(this,Qa,t)}}Ka=new WeakMap,Qa=new WeakMap,A(Ws,Ka,null),A(Ws,Qa,"");const dl={UNKNOWN:0,DATA:1,ERROR:2},wt={UNKNOWN:0,CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function ge(h){switch(h instanceof Error||typeof h=="object"&&h!==null||at('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),h.name){case"AbortException":return new Bn(h.message);case"MissingPDFException":return new Hn(h.message);case"PasswordException":return new Oh(h.message,h.code);case"UnexpectedResponseException":return new gh(h.message,h.status);case"UnknownErrorException":return new Nh(h.message,h.details);default:return new Nh(h.message,h.toString())}}var fs,xu,Cu,yl;class oa{constructor(t,e,s){A(this,fs);this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=i=>{const n=i.data;if(n.targetName!==this.sourceName)return;if(n.stream){m(this,fs,Cu).call(this,n);return}if(n.callback){const o=n.callbackId,l=this.callbackCapabilities[o];if(!l)throw new Error(`Cannot resolve callback ${o}`);if(delete this.callbackCapabilities[o],n.callback===dl.DATA)l.resolve(n.data);else if(n.callback===dl.ERROR)l.reject(ge(n.reason));else throw new Error("Unexpected callback case");return}const a=this.actionHandler[n.action];if(!a)throw new Error(`Unknown action from worker: ${n.action}`);if(n.callbackId){const o=this.sourceName,l=n.sourceName;new Promise(function(c){c(a(n.data))}).then(function(c){s.postMessage({sourceName:o,targetName:l,callback:dl.DATA,callbackId:n.callbackId,data:c})},function(c){s.postMessage({sourceName:o,targetName:l,callback:dl.ERROR,callbackId:n.callbackId,reason:ge(c)})});return}if(n.streamId){m(this,fs,xu).call(this,n);return}a(n.data)},s.addEventListener("message",this._onComObjOnMessage)}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[i]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(a){n.reject(a)}return n.promise}sendWithStream(t,e,s,i){const n=this.streamId++,a=this.sourceName,o=this.targetName,l=this.comObj;return new ReadableStream({start:c=>{const d=Promise.withResolvers();return this.streamControllers[n]={controller:c,startCall:d,pullCall:null,cancelCall:null,isClosed:!1},l.postMessage({sourceName:a,targetName:o,action:t,streamId:n,data:e,desiredSize:c.desiredSize},i),d.promise},pull:c=>{const d=Promise.withResolvers();return this.streamControllers[n].pullCall=d,l.postMessage({sourceName:a,targetName:o,stream:wt.PULL,streamId:n,desiredSize:c.desiredSize}),d.promise},cancel:c=>{Lt(c instanceof Error,"cancel must have a valid reason");const d=Promise.withResolvers();return this.streamControllers[n].cancelCall=d,this.streamControllers[n].isClosed=!0,l.postMessage({sourceName:a,targetName:o,stream:wt.CANCEL,streamId:n,reason:ge(c)}),d.promise}},s)}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}fs=new WeakSet,xu=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,a=this,o=this.actionHandler[t.action],l={enqueue(c,d=1,u){if(this.isCancelled)return;const f=this.desiredSize;this.desiredSize-=d,f>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:s,targetName:i,stream:wt.ENQUEUE,streamId:e,chunk:c},u)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:wt.CLOSE,streamId:e}),delete a.streamSinks[e])},error(c){Lt(c instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:wt.ERROR,streamId:e,reason:ge(c)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};l.sinkCapability.resolve(),l.ready=l.sinkCapability.promise,this.streamSinks[e]=l,new Promise(function(c){c(o(t.data,l))}).then(function(){n.postMessage({sourceName:s,targetName:i,stream:wt.START_COMPLETE,streamId:e,success:!0})},function(c){n.postMessage({sourceName:s,targetName:i,stream:wt.START_COMPLETE,streamId:e,reason:ge(c)})})},Cu=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,a=this.streamControllers[e],o=this.streamSinks[e];switch(t.stream){case wt.START_COMPLETE:t.success?a.startCall.resolve():a.startCall.reject(ge(t.reason));break;case wt.PULL_COMPLETE:t.success?a.pullCall.resolve():a.pullCall.reject(ge(t.reason));break;case wt.PULL:if(!o){n.postMessage({sourceName:s,targetName:i,stream:wt.PULL_COMPLETE,streamId:e,success:!0});break}o.desiredSize<=0&&t.desiredSize>0&&o.sinkCapability.resolve(),o.desiredSize=t.desiredSize,new Promise(function(l){var c;l((c=o.onPull)==null?void 0:c.call(o))}).then(function(){n.postMessage({sourceName:s,targetName:i,stream:wt.PULL_COMPLETE,streamId:e,success:!0})},function(l){n.postMessage({sourceName:s,targetName:i,stream:wt.PULL_COMPLETE,streamId:e,reason:ge(l)})});break;case wt.ENQUEUE:if(Lt(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case wt.CLOSE:if(Lt(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),m(this,fs,yl).call(this,a,e);break;case wt.ERROR:Lt(a,"error should have stream controller"),a.controller.error(ge(t.reason)),m(this,fs,yl).call(this,a,e);break;case wt.CANCEL_COMPLETE:t.success?a.cancelCall.resolve():a.cancelCall.reject(ge(t.reason)),m(this,fs,yl).call(this,a,e);break;case wt.CANCEL:if(!o)break;new Promise(function(l){var c;l((c=o.onCancel)==null?void 0:c.call(o,ge(t.reason)))}).then(function(){n.postMessage({sourceName:s,targetName:i,stream:wt.CANCEL_COMPLETE,streamId:e,success:!0})},function(l){n.postMessage({sourceName:s,targetName:i,stream:wt.CANCEL_COMPLETE,streamId:e,reason:ge(l)})}),o.sinkCapability.reject(ge(t.reason)),o.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}},yl=async function(t,e){var s,i,n;await Promise.allSettled([(s=t.startCall)==null?void 0:s.promise,(i=t.pullCall)==null?void 0:i.promise,(n=t.cancelCall)==null?void 0:n.promise]),delete this.streamControllers[e]};var Qi,Ja;class rg{constructor({parsedData:t,rawData:e}){A(this,Qi);A(this,Ja);g(this,Qi,t),g(this,Ja,e)}getRaw(){return r(this,Ja)}get(t){return r(this,Qi).get(t)??null}getAll(){return td(r(this,Qi))}has(t){return r(this,Qi).has(t)}}Qi=new WeakMap,Ja=new WeakMap;const ei=Symbol("INTERNAL");var Za,to,eo,fr;class ag{constructor(t,{name:e,intent:s,usage:i}){A(this,Za,!1);A(this,to,!1);A(this,eo,!1);A(this,fr,!0);g(this,Za,!!(t&Le.DISPLAY)),g(this,to,!!(t&Le.PRINT)),this.name=e,this.intent=s,this.usage=i}get visible(){if(r(this,eo))return r(this,fr);if(!r(this,fr))return!1;const{print:t,view:e}=this.usage;return r(this,Za)?(e==null?void 0:e.viewState)!=="OFF":r(this,to)?(t==null?void 0:t.printState)!=="OFF":!0}_setVisible(t,e,s=!1){t!==ei&&at("Internal method `_setVisible` called."),g(this,eo,s),g(this,fr,e)}}Za=new WeakMap,to=new WeakMap,eo=new WeakMap,fr=new WeakMap;var hi,rt,pr,gr,so,fc;class og{constructor(t,e=Le.DISPLAY){A(this,so);A(this,hi,null);A(this,rt,new Map);A(this,pr,null);A(this,gr,null);if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,g(this,gr,t.order);for(const s of t.groups)r(this,rt).set(s.id,new ag(e,s));if(t.baseState==="OFF")for(const s of r(this,rt).values())s._setVisible(ei,!1);for(const s of t.on)r(this,rt).get(s)._setVisible(ei,!0);for(const s of t.off)r(this,rt).get(s)._setVisible(ei,!1);g(this,pr,this.getHash())}}isVisible(t){if(r(this,rt).size===0)return!0;if(!t)return ph("Optional content group not defined."),!0;if(t.type==="OCG")return r(this,rt).has(t.id)?r(this,rt).get(t.id).visible:(q(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return m(this,so,fc).call(this,t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!r(this,rt).has(e))return q(`Optional content group not found: ${e}`),!0;if(r(this,rt).get(e).visible)return!0}return!1}else if(t.policy==="AllOn"){for(const e of t.ids){if(!r(this,rt).has(e))return q(`Optional content group not found: ${e}`),!0;if(!r(this,rt).get(e).visible)return!1}return!0}else if(t.policy==="AnyOff"){for(const e of t.ids){if(!r(this,rt).has(e))return q(`Optional content group not found: ${e}`),!0;if(!r(this,rt).get(e).visible)return!0}return!1}else if(t.policy==="AllOff"){for(const e of t.ids){if(!r(this,rt).has(e))return q(`Optional content group not found: ${e}`),!0;if(r(this,rt).get(e).visible)return!1}return!0}return q(`Unknown optional content policy ${t.policy}.`),!0}return q(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0){const s=r(this,rt).get(t);if(!s){q(`Optional content group not found: ${t}`);return}s._setVisible(ei,!!e,!0),g(this,hi,null)}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const n=r(this,rt).get(i);if(n)switch(s){case"ON":n._setVisible(ei,!0);break;case"OFF":n._setVisible(ei,!1);break;case"Toggle":n._setVisible(ei,!n.visible);break}}g(this,hi,null)}get hasInitialVisibility(){return r(this,pr)===null||this.getHash()===r(this,pr)}getOrder(){return r(this,rt).size?r(this,gr)?r(this,gr).slice():[...r(this,rt).keys()]:null}getGroups(){return r(this,rt).size>0?td(r(this,rt)):null}getGroup(t){return r(this,rt).get(t)||null}getHash(){if(r(this,hi)!==null)return r(this,hi);const t=new _u;for(const[e,s]of r(this,rt))t.update(`${e}:${s.visible}`);return g(this,hi,t.hexdigest())}}hi=new WeakMap,rt=new WeakMap,pr=new WeakMap,gr=new WeakMap,so=new WeakSet,fc=function(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const n=t[i];let a;if(Array.isArray(n))a=m(this,so,fc).call(this,n);else if(r(this,rt).has(n))a=r(this,rt).get(n).visible;else return q(`Optional content group not found: ${n}`),!0;switch(s){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return s==="And"};class lg{constructor(t,{disableRange:e=!1,disableStream:s=!1}){Lt(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:n,progressiveDone:a,contentDispositionFilename:o}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=o,(n==null?void 0:n.length)>0){const l=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(l)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((l,c)=>{this._onReceiveData({begin:l,chunk:c})}),t.addProgressListener((l,c)=>{this._onProgress({loaded:l,total:c})}),t.addProgressiveReadListener(l=>{this._onReceiveData({chunk:l})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(t===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const i=this._rangeReaders.some(function(n){return n._begin!==t?!1:(n._enqueue(s),!0)});Lt(i,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var t;return((t=this._fullRequestReader)==null?void 0:t._loaded)??0}_onProgress(t){var e,s,i,n;t.total===void 0?(s=(e=this._rangeReaders[0])==null?void 0:e.onProgress)==null||s.call(e,{loaded:t.loaded}):(n=(i=this._fullRequestReader)==null?void 0:i.onProgress)==null||n.call(i,{loaded:t.loaded,total:t.total})}_onProgressiveDone(){var t;(t=this._fullRequestReader)==null||t.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){Lt(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new hg(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new cg(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeReaders.slice(0))s.cancel(t);this._pdfDataRangeTransport.abort()}}class hg{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=rd(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const n of this._queuedChunks)this._loaded+=n.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class cg{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function dg(h){let t=!0,e=s("filename\\*","i").exec(h);if(e){e=e[1];let d=o(e);return d=unescape(d),d=l(d),d=c(d),n(d)}if(e=a(h),e){const d=c(e);return n(d)}if(e=s("filename","i").exec(h),e){e=e[1];let d=o(e);return d=c(d),n(d)}function s(d,u){return new RegExp("(?:^|;)\\s*"+d+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',u)}function i(d,u){if(d){if(!/^[\x00-\xFF]+$/.test(u))return u;try{const f=new TextDecoder(d,{fatal:!0}),p=mh(u);u=f.decode(p),t=!1}catch{}}return u}function n(d){return t&&/[\x80-\xff]/.test(d)&&(d=i("utf-8",d),t&&(d=i("iso-8859-1",d))),d}function a(d){const u=[];let f;const p=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(f=p.exec(d))!==null;){let[,v,_,y]=f;if(v=parseInt(v,10),v in u){if(v===0)break;continue}u[v]=[_,y]}const b=[];for(let v=0;v<u.length&&v in u;++v){let[_,y]=u[v];y=o(y),_&&(y=unescape(y),v===0&&(y=l(y))),b.push(y)}return b.join("")}function o(d){if(d.startsWith('"')){const u=d.slice(1).split('\\"');for(let f=0;f<u.length;++f){const p=u[f].indexOf('"');p!==-1&&(u[f]=u[f].slice(0,p),u.length=f+1),u[f]=u[f].replaceAll(/\\(.)/g,"$1")}d=u.join('"')}return d}function l(d){const u=d.indexOf("'");if(u===-1)return d;const f=d.slice(0,u),b=d.slice(u+1).replace(/^[^']*'/,"");return i(f,b)}function c(d){return!d.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(d)?d:d.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(u,f,p,b){if(p==="q"||p==="Q")return b=b.replaceAll("_"," "),b=b.replaceAll(/=([0-9a-fA-F]{2})/g,function(v,_){return String.fromCharCode(parseInt(_,16))}),i(f,b);try{b=atob(b)}catch{}return i(f,b)})}return""}function ld({getResponseHeader:h,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},n=parseInt(h("Content-Length"),10);return!Number.isInteger(n)||(i.suggestedLength=n,n<=2*e)||s||!t||h("Accept-Ranges")!=="bytes"||(h("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function hd(h){const t=h("Content-Disposition");if(t){let e=dg(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch{}if(rd(e))return e}return null}function Eh(h,t){return h===404||h===0&&t.startsWith("file:")?new Hn('Missing PDF "'+t+'".'):new gh(`Unexpected server response (${h}) while retrieving PDF "${t}".`,h)}function Tu(h){return h===200||h===206}function Ru(h,t,e){return{method:"GET",headers:h,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function Pu(h){const t=new Headers;for(const e in h){const s=h[e];s!==void 0&&t.append(e,s)}return t}function Lu(h){return h instanceof Uint8Array?h.buffer:h instanceof ArrayBuffer?h:(q(`getArrayBuffer - unexpected data format: ${h}`),new Uint8Array(h).buffer)}class Pd{constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return((t=this._fullRequestReader)==null?void 0:t._loaded)??0}getFullReader(){return Lt(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new ug(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new fg(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class ug{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._headers=Pu(this._stream.httpHeaders);const s=e.url;fetch(s,Ru(this._headers,this._withCredentials,this._abortController)).then(i=>{if(!Tu(i.status))throw Eh(i.status,s);this._reader=i.body.getReader(),this._headersCapability.resolve();const n=l=>i.headers.get(l),{allowRangeRequests:a,suggestedLength:o}=ld({getResponseHeader:n,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=a,this._contentLength=o||this._contentLength,this._filename=hd(n),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new Bn("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded,total:this._contentLength}),{value:Lu(t),done:!1})}cancel(t){var e;(e=this._reader)==null||e.cancel(t),this._abortController.abort()}}class fg{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController,this._headers=Pu(this._stream.httpHeaders),this._headers.append("Range",`bytes=${e}-${s-1}`);const n=i.url;fetch(n,Ru(this._headers,this._withCredentials,this._abortController)).then(a=>{if(!Tu(a.status))throw Eh(a.status,n);this._readCapability.resolve(),this._reader=a.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded}),{value:Lu(t),done:!1})}cancel(t){var e;(e=this._reader)==null||e.cancel(t),this._abortController.abort()}}const Mh=200,Ih=206;function pg(h){const t=h.response;return typeof t!="string"?t:mh(t).buffer}class gg{constructor(t,e={}){this.url=t,this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null),this.withCredentials=e.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(t,e,s){const i={begin:t,end:e};for(const n in s)i[n]=s[n];return this.request(i)}requestFull(t){return this.request(t)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const n in this.httpHeaders){const a=this.httpHeaders[n];a!==void 0&&e.setRequestHeader(n,a)}return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=Ih):i.expectedStatus=Mh,e.responseType="arraybuffer",t.onError&&(e.onerror=function(n){t.onError(e.status)}),e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){var i;const s=this.pendingRequests[t];s&&((i=s.onProgress)==null||i.call(s,e))}onStateChange(t,e){var l,c,d;const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){(l=s.onError)==null||l.call(s,i.status);return}const n=i.status||Mh;if(!(n===Mh&&s.expectedStatus===Ih)&&n!==s.expectedStatus){(c=s.onError)==null||c.call(s,i.status);return}const o=pg(i);if(n===Ih){const u=i.getResponseHeader("Content-Range"),f=/bytes (\d+)-(\d+)\/(\d+)/.exec(u);s.onDone({begin:parseInt(f[1],10),chunk:o})}else o?s.onDone({begin:0,chunk:o}):(d=s.onError)==null||d.call(s,i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class mg{constructor(t){this._source=t,this._manager=new gg(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return Lt(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new bg(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new Ag(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class bg{constructor(t,e){this._manager=t;const s={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url,this._fullRequestId=t.requestFull(s),this._headersReceivedCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),s=a=>e.getResponseHeader(a),{allowRangeRequests:i,suggestedLength:n}=ld({getResponseHeader:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});i&&(this._isRangeSupported=!0),this._contentLength=n||this._contentLength,this._filename=hd(s),this._isRangeSupported&&this._manager.abortRequest(t),this._headersReceivedCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=Eh(t,this._url),this._headersReceivedCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){var e;(e=this.onProgress)==null||e.call(this,{loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersReceivedCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class Ag{constructor(t,e,s){this._manager=t;const i={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._requestId=t.requestRange(e,s,i),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){var t;(t=this.onClosed)==null||t.call(this,this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError=Eh(t,this._url);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){var e;this.isStreamingSupported||(e=this.onProgress)==null||e.call(this,{loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const ku=/^file:\/\/\/[a-zA-Z]:\//;function vg(h){const t=ps.get("url"),e=t.parse(h);return e.protocol==="file:"||e.host?e:/^[a-z]:[/\\]/i.test(h)?t.parse(`file:///${h}`):(e.host||(e.protocol="file:"),e)}class yg{constructor(t){this.source=t,this.url=vg(t.url),this.isHttp=this.url.protocol==="http:"||this.url.protocol==="https:",this.isFsUrl=this.url.protocol==="file:",this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return((t=this._fullRequestReader)==null?void 0:t._loaded)??0}getFullReader(){return Lt(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new wg(this):new _g(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=this.isFsUrl?new Sg(this,t,e):new Eg(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class Mu{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new Bn("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class Iu{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){var s;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}function jl(h,t){return{protocol:h.protocol,auth:h.auth,host:h.hostname,port:h.port,path:h.path,method:"GET",headers:t}}class _g extends Mu{constructor(t){super(t);const e=s=>{if(s.statusCode===404){const o=new Hn(`Missing PDF "${this._url}".`);this._storedError=o,this._headersCapability.reject(o);return}this._headersCapability.resolve(),this._setReadableStream(s);const i=o=>this._readableStream.headers[o.toLowerCase()],{allowRangeRequests:n,suggestedLength:a}=ld({getResponseHeader:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n,this._contentLength=a||this._contentLength,this._filename=hd(i)};if(this._request=null,this._url.protocol==="http:"){const s=ps.get("http");this._request=s.request(jl(this._url,t.httpHeaders),e)}else{const s=ps.get("https");this._request=s.request(jl(this._url,t.httpHeaders),e)}this._request.on("error",s=>{this._storedError=s,this._headersCapability.reject(s)}),this._request.end()}}class Eg extends Iu{constructor(t,e,s){super(t),this._httpHeaders={};for(const n in t.httpHeaders){const a=t.httpHeaders[n];a!==void 0&&(this._httpHeaders[n]=a)}this._httpHeaders.Range=`bytes=${e}-${s-1}`;const i=n=>{if(n.statusCode===404){const a=new Hn(`Missing PDF "${this._url}".`);this._storedError=a;return}this._setReadableStream(n)};if(this._request=null,this._url.protocol==="http:"){const n=ps.get("http");this._request=n.request(jl(this._url,this._httpHeaders),i)}else{const n=ps.get("https");this._request=n.request(jl(this._url,this._httpHeaders),i)}this._request.on("error",n=>{this._storedError=n}),this._request.end()}}class wg extends Mu{constructor(t){super(t);let e=decodeURIComponent(this._url.path);ku.test(this._url.href)&&(e=e.replace(/^\//,""));const s=ps.get("fs");s.promises.lstat(e).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(e)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=new Hn(`Missing PDF "${e}".`)),this._storedError=i,this._headersCapability.reject(i)})}}class Sg extends Iu{constructor(t,e,s){super(t);let i=decodeURIComponent(this._url.path);ku.test(this._url.href)&&(i=i.replace(/^\//,""));const n=ps.get("fs");this._setReadableStream(n.createReadStream(i,{start:e,end:s-1}))}}const xg=1e5,ne=30,Cg=.8;var $d,ci,ae,io,no,Ji,Ms,ro,ao,Zi,mr,br,di,Ar,oo,vr,tn,lo,ho,le,en,sn,yr,Ks,Du,Fu,pc,Si,_l,Ou;let $l=(le=class{constructor({textContentSource:t,container:e,viewport:s}){A(this,Ks);A(this,ci,Promise.withResolvers());A(this,ae,null);A(this,io,!1);A(this,no,!!(($d=globalThis.FontInspector)!=null&&$d.enabled));A(this,Ji,null);A(this,Ms,null);A(this,ro,0);A(this,ao,0);A(this,Zi,null);A(this,mr,null);A(this,br,0);A(this,di,0);A(this,Ar,Object.create(null));A(this,oo,[]);A(this,vr,null);A(this,tn,[]);A(this,lo,new WeakMap);A(this,ho,null);if(t instanceof ReadableStream)g(this,vr,t);else if(typeof t=="object")g(this,vr,new ReadableStream({start(l){l.enqueue(t),l.close()}}));else throw new Error('No "textContentSource" parameter specified.');g(this,ae,g(this,mr,e)),g(this,di,s.scale*(globalThis.devicePixelRatio||1)),g(this,br,s.rotation),g(this,Ms,{prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:null});const{pageWidth:i,pageHeight:n,pageX:a,pageY:o}=s.rawDims;g(this,ho,[1,0,0,-1,-a,o+n]),g(this,ao,i),g(this,ro,n),Mn(e,s),r(this,ci).promise.catch(()=>{}).then(()=>{r(le,yr).delete(this),g(this,Ms,null),g(this,Ar,null)})}render(){const t=()=>{r(this,Zi).read().then(({value:e,done:s})=>{if(s){r(this,ci).resolve();return}r(this,Ji)??g(this,Ji,e.lang),Object.assign(r(this,Ar),e.styles),m(this,Ks,Du).call(this,e.items),t()},r(this,ci).reject)};return g(this,Zi,r(this,vr).getReader()),r(le,yr).add(this),t(),r(this,ci).promise}update({viewport:t,onBefore:e=null}){var n;const s=t.scale*(globalThis.devicePixelRatio||1),i=t.rotation;if(i!==r(this,br)&&(e==null||e(),g(this,br,i),Mn(r(this,mr),{rotation:i})),s!==r(this,di)){e==null||e(),g(this,di,s);const a={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:m(n=le,Si,_l).call(n,r(this,Ji))};for(const o of r(this,tn))a.properties=r(this,lo).get(o),a.div=o,m(this,Ks,pc).call(this,a)}}cancel(){var e;const t=new Bn("TextLayer task cancelled.");(e=r(this,Zi))==null||e.cancel(t).catch(()=>{}),g(this,Zi,null),r(this,ci).reject(t)}get textDivs(){return r(this,tn)}get textContentItemsStr(){return r(this,oo)}static cleanup(){if(!(r(this,yr).size>0)){r(this,en).clear();for(const{canvas:t}of r(this,sn).values())t.remove();r(this,sn).clear()}}},ci=new WeakMap,ae=new WeakMap,io=new WeakMap,no=new WeakMap,Ji=new WeakMap,Ms=new WeakMap,ro=new WeakMap,ao=new WeakMap,Zi=new WeakMap,mr=new WeakMap,br=new WeakMap,di=new WeakMap,Ar=new WeakMap,oo=new WeakMap,vr=new WeakMap,tn=new WeakMap,lo=new WeakMap,ho=new WeakMap,en=new WeakMap,sn=new WeakMap,yr=new WeakMap,Ks=new WeakSet,Du=function(t){var i,n;if(r(this,io))return;(n=r(this,Ms)).ctx||(n.ctx=m(i=le,Si,_l).call(i,r(this,Ji)));const e=r(this,tn),s=r(this,oo);for(const a of t){if(e.length>xg){q("Ignoring additional textDivs for performance reasons."),g(this,io,!0);return}if(a.str===void 0){if(a.type==="beginMarkedContentProps"||a.type==="beginMarkedContent"){const o=r(this,ae);g(this,ae,document.createElement("span")),r(this,ae).classList.add("markedContent"),a.id!==null&&r(this,ae).setAttribute("id",`${a.id}`),o.append(r(this,ae))}else a.type==="endMarkedContent"&&g(this,ae,r(this,ae).parentNode);continue}s.push(a.str),m(this,Ks,Fu).call(this,a)}},Fu=function(t){var v;const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};r(this,tn).push(e);const i=F.transform(r(this,ho),t.transform);let n=Math.atan2(i[1],i[0]);const a=r(this,Ar)[t.fontName];a.vertical&&(n+=Math.PI/2);const o=r(this,no)&&a.fontSubstitution||a.fontFamily,l=Math.hypot(i[2],i[3]),c=l*m(v=le,Si,Ou).call(v,o,r(this,Ji));let d,u;n===0?(d=i[4],u=i[5]-c):(d=i[4]+c*Math.sin(n),u=i[5]-c*Math.cos(n));const f="calc(var(--scale-factor)*",p=e.style;r(this,ae)===r(this,mr)?(p.left=`${(100*d/r(this,ao)).toFixed(2)}%`,p.top=`${(100*u/r(this,ro)).toFixed(2)}%`):(p.left=`${f}${d.toFixed(2)}px)`,p.top=`${f}${u.toFixed(2)}px)`),p.fontSize=`${f}${l.toFixed(2)}px)`,p.fontFamily=o,s.fontSize=l,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,r(this,no)&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),n!==0&&(s.angle=n*(180/Math.PI));let b=!1;if(t.str.length>1)b=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const _=Math.abs(t.transform[0]),y=Math.abs(t.transform[3]);_!==y&&Math.max(_,y)/Math.min(_,y)>1.5&&(b=!0)}if(b&&(s.canvasWidth=a.vertical?t.height:t.width),r(this,lo).set(e,s),r(this,Ms).div=e,r(this,Ms).properties=s,m(this,Ks,pc).call(this,r(this,Ms)),s.hasText&&r(this,ae).append(e),s.hasEOL){const _=document.createElement("br");_.setAttribute("role","presentation"),r(this,ae).append(_)}},pc=function(t){const{div:e,properties:s,ctx:i,prevFontSize:n,prevFontFamily:a}=t,{style:o}=e;let l="";if(s.canvasWidth!==0&&s.hasText){const{fontFamily:c}=o,{canvasWidth:d,fontSize:u}=s;(n!==u||a!==c)&&(i.font=`${u*r(this,di)}px ${c}`,t.prevFontSize=u,t.prevFontFamily=c);const{width:f}=i.measureText(e.textContent);f>0&&(l=`scaleX(${d*r(this,di)/f})`)}s.angle!==0&&(l=`rotate(${s.angle}deg) ${l}`),l.length>0&&(o.transform=l)},Si=new WeakSet,_l=function(t=null){let e=r(this,sn).get(t||(t=""));if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1}),r(this,sn).set(t,e)}return e},Ou=function(t,e){const s=r(this,en).get(t);if(s)return s;const i=m(this,Si,_l).call(this,e),n=i.font;i.canvas.width=i.canvas.height=ne,i.font=`${ne}px ${t}`;const a=i.measureText("");let o=a.fontBoundingBoxAscent,l=Math.abs(a.fontBoundingBoxDescent);if(o){const u=o/(o+l);return r(this,en).set(t,u),i.canvas.width=i.canvas.height=0,i.font=n,u}i.strokeStyle="red",i.clearRect(0,0,ne,ne),i.strokeText("g",0,0);let c=i.getImageData(0,0,ne,ne).data;l=0;for(let u=c.length-1-3;u>=0;u-=4)if(c[u]>0){l=Math.ceil(u/4/ne);break}i.clearRect(0,0,ne,ne),i.strokeText("A",0,ne),c=i.getImageData(0,0,ne,ne).data,o=0;for(let u=0,f=c.length;u<f;u+=4)if(c[u]>0){o=ne-Math.floor(u/4/ne);break}i.canvas.width=i.canvas.height=0,i.font=n;const d=o?o/(o+l):Cg;return r(this,en).set(t,d),d},A(le,Si),A(le,en,new Map),A(le,sn,new Map),A(le,yr,new Set),le);function Tg(){Qd("`renderTextLayer`, please use `TextLayer` instead.");const{textContentSource:h,container:t,viewport:e,...s}=arguments[0],i=Object.keys(s);i.length>0&&q("Ignoring `renderTextLayer` parameters: "+i.join(", "));const n=new $l({textContentSource:h,container:t,viewport:e}),{textDivs:a,textContentItemsStr:o}=n;return{promise:n.render(),textDivs:a,textContentItemsStr:o}}function Rg(){Qd("`updateTextLayer`, please use `TextLayer` instead.")}class va{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};function i(n){var l;if(!n)return;let a=null;const o=n.name;if(o==="#text")a=n.value;else if(va.shouldBuildText(o))(l=n==null?void 0:n.attributes)!=null&&l.textContent?a=n.attributes.textContent:n.value&&(a=n.value);else return;if(a!==null&&e.push({str:a}),!!n.children)for(const c of n.children)i(c)}return i(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const Pg=65536,Lg=100,kg=5e3,Mg=qt?zp:Lp,Ig=qt?Gp:Yd,Dg=qt?$p:Pp,Fg=qt?Vp:Kd;function Og(h){if(typeof h=="string"||h instanceof URL?h={url:h}:(h instanceof ArrayBuffer||ArrayBuffer.isView(h))&&(h={data:h}),typeof h!="object")throw new Error("Invalid parameter in getDocument, need parameter object.");if(!h.url&&!h.data&&!h.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const t=new gc,{docId:e}=t,s=h.url?Ng(h.url):null,i=h.data?Hg(h.data):null,n=h.httpHeaders||null,a=h.withCredentials===!0,o=h.password??null,l=h.range instanceof Nu?h.range:null,c=Number.isInteger(h.rangeChunkSize)&&h.rangeChunkSize>0?h.rangeChunkSize:Pg;let d=h.worker instanceof Jn?h.worker:null;const u=h.verbosity,f=typeof h.docBaseUrl=="string"&&!nd(h.docBaseUrl)?h.docBaseUrl:null,p=typeof h.cMapUrl=="string"?h.cMapUrl:null,b=h.cMapPacked!==!1,v=h.CMapReaderFactory||Ig,_=typeof h.standardFontDataUrl=="string"?h.standardFontDataUrl:null,y=h.StandardFontDataFactory||Fg,E=h.stopAtErrors!==!0,w=Number.isInteger(h.maxImageSize)&&h.maxImageSize>-1?h.maxImageSize:-1,x=h.isEvalSupported!==!1,S=typeof h.isOffscreenCanvasSupported=="boolean"?h.isOffscreenCanvasSupported:!qt,C=Number.isInteger(h.canvasMaxAreaInBytes)?h.canvasMaxAreaInBytes:-1,R=typeof h.disableFontFace=="boolean"?h.disableFontFace:qt,k=h.fontExtraProperties===!0,D=h.enableXfa===!0,H=h.ownerDocument||globalThis.document,T=h.disableRange===!0,j=h.disableStream===!0,O=h.disableAutoFetch===!0,$=h.pdfBug===!0,W=l?l.length:h.length??NaN,Z=typeof h.useSystemFonts=="boolean"?h.useSystemFonts:!qt&&!R,M=typeof h.useWorkerFetch=="boolean"?h.useWorkerFetch:v===Yd&&y===Kd&&p&&_&&na(p,document.baseURI)&&na(_,document.baseURI),U=h.canvasFactory||new Mg({ownerDocument:H}),Xt=h.filterFactory||new Dg({docId:e,ownerDocument:H}),Se=null;Ap(u);const ue={canvasFactory:U,filterFactory:Xt};if(M||(ue.cMapReaderFactory=new v({baseUrl:p,isCompressed:b}),ue.standardFontDataFactory=new y({baseUrl:_})),!d){const $t={verbosity:u,port:Ws.workerPort};d=$t.port?Jn.fromPort($t):new Jn($t),t._worker=d}const vt={docId:e,apiVersion:"4.3.136",data:i,password:o,disableAutoFetch:O,rangeChunkSize:c,length:W,docBaseUrl:f,enableXfa:D,evaluatorOptions:{maxImageSize:w,disableFontFace:R,ignoreErrors:E,isEvalSupported:x,isOffscreenCanvasSupported:S,canvasMaxAreaInBytes:C,fontExtraProperties:k,useSystemFonts:Z,cMapUrl:M?p:null,standardFontDataUrl:M?_:null}},pt={disableFontFace:R,fontExtraProperties:k,ownerDocument:H,pdfBug:$,styleElement:Se,loadingParams:{disableAutoFetch:O,enableXfa:D}};return d.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const $t=d.messageHandler.sendWithPromise("GetDocRequest",vt,i?[i.buffer]:null);let Ie;return l?Ie=new lg(l,{disableRange:T,disableStream:j}):i||(Ie=(et=>qt?function(){return typeof fetch<"u"&&typeof Response<"u"&&"body"in Response.prototype}()&&na(et.url)?new Pd(et):new yg(et):na(et.url)?new Pd(et):new mg(et))({url:s,length:W,httpHeaders:n,withCredentials:a,rangeChunkSize:c,disableRange:T,disableStream:j})),$t.then(ie=>{if(t.destroyed)throw new Error("Loading aborted");if(d.destroyed)throw new Error("Worker was destroyed");const et=new oa(e,ie,d.port),jn=new $g(et,t,Ie,pt,ue);t._transport=jn,et.send("Ready",null)})}).catch(t._capability.reject),t}function Ng(h){if(h instanceof URL)return h.href;try{return new URL(h,window.location).href}catch{if(qt&&typeof h=="string")return h}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function Hg(h){if(qt&&typeof Buffer<"u"&&h instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(h instanceof Uint8Array&&h.byteLength===h.buffer.byteLength)return h;if(typeof h=="string")return mh(h);if(h instanceof ArrayBuffer||ArrayBuffer.isView(h)||typeof h=="object"&&!isNaN(h==null?void 0:h.length))return new Uint8Array(h);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function Ld(h){return typeof h=="object"&&Number.isInteger(h==null?void 0:h.num)&&h.num>=0&&Number.isInteger(h==null?void 0:h.gen)&&h.gen>=0}var ih;const nh=class nh{constructor(){this._capability=Promise.withResolvers(),this._transport=null,this._worker=null,this.docId=`d${pe(nh,ih)._++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){var t,e,s;this.destroyed=!0;try{(t=this._worker)!=null&&t.port&&(this._worker._pendingDestroy=!0),await((e=this._transport)==null?void 0:e.destroy())}catch(i){throw(s=this._worker)!=null&&s.port&&delete this._worker._pendingDestroy,i}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}};ih=new WeakMap,A(nh,ih,0);let gc=nh,Nu=class{constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then(()=>{for(const s of this._progressListeners)s(t,e)})}onDataProgressiveRead(t){this._readyCapability.promise.then(()=>{for(const e of this._progressiveReadListeners)e(t)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const t of this._progressiveDoneListeners)t()})}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){at("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}};class Bg{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return J(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}var ui,Is,ke,qn,El;class Ug{constructor(t,e,s,i=!1){A(this,ke);A(this,ui,null);A(this,Is,!1);this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new md:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new Hu,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:n=!1}={}){return new tl({viewBox:this.view,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return J(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var t;return((t=this._transport._htmlForXfa)==null?void 0:t.children[this._pageIndex])||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=ii.ENABLE,transform:n=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:c=null,printAnnotationStorage:d=null}){var w,x;(w=this._stats)==null||w.time("Overall");const u=this._transport.getRenderingIntent(s,i,d),{renderingIntent:f,cacheKey:p}=u;g(this,Is,!1),m(this,ke,El).call(this),o||(o=this._transport.getOptionalContentConfig(f));let b=this._intentStates.get(p);b||(b=Object.create(null),this._intentStates.set(p,b)),b.streamReaderCancelTimeout&&(clearTimeout(b.streamReaderCancelTimeout),b.streamReaderCancelTimeout=null);const v=!!(f&Le.PRINT);b.displayReadyCapability||(b.displayReadyCapability=Promise.withResolvers(),b.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(x=this._stats)==null||x.time("Page Request"),this._pumpOperatorList(u));const _=S=>{var C;b.renderTasks.delete(y),(this._maybeCleanupAfterRender||v)&&g(this,Is,!0),m(this,ke,qn).call(this,!v),S?(y.capability.reject(S),this._abortOperatorList({intentState:b,reason:S instanceof Error?S:new Error(S)})):y.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),(C=globalThis.Stats)!=null&&C.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},y=new bc({callback:_,params:{canvasContext:t,viewport:e,transform:n,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:b.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!v,pdfBug:this._pdfBug,pageColors:c});(b.renderTasks||(b.renderTasks=new Set)).add(y);const E=y.task;return Promise.all([b.displayReadyCapability.promise,o]).then(([S,C])=>{var R;if(this.destroyed){_();return}if((R=this._stats)==null||R.time("Rendering"),!(C.renderingIntent&f))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");y.initializeGraphics({transparency:S,optionalContentConfig:C}),y.operatorListChanged()}).catch(_),E}getOperatorList({intent:t="display",annotationMode:e=ii.ENABLE,printAnnotationStorage:s=null}={}){var l;function i(){a.operatorList.lastChunk&&(a.opListReadCapability.resolve(a.operatorList),a.renderTasks.delete(o))}const n=this._transport.getRenderingIntent(t,e,s,!0);let a=this._intentStates.get(n.cacheKey);a||(a=Object.create(null),this._intentStates.set(n.cacheKey,a));let o;return a.opListReadCapability||(o=Object.create(null),o.operatorListChanged=i,a.opListReadCapability=Promise.withResolvers(),(a.renderTasks||(a.renderTasks=new Set)).add(o),a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(l=this._stats)==null||l.time("Page Request"),this._pumpOperatorList(n)),a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size(i){return i.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>va.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){function n(){a.read().then(function({value:l,done:c}){if(c){s(o);return}o.lang??(o.lang=l.lang),Object.assign(o.styles,l.styles),o.items.push(...l.items),n()},i)}const a=e.getReader(),o={items:[],styles:Object.create(null),lang:null};n()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),g(this,Is,!1),m(this,ke,El).call(this),Promise.all(t)}cleanup(t=!1){g(this,Is,!0);const e=m(this,ke,qn).call(this,!1);return t&&e&&this._stats&&(this._stats=new md),e}_startRenderPage(t,e){var i,n;const s=this._intentStates.get(e);s&&((i=this._stats)==null||i.timeEnd("Page Request"),(n=s.displayReadyCapability)==null||n.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&m(this,ke,qn).call(this,!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s}){const{map:i,transfer:n}=s,o=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:i},n).getReader(),l=this._intentStates.get(e);l.streamReader=o;const c=()=>{o.read().then(({value:d,done:u})=>{if(u){l.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(d,l),c())},d=>{if(l.streamReader=null,!this._transport.destroyed){if(l.operatorList){l.operatorList.lastChunk=!0;for(const u of l.renderTasks)u.operatorListChanged();m(this,ke,qn).call(this,!0)}if(l.displayReadyCapability)l.displayReadyCapability.reject(d);else if(l.opListReadCapability)l.opListReadCapability.reject(d);else throw d}})};c()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof id){let i=Lg;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new Bn(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,n]of this._intentStates)if(n===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}ui=new WeakMap,Is=new WeakMap,ke=new WeakSet,qn=function(t=!1){if(m(this,ke,El).call(this),!r(this,Is)||this.destroyed)return!1;if(t)return g(this,ui,setTimeout(()=>{g(this,ui,null),m(this,ke,qn).call(this,!1)},kg)),!1;for(const{renderTasks:e,operatorList:s}of this._intentStates.values())if(e.size>0||!s.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),g(this,Is,!1),!0},El=function(){r(this,ui)&&(clearTimeout(r(this,ui)),g(this,ui,null))};var nn,rh;class jg{constructor(){A(this,nn,new Set);A(this,rh,Promise.resolve())}postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};r(this,rh).then(()=>{for(const i of r(this,nn))i.call(this,s)})}addEventListener(t,e){r(this,nn).add(e)}removeEventListener(t,e){r(this,nn).delete(e)}terminate(){r(this,nn).clear()}}nn=new WeakMap,rh=new WeakMap;const _s={isWorkerDisabled:!1,fakeWorkerId:0};qt&&(_s.isWorkerDisabled=!0,Ws.workerSrc||(Ws.workerSrc="./pdf.worker.mjs")),_s.isSameOrigin=function(h,t){let e;try{if(e=new URL(h),!e.origin||e.origin==="null")return!1}catch{return!1}const s=new URL(t,e);return e.origin===s.origin},_s.createCDNWrapper=function(h){const t=`await import("${h}");`;return URL.createObjectURL(new Blob([t],{type:"text/javascript"}))};var rn,_r,wl;const me=class me{constructor({name:t=null,port:e=null,verbosity:s=vp()}={}){var i;if(this.name=t,this.destroyed=!1,this.verbosity=s,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if((i=r(me,rn))!=null&&i.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(r(me,rn)||g(me,rn,new WeakMap)).set(e,this),this._initializeFromPort(e);return}this._initialize()}get promise(){return qt?Promise.all([ps.promise,this._readyCapability.promise]):this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new oa("main","worker",t),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!_s.isWorkerDisabled&&!r(me,_r,wl)){let{workerSrc:t}=me;try{_s.isSameOrigin(window.location.href,t)||(t=_s.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new oa("main","worker",e),i=()=>{e.removeEventListener("error",n),s.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},n=()=>{this._webWorker||i()};e.addEventListener("error",n),s.on("test",o=>{if(e.removeEventListener("error",n),this.destroyed){i();return}o?(this._messageHandler=s,this._port=e,this._webWorker=e,this._readyCapability.resolve(),s.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),s.destroy(),e.terminate())}),s.on("ready",o=>{if(e.removeEventListener("error",n),this.destroyed){i();return}try{a()}catch{this._setupFakeWorker()}});const a=()=>{const o=new Uint8Array;s.send("test",o,[o.buffer])};a();return}catch{ph("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){_s.isWorkerDisabled||(q("Setting up fake worker."),_s.isWorkerDisabled=!0),me._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new jg;this._port=e;const s=`fake${_s.fakeWorkerId++}`,i=new oa(s+"_worker",s,e);t.setup(i,e);const n=new oa(s,s+"_worker",e);this._messageHandler=n,this._readyCapability.resolve(),n.send("configure",{verbosity:this.verbosity})}).catch(t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){var t;this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),(t=r(me,rn))==null||t.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(t){var s;if(!(t!=null&&t.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const e=(s=r(this,rn))==null?void 0:s.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new me(t)}static get workerSrc(){if(Ws.workerSrc)return Ws.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return J(this,"_setupFakeWorkerGlobal",(async()=>r(this,_r,wl)?r(this,_r,wl):(await import(this.workerSrc)).WorkerMessageHandler)())}};rn=new WeakMap,_r=new WeakSet,wl=function(){var t;try{return((t=globalThis.pdfjsWorker)==null?void 0:t.WorkerMessageHandler)||null}catch{return null}},A(me,_r),A(me,rn);let Jn=me;var Ds,is,Er,wr,ns,an,la;class $g{constructor(t,e,s,i,n){A(this,an);A(this,Ds,new Map);A(this,is,new Map);A(this,Er,new Map);A(this,wr,new Map);A(this,ns,null);this.messageHandler=t,this.loadingTask=e,this.commonObjs=new Hu,this.fontLoader=new Up({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}get annotationStorage(){return J(this,"annotationStorage",new od)}getRenderingIntent(t,e=ii.ENABLE,s=null,i=!1){let n=Le.DISPLAY,a=oc;switch(t){case"any":n=Le.ANY;break;case"display":break;case"print":n=Le.PRINT;break;default:q(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case ii.DISABLE:n+=Le.ANNOTATIONS_DISABLE;break;case ii.ENABLE:break;case ii.ENABLE_FORMS:n+=Le.ANNOTATIONS_FORMS;break;case ii.ENABLE_STORAGE:n+=Le.ANNOTATIONS_STORAGE,a=(n&Le.PRINT&&s instanceof wu?s:this.annotationStorage).serializable;break;default:q(`getRenderingIntent - invalid annotationMode: ${e}`)}return i&&(n+=Le.OPLIST),{renderingIntent:n,cacheKey:`${n}_${a.hash}`,annotationStorageSerializable:a}}destroy(){var s;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),(s=r(this,ns))==null||s.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const i of r(this,is).values())t.push(i._destroy());r(this,is).clear(),r(this,Er).clear(),r(this,wr).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{var i;this.commonObjs.clear(),this.fontLoader.clear(),r(this,Ds).clear(),this.filterFactory.destroy(),$l.cleanup(),(i=this._networkStream)==null||i.cancelAllRequests(new Bn("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{Lt(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=n=>{this._lastProgress={loaded:n.loaded,total:n.total}},i.onPull=()=>{this._fullReader.read().then(function({value:n,done:a}){if(a){i.close();return}Lt(n instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(n),1,[n])}).catch(n=>{i.error(n)})},i.onCancel=n=>{this._fullReader.cancel(n),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("ReaderHeadersReady",s=>{const i=Promise.withResolvers(),n=this._fullReader;return n.headersReady.then(()=>{var a;(!n.isStreamingSupported||!n.isRangeSupported)&&(this._lastProgress&&((a=e.onProgress)==null||a.call(e,this._lastProgress)),n.onProgress=o=>{var l;(l=e.onProgress)==null||l.call(e,{loaded:o.loaded,total:o.total})}),i.resolve({isStreamingSupported:n.isStreamingSupported,isRangeSupported:n.isRangeSupported,contentLength:n.contentLength})},i.reject),i.promise}),t.on("GetRangeReader",(s,i)=>{Lt(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const n=this._networkStream.getRangeReader(s.begin,s.end);if(!n){i.close();return}i.onPull=()=>{n.read().then(function({value:a,done:o}){if(o){i.close();return}Lt(a instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(a),1,[a])}).catch(a=>{i.error(a)})},i.onCancel=a=>{n.cancel(a),i.ready.catch(o=>{if(!this.destroyed)throw o})}}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new Bg(s,this))}),t.on("DocException",function(s){let i;switch(s.name){case"PasswordException":i=new Oh(s.message,s.code);break;case"InvalidPDFException":i=new Vd(s.message);break;case"MissingPDFException":i=new Hn(s.message);break;case"UnexpectedResponseException":i=new gh(s.message,s.status);break;case"UnknownErrorException":i=new Nh(s.message,s.details);break;default:at("DocException - expected a valid Error.")}e._capability.reject(i)}),t.on("PasswordRequest",s=>{if(g(this,ns,Promise.withResolvers()),e.onPassword){const i=n=>{n instanceof Error?r(this,ns).reject(n):r(this,ns).resolve({password:n})};try{e.onPassword(i,s.code)}catch(n){r(this,ns).reject(n)}}else r(this,ns).reject(new Oh(s.message,s.code));return r(this,ns).promise}),t.on("DataLoaded",s=>{var i;(i=e.onProgress)==null||i.call(e,{loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{if(this.destroyed)return;r(this,is).get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,n])=>{var a;if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":const{disableFontFace:o,fontExtraProperties:l,pdfBug:c}=this._params;if("error"in n){const p=n.error;q(`Error during font loading: ${p}`),this.commonObjs.resolve(s,p);break}const d=c&&((a=globalThis.FontInspector)!=null&&a.enabled)?(p,b)=>globalThis.FontInspector.fontAdded(p,b):null,u=new jp(n,{disableFontFace:o,inspectFont:d});this.fontLoader.bind(u).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!l&&u.data&&(u.data=null),this.commonObjs.resolve(s,u)});break;case"CopyLocalImage":const{imageRef:f}=n;Lt(f,"The imageRef must be defined.");for(const p of r(this,is).values())for(const[,b]of p.objs)if((b==null?void 0:b.ref)===f)return b.dataLen?(this.commonObjs.resolve(s,structuredClone(b)),b.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,n);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,n,a])=>{var l;if(this.destroyed)return;const o=r(this,is).get(i);if(!o.objs.has(s)){if(o._intentStates.size===0){(l=a==null?void 0:a.bitmap)==null||l.close();return}switch(n){case"Image":o.objs.resolve(s,a),(a==null?void 0:a.dataLen)>pp&&(o._maybeCleanupAfterRender=!0);break;case"Pattern":o.objs.resolve(s,a);break;default:throw new Error(`Got unknown object type ${n}`)}}}),t.on("DocProgress",s=>{var i;this.destroyed||(i=e.onProgress)==null||i.call(e,{loaded:s.loaded,total:s.total})}),t.on("FetchBuiltInCMap",s=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(s):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))),t.on("FetchStandardFontData",s=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(s):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.")))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var s;this.annotationStorage.size<=0&&q("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:((s=this._fullReader)==null?void 0:s.filename)??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=r(this,Er).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(n=>{if(this.destroyed)throw new Error("Transport destroyed");n.refStr&&r(this,wr).set(n.refStr,t);const a=new Ug(e,n,this,this._params.pdfBug);return r(this,is).set(e,a),a});return r(this,Er).set(e,i),i}getPageIndex(t){return Ld(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return m(this,an,la).call(this,"GetFieldObjects")}hasJSActions(){return m(this,an,la).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return m(this,an,la).call(this,"GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return m(this,an,la).call(this,"GetOptionalContentConfig").then(e=>new og(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=r(this,Ds).get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>{var n,a;return{info:i[0],metadata:i[1]?new rg(i[1]):null,contentDispositionFilename:((n=this._fullReader)==null?void 0:n.filename)??null,contentLength:((a=this._fullReader)==null?void 0:a.contentLength)??null}});return r(this,Ds).set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const e of r(this,is).values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),r(this,Ds).clear(),this.filterFactory.destroy(!0),$l.cleanup()}}cachedPageNumber(t){if(!Ld(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return r(this,wr).get(e)??null}}Ds=new WeakMap,is=new WeakMap,Er=new WeakMap,wr=new WeakMap,ns=new WeakMap,an=new WeakSet,la=function(t,e=null){const s=r(this,Ds).get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return r(this,Ds).set(t,i),i};const ul=Symbol("INITIAL_DATA");var je,co,mc;class Hu{constructor(){A(this,co);A(this,je,Object.create(null))}get(t,e=null){if(e){const i=m(this,co,mc).call(this,t);return i.promise.then(()=>e(i.data)),null}const s=r(this,je)[t];if(!s||s.data===ul)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=r(this,je)[t];return!!e&&e.data!==ul}resolve(t,e=null){const s=m(this,co,mc).call(this,t);s.data=e,s.resolve()}clear(){var t;for(const e in r(this,je)){const{data:s}=r(this,je)[e];(t=s==null?void 0:s.bitmap)==null||t.close()}g(this,je,Object.create(null))}*[Symbol.iterator](){for(const t in r(this,je)){const{data:e}=r(this,je)[t];e!==ul&&(yield[t,e])}}}je=new WeakMap,co=new WeakSet,mc=function(t){var e;return(e=r(this,je))[t]||(e[t]={...Promise.withResolvers(),data:ul})};var fi;class zg{constructor(t){A(this,fi,null);g(this,fi,t),this.onContinue=null}get promise(){return r(this,fi).capability.promise}cancel(t=0){r(this,fi).cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=r(this,fi).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=r(this,fi);return t.form||t.canvas&&(e==null?void 0:e.size)>0}}fi=new WeakMap;var on;const Fi=class Fi{constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:n,operatorList:a,pageIndex:o,canvasFactory:l,filterFactory:c,useRequestAnimationFrame:d=!1,pdfBug:u=!1,pageColors:f=null}){this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=o,this.canvasFactory=l,this.filterFactory=c,this._pdfBug=u,this.pageColors=f,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=d===!0&&typeof window<"u",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new zg(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){var o,l;if(this.cancelled)return;if(this._canvas){if(r(Fi,on).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");r(Fi,on).add(this._canvas)}this._pdfBug&&((o=globalThis.StepperManager)!=null&&o.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:s,viewport:i,transform:n,background:a}=this.params;this.gfx=new Qn(s,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:n,viewport:i,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,(l=this.graphicsReadyCallback)==null||l.call(this)}cancel(t=null,e=0){var s;this.running=!1,this.cancelled=!0,(s=this.gfx)==null||s.endDrawing(),r(Fi,on).delete(this._canvas),this.callback(t||new id(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){var t;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(t=this.stepper)==null||t.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame(()=>{this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),r(Fi,on).delete(this._canvas),this.callback())))}};on=new WeakMap,A(Fi,on,new WeakSet);let bc=Fi;const Gg="4.3.136",Vg="0cec64437";function kd(h){return Math.floor(Math.max(0,Math.min(1,h))*255).toString(16).padStart(2,"0")}function ta(h){return Math.max(0,Math.min(255,255*h))}class Md{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return t=ta(t),[t,t,t]}static G_HTML([t]){const e=kd(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(ta)}static RGB_HTML(t){return`#${t.map(kd).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[ta(1-Math.min(1,t+i)),ta(1-Math.min(1,s+i)),ta(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,n=1-e,a=1-s,o=Math.min(i,n,a);return["CMYK",i,n,a,o]}}class Bu{static setupStorage(t,e,s,i,n){const a=i.getValue(e,{value:null});switch(s.name){case"textarea":if(a.value!==null&&(t.textContent=a.value),n==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(a.value===s.attributes.xfaOn?t.setAttribute("checked",!0):a.value===s.attributes.xfaOff&&t.removeAttribute("checked"),n==="print")break;t.addEventListener("change",o=>{i.setValue(e,{value:o.target.checked?o.target.getAttribute("xfaOn"):o.target.getAttribute("xfaOff")})})}else{if(a.value!==null&&t.setAttribute("value",a.value),n==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})})}break;case"select":if(a.value!==null){t.setAttribute("value",a.value);for(const o of s.children)o.attributes.value===a.value?o.attributes.selected=!0:o.attributes.hasOwnProperty("selected")&&delete o.attributes.selected}t.addEventListener("input",o=>{const l=o.target.options,c=l.selectedIndex===-1?"":l[l.selectedIndex].value;i.setValue(e,{value:c})});break}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:n}){const{attributes:a}=e,o=t instanceof HTMLAnchorElement;a.type==="radio"&&(a.name=`${a.name}-${i}`);for(const[l,c]of Object.entries(a))if(c!=null)switch(l){case"class":c.length&&t.setAttribute(l,c.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",c);break;case"style":Object.assign(t.style,c);break;case"textContent":t.textContent=c;break;default:(!o||l!=="href"&&l!=="newWindow")&&t.setAttribute(l,c)}o&&n.addLinkAttributes(t,a.href,a.newWindow),s&&a.dataId&&this.setupStorage(t,a.dataId,e,s)}static render(t){var u,f;const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,n=t.intent||"display",a=document.createElement(i.name);i.attributes&&this.setAttributes({html:a,element:i,intent:n,linkService:s});const o=n!=="richText",l=t.div;if(l.append(a),t.viewport){const p=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=p}o&&l.setAttribute("class","xfaLayer xfaFont");const c=[];if(i.children.length===0){if(i.value){const p=document.createTextNode(i.value);a.append(p),o&&va.shouldBuildText(i.name)&&c.push(p)}return{textDivs:c}}const d=[[i,-1,a]];for(;d.length>0;){const[p,b,v]=d.at(-1);if(b+1===p.children.length){d.pop();continue}const _=p.children[++d.at(-1)[1]];if(_===null)continue;const{name:y}=_;if(y==="#text"){const w=document.createTextNode(_.value);c.push(w),v.append(w);continue}const E=(u=_==null?void 0:_.attributes)!=null&&u.xmlns?document.createElementNS(_.attributes.xmlns,y):document.createElement(y);if(v.append(E),_.attributes&&this.setAttributes({html:E,element:_,storage:e,intent:n,linkService:s}),((f=_.children)==null?void 0:f.length)>0)d.push([_,-1,E]);else if(_.value){const w=document.createTextNode(_.value);o&&va.shouldBuildText(y)&&c.push(w),E.append(w)}}for(const p of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))p.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const il=1e3,Wg=9,Dn=new WeakSet;function qs(h){return{width:h[2]-h[0],height:h[3]-h[1]}}class qg{static create(t){switch(t.data.annotationType){case xt.LINK:return new Uu(t);case xt.TEXT:return new Xg(t);case xt.WIDGET:switch(t.data.fieldType){case"Tx":return new Yg(t);case"Btn":return t.data.radioButton?new zu(t):t.data.checkBox?new Qg(t):new Jg(t);case"Ch":return new Zg(t);case"Sig":return new Kg(t)}return new Un(t);case xt.POPUP:return new vc(t);case xt.FREETEXT:return new Xu(t);case xt.LINE:return new em(t);case xt.SQUARE:return new sm(t);case xt.CIRCLE:return new im(t);case xt.POLYLINE:return new Yu(t);case xt.CARET:return new rm(t);case xt.INK:return new Ku(t);case xt.POLYGON:return new nm(t);case xt.HIGHLIGHT:return new am(t);case xt.UNDERLINE:return new om(t);case xt.SQUIGGLY:return new lm(t);case xt.STRIKEOUT:return new hm(t);case xt.STAMP:return new Qu(t);case xt.FILEATTACHMENT:return new cm(t);default:return new Et(t)}}}var ln,Sr,xr,uo,Ac;const fd=class fd{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){A(this,uo);A(this,ln,null);A(this,Sr,!1);A(this,xr,null);this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:s}){return!!(t!=null&&t.str||e!=null&&e.str||s!=null&&s.str)}get hasPopupData(){return fd._hasPopupData(this.data)}updateEdited(t){var s;if(!this.container)return;r(this,ln)||g(this,ln,{rect:this.data.rect.slice(0)});const{rect:e}=t;e&&m(this,uo,Ac).call(this,e),(s=r(this,xr))==null||s.popup.updateEdited(t)}resetEdited(){var t;r(this,ln)&&(m(this,uo,Ac).call(this,r(this,ln).rect),(t=r(this,xr))==null||t.popup.resetEdited(),g(this,ln,null))}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof Un||(n.tabIndex=il);const{style:a}=n;if(a.zIndex=this.parent.zIndex++,e.popupRef&&n.setAttribute("aria-haspopup","dialog"),e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof vc){const{rotation:v}=e;return!e.hasOwnCanvas&&v!==0&&this.setRotation(v,n),n}const{width:o,height:l}=qs(e.rect);if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const v=e.borderStyle.horizontalCornerRadius,_=e.borderStyle.verticalCornerRadius;if(v>0||_>0){const E=`calc(${v}px * var(--scale-factor)) / calc(${_}px * var(--scale-factor))`;a.borderRadius=E}else if(this instanceof zu){const E=`calc(${o}px * var(--scale-factor)) / calc(${l}px * var(--scale-factor))`;a.borderRadius=E}switch(e.borderStyle.style){case Jr.SOLID:a.borderStyle="solid";break;case Jr.DASHED:a.borderStyle="dashed";break;case Jr.BEVELED:q("Unimplemented border style: beveled");break;case Jr.INSET:q("Unimplemented border style: inset");break;case Jr.UNDERLINE:a.borderBottomStyle="solid";break}const y=e.borderColor||null;y?(g(this,Sr,!0),a.borderColor=F.makeHexColor(y[0]|0,y[1]|0,y[2]|0)):a.borderWidth=0}const c=F.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:d,pageHeight:u,pageX:f,pageY:p}=i.rawDims;a.left=`${100*(c[0]-f)/d}%`,a.top=`${100*(c[1]-p)/u}%`;const{rotation:b}=e;return e.hasOwnCanvas||b===0?(a.width=`${100*o/d}%`,a.height=`${100*l/u}%`):this.setRotation(b,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims,{width:n,height:a}=qs(this.data.rect);let o,l;t%180===0?(o=100*n/s,l=100*a/i):(o=100*a/s,l=100*n/i),e.style.width=`${o}%`,e.style.height=`${l}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const n=i.detail[e],a=n[0],o=n.slice(1);i.target.style[s]=Md[`${a}_HTML`](o),this.annotationStorage.setValue(this.data.id,{[s]:Md[`${a}_rgb`](o)})};return J(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail)){const n=t[i]||s[i];n==null||n(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,n]of Object.entries(e)){const a=s[i];if(a){const o={detail:{[i]:n},target:t};a(o),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,n]=this.data.rect;if(t.length===1){const[,{x:v,y:_},{x:y,y:E}]=t[0];if(i===v&&n===_&&e===y&&s===E)return}const{style:a}=this.container;let o;if(r(this,Sr)){const{borderColor:v,borderWidth:_}=a;a.borderWidth=0,o=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${v}" stroke-width="${_}">`],this.container.classList.add("hasBorder")}const l=i-e,c=n-s,{svgFactory:d}=this,u=d.createElement("svg");u.classList.add("quadrilateralsContainer"),u.setAttribute("width",0),u.setAttribute("height",0);const f=d.createElement("defs");u.append(f);const p=d.createElement("clipPath"),b=`clippath_${this.data.id}`;p.setAttribute("id",b),p.setAttribute("clipPathUnits","objectBoundingBox"),f.append(p);for(const[,{x:v,y:_},{x:y,y:E}]of t){const w=d.createElement("rect"),x=(y-e)/l,S=(n-_)/c,C=(v-y)/l,R=(_-E)/c;w.setAttribute("x",x),w.setAttribute("y",S),w.setAttribute("width",C),w.setAttribute("height",R),p.append(w),o==null||o.push(`<rect vector-effect="non-scaling-stroke" x="${x}" y="${S}" width="${C}" height="${R}"/>`)}r(this,Sr)&&(o.push("</g></svg>')"),a.backgroundImage=o.join("")),this.container.append(u),this.container.style.clipPath=`url(#${b})`}_createPopup(){const{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");const s=g(this,xr,new vc({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]}));this.parent.div.append(s.render())}render(){at("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:n,id:a,exportValues:o}of i){if(n===-1||a===e)continue;const l=typeof o=="string"?o:null,c=document.querySelector(`[data-element-id="${a}"]`);if(c&&!Dn.has(c)){q(`_getElementsByName - element not allowed: ${a}`);continue}s.push({id:a,exportValue:l,domElement:c})}return s}for(const i of document.getElementsByName(t)){const{exportValue:n}=i,a=i.getAttribute("data-element-id");a!==e&&Dn.has(i)&&s.push({id:a,exportValue:n,domElement:i})}return s}show(){var t;this.container&&(this.container.hidden=!1),(t=this.popup)==null||t.maybeShow()}hide(){var t;this.container&&(this.container.hidden=!0),(t=this.popup)==null||t.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}get _isEditable(){return!1}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{var s;(s=this.linkService.eventBus)==null||s.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}};ln=new WeakMap,Sr=new WeakMap,xr=new WeakMap,uo=new WeakSet,Ac=function(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:a,pageX:o,pageY:l}}}}=this;s==null||s.splice(0,4,...t);const{width:c,height:d}=qs(t);e.left=`${100*(t[0]-o)/n}%`,e.top=`${100*(a-t[3]+l)/a}%`,i===0?(e.width=`${100*c/n}%`,e.height=`${100*d/a}%`):this.setRotation(i)};let Et=fd;var we,ki,ju,$u;class Uu extends Et{constructor(e,s=null){super(e,{isRenderable:!0,ignoreBorder:!!(s!=null&&s.ignoreBorder),createQuadrilaterals:!0});A(this,we);this.isTooltipOnly=e.data.isTooltipOnly}render(){const{data:e,linkService:s}=this,i=document.createElement("a");i.setAttribute("data-element-id",e.id);let n=!1;return e.url?(s.addLinkAttributes(i,e.url,e.newWindow),n=!0):e.action?(this._bindNamedAction(i,e.action),n=!0):e.attachment?(m(this,we,ju).call(this,i,e.attachment,e.attachmentDest),n=!0):e.setOCGState?(m(this,we,$u).call(this,i,e.setOCGState),n=!0):e.dest?(this._bindLink(i,e.dest),n=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,e),n=!0),e.resetForm?(this._bindResetFormAction(i,e.resetForm),n=!0):this.isTooltipOnly&&!n&&(this._bindLink(i,""),n=!0)),this.container.classList.add("linkAnnotation"),n&&this.container.append(i),this.container}_bindLink(e,s){e.href=this.linkService.getDestinationHash(s),e.onclick=()=>(s&&this.linkService.goToDestination(s),!1),(s||s==="")&&m(this,we,ki).call(this)}_bindNamedAction(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeNamedAction(s),!1),m(this,we,ki).call(this)}_bindJSAction(e,s){e.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const n of Object.keys(s.actions)){const a=i.get(n);a&&(e[a]=()=>{var o;return(o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:s.id,name:n}}),!1})}e.onclick||(e.onclick=()=>!1),m(this,we,ki).call(this)}_bindResetFormAction(e,s){const i=e.onclick;if(i||(e.href=this.linkService.getAnchorUrl("")),m(this,we,ki).call(this),!this._fieldObjects){q('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),i||(e.onclick=()=>!1);return}e.onclick=()=>{var u;i==null||i();const{fields:n,refs:a,include:o}=s,l=[];if(n.length!==0||a.length!==0){const f=new Set(a);for(const p of n){const b=this._fieldObjects[p]||[];for(const{id:v}of b)f.add(v)}for(const p of Object.values(this._fieldObjects))for(const b of p)f.has(b.id)===o&&l.push(b)}else for(const f of Object.values(this._fieldObjects))l.push(...f);const c=this.annotationStorage,d=[];for(const f of l){const{id:p}=f;switch(d.push(p),f.type){case"text":{const v=f.defaultValue||"";c.setValue(p,{value:v});break}case"checkbox":case"radiobutton":{const v=f.defaultValue===f.exportValues;c.setValue(p,{value:v});break}case"combobox":case"listbox":{const v=f.defaultValue||"";c.setValue(p,{value:v});break}default:continue}const b=document.querySelector(`[data-element-id="${p}"]`);if(b){if(!Dn.has(b)){q(`_bindResetFormAction - element not allowed: ${p}`);continue}}else continue;b.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((u=this.linkService.eventBus)==null||u.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:d,name:"ResetForm"}})),!1}}}we=new WeakSet,ki=function(){this.container.setAttribute("data-internal-link","")},ju=function(e,s,i=null){e.href=this.linkService.getAnchorUrl(""),s.description&&(e.title=s.description),e.onclick=()=>{var n;return(n=this.downloadManager)==null||n.openOrDownloadData(s.content,s.filename,i),!1},m(this,we,ki).call(this)},$u=function(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeSetOCGState(s),!1),m(this,we,ki).call(this)};class Xg extends Et{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class Un extends Et{render(){return this.container}showElementAndHideCanvas(t){var e;this.data.hasOwnCanvas&&(((e=t.previousSibling)==null?void 0:e.nodeName)==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return he.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,n){s.includes("mouse")?t.addEventListener(s,a=>{var o;(o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(a),shift:a.shiftKey,modifier:this._getKeyModifier(a)}})}):t.addEventListener(s,a=>{var o;if(s==="blur"){if(!e.focused||!a.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}n&&((o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(a)}}))})}_setEventListeners(t,e,s,i){var n,a,o;for(const[l,c]of s)(c==="Action"||(n=this.data.actions)!=null&&n[c])&&((c==="Focus"||c==="Blur")&&(e||(e={focused:!1})),this._setEventListener(t,e,l,c,i),c==="Focus"&&!((a=this.data.actions)!=null&&a.Blur)?this._setEventListener(t,e,"blur","Blur",null):c==="Blur"&&!((o=this.data.actions)!=null&&o.Focus)&&this._setEventListener(t,e,"focus","Focus",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":F.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||Wg,n=t.style;let a;const o=2,l=c=>Math.round(10*c)/10;if(this.data.multiLine){const c=Math.abs(this.data.rect[3]-this.data.rect[1]-o),d=Math.round(c/(Rh*i))||1,u=c/d;a=Math.min(i,l(u/Rh))}else{const c=Math.abs(this.data.rect[3]-this.data.rect[1]-o);a=Math.min(i,l(c/Rh))}n.fontSize=`calc(${a}px * var(--scale-factor))`,n.color=F.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class Yg extends Un{constructor(t){const e=t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,s,i){const n=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=s),n.setValue(a.id,{[i]:s})}render(){var i,n;const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const a=t.getValue(e,{value:this.data.fieldValue});let o=a.value||"";const l=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;l&&o.length>l&&(o=o.slice(0,l));let c=a.formattedValue||((i=this.data.textContent)==null?void 0:i.join(`
`))||null;c&&this.data.comb&&(c=c.replaceAll(/\s+/g,""));const d={userValue:o,formattedValue:c,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=c??o,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type="text",s.setAttribute("value",c??o),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),Dn.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=il,this._setRequired(s,this.data.required),l&&(s.maxLength=l),s.addEventListener("input",f=>{t.setValue(e,{value:f.target.value}),this.setPropertyOnSiblings(s,"value",f.target.value,"value"),d.formattedValue=null}),s.addEventListener("resetform",f=>{const p=this.data.defaultFieldValue??"";s.value=d.userValue=p,d.formattedValue=null});let u=f=>{const{formattedValue:p}=d;p!=null&&(f.target.value=p),f.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",p=>{var v;if(d.focused)return;const{target:b}=p;d.userValue&&(b.value=d.userValue),d.lastCommittedValue=b.value,d.commitKey=1,(v=this.data.actions)!=null&&v.Focus||(d.focused=!0)}),s.addEventListener("updatefromsandbox",p=>{this.showElementAndHideCanvas(p.target);const b={value(v){d.userValue=v.detail.value??"",t.setValue(e,{value:d.userValue.toString()}),v.target.value=d.userValue},formattedValue(v){const{formattedValue:_}=v.detail;d.formattedValue=_,_!=null&&v.target!==document.activeElement&&(v.target.value=_),t.setValue(e,{formattedValue:_})},selRange(v){v.target.setSelectionRange(...v.detail.selRange)},charLimit:v=>{var w;const{charLimit:_}=v.detail,{target:y}=v;if(_===0){y.removeAttribute("maxLength");return}y.setAttribute("maxLength",_);let E=d.userValue;!E||E.length<=_||(E=E.slice(0,_),y.value=d.userValue=E,t.setValue(e,{value:E}),(w=this.linkService.eventBus)==null||w.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:E,willCommit:!0,commitKey:1,selStart:y.selectionStart,selEnd:y.selectionEnd}}))}};this._dispatchEventFromSandbox(b,p)}),s.addEventListener("keydown",p=>{var _;d.commitKey=1;let b=-1;if(p.key==="Escape"?b=0:p.key==="Enter"&&!this.data.multiLine?b=2:p.key==="Tab"&&(d.commitKey=3),b===-1)return;const{value:v}=p.target;d.lastCommittedValue!==v&&(d.lastCommittedValue=v,d.userValue=v,(_=this.linkService.eventBus)==null||_.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:v,willCommit:!0,commitKey:b,selStart:p.target.selectionStart,selEnd:p.target.selectionEnd}}))});const f=u;u=null,s.addEventListener("blur",p=>{var v,_;if(!d.focused||!p.relatedTarget)return;(v=this.data.actions)!=null&&v.Blur||(d.focused=!1);const{value:b}=p.target;d.userValue=b,d.lastCommittedValue!==b&&((_=this.linkService.eventBus)==null||_.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:b,willCommit:!0,commitKey:d.commitKey,selStart:p.target.selectionStart,selEnd:p.target.selectionEnd}})),f(p)}),(n=this.data.actions)!=null&&n.Keystroke&&s.addEventListener("beforeinput",p=>{var S;d.lastCommittedValue=null;const{data:b,target:v}=p,{value:_,selectionStart:y,selectionEnd:E}=v;let w=y,x=E;switch(p.inputType){case"deleteWordBackward":{const C=_.substring(0,y).match(/\w*[^\w]*$/);C&&(w-=C[0].length);break}case"deleteWordForward":{const C=_.substring(y).match(/^[^\w]*\w*/);C&&(x+=C[0].length);break}case"deleteContentBackward":y===E&&(w-=1);break;case"deleteContentForward":y===E&&(x+=1);break}p.preventDefault(),(S=this.linkService.eventBus)==null||S.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:_,change:b||"",willCommit:!1,selStart:w,selEnd:x}})}),this._setEventListeners(s,d,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],p=>p.target.value)}if(u&&s.addEventListener("blur",u),this.data.comb){const p=(this.data.rect[2]-this.data.rect[0])/l;s.classList.add("comb"),s.style.letterSpacing=`calc(${p}px * var(--scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class Kg extends Un{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class Qg extends Un{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return Dn.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=il,n.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const c of this._getElementsByName(o,s)){const d=l&&c.exportValue===e.exportValue;c.domElement&&(c.domElement.checked=d),t.setValue(c.id,{value:d})}t.setValue(s,{value:l})}),n.addEventListener("resetform",a=>{const o=e.defaultFieldValue||"Off";a.target.checked=o===e.exportValue}),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",a=>{const o={value(l){l.target.checked=l.detail.value!=="Off",t.setValue(s,{value:l.target.checked})}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class zu extends Un{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const a of this._getElementsByName(e.fieldName,s))t.setValue(a.id,{value:!1});const n=document.createElement("input");if(Dn.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.tabIndex=il,n.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const c of this._getElementsByName(o,s))t.setValue(c.id,{value:!1});t.setValue(s,{value:l})}),n.addEventListener("resetform",a=>{const o=e.defaultFieldValue;a.target.checked=o!=null&&o===e.buttonValue}),this.enableScripting&&this.hasJSActions){const a=e.buttonValue;n.addEventListener("updatefromsandbox",o=>{const l={value:c=>{const d=a===c.detail.value;for(const u of this._getElementsByName(c.target.name)){const f=d&&u.id===s;u.domElement&&(u.domElement.checked=f),t.setValue(u.id,{value:f})}}};this._dispatchEventFromSandbox(l,o)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],o=>o.target.checked)}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Jg extends Uu{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class Zg extends Un{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");Dn.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=il;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",d=>{const u=this.data.defaultFieldValue;for(const f of i.options)f.selected=f.value===u});for(const d of this.data.options){const u=document.createElement("option");u.textContent=d.displayValue,u.value=d.exportValue,s.value.includes(d.exportValue)&&(u.setAttribute("selected",!0),n=!1),i.append(u)}let a=null;if(n){const d=document.createElement("option");d.value=" ",d.setAttribute("hidden",!0),d.setAttribute("selected",!0),i.prepend(d),a=()=>{d.remove(),i.removeEventListener("input",a),a=null},i.addEventListener("input",a)}const o=d=>{const u=d?"value":"textContent",{options:f,multiple:p}=i;return p?Array.prototype.filter.call(f,b=>b.selected).map(b=>b[u]):f.selectedIndex===-1?null:f[f.selectedIndex][u]};let l=o(!1);const c=d=>{const u=d.target.options;return Array.prototype.map.call(u,f=>({displayValue:f.textContent,exportValue:f.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",d=>{const u={value(f){a==null||a();const p=f.detail.value,b=new Set(Array.isArray(p)?p:[p]);for(const v of i.options)v.selected=b.has(v.value);t.setValue(e,{value:o(!0)}),l=o(!1)},multipleSelection(f){i.multiple=!0},remove(f){const p=i.options,b=f.detail.remove;p[b].selected=!1,i.remove(b),p.length>0&&Array.prototype.findIndex.call(p,_=>_.selected)===-1&&(p[0].selected=!0),t.setValue(e,{value:o(!0),items:c(f)}),l=o(!1)},clear(f){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),l=o(!1)},insert(f){const{index:p,displayValue:b,exportValue:v}=f.detail.insert,_=i.children[p],y=document.createElement("option");y.textContent=b,y.value=v,_?_.before(y):i.append(y),t.setValue(e,{value:o(!0),items:c(f)}),l=o(!1)},items(f){const{items:p}=f.detail;for(;i.length!==0;)i.remove(0);for(const b of p){const{displayValue:v,exportValue:_}=b,y=document.createElement("option");y.textContent=v,y.value=_,i.append(y)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:o(!0),items:c(f)}),l=o(!1)},indices(f){const p=new Set(f.detail.indices);for(const b of f.target.options)b.selected=p.has(b.index);t.setValue(e,{value:o(!0)}),l=o(!1)},editable(f){f.target.disabled=!f.detail.editable}};this._dispatchEventFromSandbox(u,d)}),i.addEventListener("input",d=>{var p;const u=o(!0),f=o(!1);t.setValue(e,{value:u}),d.preventDefault(),(p=this.linkService.eventBus)==null||p.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:l,change:f,changeEx:u,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],d=>d.target.value)):i.addEventListener("input",function(d){t.setValue(e,{value:o(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class vc extends Et{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:Et._hasPopupData(e)}),this.elements=s,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new tm({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const s of this.elements)s.popup=t,e.push(s.data.id),s.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(s=>`${qd}${s}`).join(",")),this.container}}var Cr,ah,oh,Tr,hn,At,Fs,cn,fo,po,Rr,Os,$e,Ns,go,Hs,mo,dn,un,ot,Sl,yc,Gu,Vu,Wu,qu,xl,Cl,_c;class tm{constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:n,contentsObj:a,richText:o,parent:l,rect:c,parentRect:d,open:u}){A(this,ot);A(this,Cr,m(this,ot,Wu).bind(this));A(this,ah,m(this,ot,_c).bind(this));A(this,oh,m(this,ot,Cl).bind(this));A(this,Tr,m(this,ot,xl).bind(this));A(this,hn,null);A(this,At,null);A(this,Fs,null);A(this,cn,null);A(this,fo,null);A(this,po,null);A(this,Rr,null);A(this,Os,!1);A(this,$e,null);A(this,Ns,null);A(this,go,null);A(this,Hs,null);A(this,mo,null);A(this,dn,null);A(this,un,!1);var f;g(this,At,t),g(this,mo,i),g(this,Fs,a),g(this,Hs,o),g(this,po,l),g(this,hn,e),g(this,go,c),g(this,Rr,d),g(this,fo,s),g(this,cn,Jd.toDateObject(n)),this.trigger=s.flatMap(p=>p.getElementsToTriggerPopup());for(const p of this.trigger)p.addEventListener("click",r(this,Tr)),p.addEventListener("mouseenter",r(this,oh)),p.addEventListener("mouseleave",r(this,ah)),p.classList.add("popupTriggerArea");for(const p of s)(f=p.container)==null||f.addEventListener("keydown",r(this,Cr));r(this,At).hidden=!0,u&&m(this,ot,xl).call(this)}render(){if(r(this,$e))return;const t=g(this,$e,document.createElement("div"));if(t.className="popup",r(this,hn)){const n=t.style.outlineColor=F.makeHexColor(...r(this,hn));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?t.style.backgroundColor=`color-mix(in srgb, ${n} 30%, white)`:t.style.backgroundColor=F.makeHexColor(...r(this,hn).map(o=>Math.floor(.7*(255-o)+o)))}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),{dir:s.dir,str:s.textContent}=r(this,mo),t.append(e),r(this,cn)){const n=document.createElement("span");n.classList.add("popupDate"),n.setAttribute("data-l10n-id","pdfjs-annotation-date-string"),n.setAttribute("data-l10n-args",JSON.stringify({date:r(this,cn).toLocaleDateString(),time:r(this,cn).toLocaleTimeString()})),e.append(n)}const i=r(this,ot,Sl);if(i)Bu.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const n=this._formatContents(r(this,Fs));t.append(n)}r(this,At).append(t)}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let n=0,a=i.length;n<a;++n){const o=i[n];s.append(document.createTextNode(o)),n<a-1&&s.append(document.createElement("br"))}return s}updateEdited({rect:t,popupContent:e}){var s;r(this,dn)||g(this,dn,{contentsObj:r(this,Fs),richText:r(this,Hs)}),t&&g(this,Ns,null),e&&(g(this,Hs,m(this,ot,Vu).call(this,e)),g(this,Fs,null)),(s=r(this,$e))==null||s.remove(),g(this,$e,null)}resetEdited(){var t;r(this,dn)&&({contentsObj:pe(this,Fs)._,richText:pe(this,Hs)._}=r(this,dn),g(this,dn,null),(t=r(this,$e))==null||t.remove(),g(this,$e,null),g(this,Ns,null))}forceHide(){g(this,un,this.isVisible),r(this,un)&&(r(this,At).hidden=!0)}maybeShow(){r(this,un)&&(r(this,$e)||m(this,ot,Cl).call(this),g(this,un,!1),r(this,At).hidden=!1)}get isVisible(){return r(this,At).hidden===!1}}Cr=new WeakMap,ah=new WeakMap,oh=new WeakMap,Tr=new WeakMap,hn=new WeakMap,At=new WeakMap,Fs=new WeakMap,cn=new WeakMap,fo=new WeakMap,po=new WeakMap,Rr=new WeakMap,Os=new WeakMap,$e=new WeakMap,Ns=new WeakMap,go=new WeakMap,Hs=new WeakMap,mo=new WeakMap,dn=new WeakMap,un=new WeakMap,ot=new WeakSet,Sl=function(){const t=r(this,Hs),e=r(this,Fs);return t!=null&&t.str&&(!(e!=null&&e.str)||e.str===t.str)&&r(this,Hs).html||null},yc=function(){var t,e,s;return((s=(e=(t=r(this,ot,Sl))==null?void 0:t.attributes)==null?void 0:e.style)==null?void 0:s.fontSize)||0},Gu=function(){var t,e,s;return((s=(e=(t=r(this,ot,Sl))==null?void 0:t.attributes)==null?void 0:e.style)==null?void 0:s.color)||null},Vu=function(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:r(this,ot,Gu),fontSize:r(this,ot,yc)?`calc(${r(this,ot,yc)}px * var(--scale-factor))`:""}};for(const n of t.split(`
`))e.push({name:"span",value:n,attributes:i});return s},Wu=function(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&r(this,Os))&&m(this,ot,xl).call(this)},qu=function(){if(r(this,Ns)!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:n}}}=r(this,po);let a=!!r(this,Rr),o=a?r(this,Rr):r(this,go);for(const b of r(this,fo))if(!o||F.intersect(b.data.rect,o)!==null){o=b.data.rect,a=!0;break}const l=F.normalizeRect([o[0],t[3]-o[1]+t[1],o[2],t[3]-o[3]+t[1]]),d=a?o[2]-o[0]+5:0,u=l[0]+d,f=l[1];g(this,Ns,[100*(u-i)/e,100*(f-n)/s]);const{style:p}=r(this,At);p.left=`${r(this,Ns)[0]}%`,p.top=`${r(this,Ns)[1]}%`},xl=function(){g(this,Os,!r(this,Os)),r(this,Os)?(m(this,ot,Cl).call(this),r(this,At).addEventListener("click",r(this,Tr)),r(this,At).addEventListener("keydown",r(this,Cr))):(m(this,ot,_c).call(this),r(this,At).removeEventListener("click",r(this,Tr)),r(this,At).removeEventListener("keydown",r(this,Cr)))},Cl=function(){r(this,$e)||this.render(),this.isVisible?r(this,Os)&&r(this,At).classList.add("focused"):(m(this,ot,qu).call(this),r(this,At).hidden=!1,r(this,At).style.zIndex=parseInt(r(this,At).style.zIndex)+1e3)},_c=function(){r(this,At).classList.remove("focused"),!(r(this,Os)||!this.isVisible)&&(r(this,At).hidden=!0,r(this,At).style.zIndex=parseInt(r(this,At).style.zIndex)-1e3)};class Xu extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=Q.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}get _isEditable(){return this.data.hasOwnCanvas}}var bo;class em extends Et{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});A(this,bo,null)}render(){this.container.classList.add("lineAnnotation");const e=this.data,{width:s,height:i}=qs(e.rect),n=this.svgFactory.create(s,i,!0),a=g(this,bo,this.svgFactory.createElement("svg:line"));return a.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),a.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),a.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),a.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),a.setAttribute("stroke-width",e.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),n.append(a),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,bo)}addHighlightArea(){this.container.classList.add("highlightArea")}}bo=new WeakMap;var Ao;class sm extends Et{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});A(this,Ao,null)}render(){this.container.classList.add("squareAnnotation");const e=this.data,{width:s,height:i}=qs(e.rect),n=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=g(this,Ao,this.svgFactory.createElement("svg:rect"));return o.setAttribute("x",a/2),o.setAttribute("y",a/2),o.setAttribute("width",s-a),o.setAttribute("height",i-a),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),n.append(o),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,Ao)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ao=new WeakMap;var vo;class im extends Et{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});A(this,vo,null)}render(){this.container.classList.add("circleAnnotation");const e=this.data,{width:s,height:i}=qs(e.rect),n=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=g(this,vo,this.svgFactory.createElement("svg:ellipse"));return o.setAttribute("cx",s/2),o.setAttribute("cy",i/2),o.setAttribute("rx",s/2-a/2),o.setAttribute("ry",i/2-a/2),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),n.append(o),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,vo)}addHighlightArea(){this.container.classList.add("highlightArea")}}vo=new WeakMap;var yo;class Yu extends Et{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});A(this,yo,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const e=this.data,{width:s,height:i}=qs(e.rect),n=this.svgFactory.create(s,i,!0);let a=[];for(const l of e.vertices){const c=l.x-e.rect[0],d=e.rect[3]-l.y;a.push(c+","+d)}a=a.join(" ");const o=g(this,yo,this.svgFactory.createElement(this.svgElementName));return o.setAttribute("points",a),o.setAttribute("stroke-width",e.borderStyle.width||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),n.append(o),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,yo)}addHighlightArea(){this.container.classList.add("highlightArea")}}yo=new WeakMap;class nm extends Yu{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class rm extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}var _o;class Ku extends Et{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});A(this,_o,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=Q.INK}render(){this.container.classList.add(this.containerClassName);const e=this.data,{width:s,height:i}=qs(e.rect),n=this.svgFactory.create(s,i,!0);for(const a of e.inkLists){let o=[];for(const c of a){const d=c.x-e.rect[0],u=e.rect[3]-c.y;o.push(`${d},${u}`)}o=o.join(" ");const l=this.svgFactory.createElement(this.svgElementName);r(this,_o).push(l),l.setAttribute("points",o),l.setAttribute("stroke-width",e.borderStyle.width||1),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),!e.popupRef&&this.hasPopupData&&this._createPopup(),n.append(l)}return this.container.append(n),this.container}getElementsToTriggerPopup(){return r(this,_o)}addHighlightArea(){this.container.classList.add("highlightArea")}}_o=new WeakMap;class am extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class om extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class lm extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class hm extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Qu extends Et{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}var Eo,wo,Ec;class cm extends Et{constructor(e){var i;super(e,{isRenderable:!0});A(this,wo);A(this,Eo,null);const{file:s}=this.data;this.filename=s.filename,this.content=s.content,(i=this.linkService.eventBus)==null||i.dispatch("fileattachmentannotation",{source:this,...s})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:e,data:s}=this;let i;s.hasAppearance||s.fillAlpha===0?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(s.name)?"paperclip":"pushpin"}.svg`,s.fillAlpha&&s.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(s.fillAlpha*100)}%);`)),i.addEventListener("dblclick",m(this,wo,Ec).bind(this)),g(this,Eo,i);const{isMac:n}=he.platform;return e.addEventListener("keydown",a=>{a.key==="Enter"&&(n?a.metaKey:a.ctrlKey)&&m(this,wo,Ec).call(this)}),!s.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),e.append(i),e}getElementsToTriggerPopup(){return r(this,Eo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Eo=new WeakMap,wo=new WeakSet,Ec=function(){var e;(e=this.downloadManager)==null||e.openOrDownloadData(this.content,this.filename)};var So,fn,Pr,On,Ju,wc,zd;let dm=(zd=class{constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:n,viewport:a}){A(this,On);A(this,So,null);A(this,fn,null);A(this,Pr,new Map);this.div=t,g(this,So,e),g(this,fn,s),this.page=n,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=i}async render(t){var a;const{annotations:e}=t,s=this.div;Mn(s,this.viewport);const i=new Map,n={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new sd,annotationStorage:t.annotationStorage||new od,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const o of e){if(o.noHTML)continue;const l=o.annotationType===xt.POPUP;if(l){const u=i.get(o.id);if(!u)continue;n.elements=u}else{const{width:u,height:f}=qs(o.rect);if(u<=0||f<=0)continue}n.data=o;const c=qg.create(n);if(!c.isRenderable)continue;if(!l&&o.popupRef){const u=i.get(o.popupRef);u?u.push(c):i.set(o.popupRef,[c])}const d=c.render();o.hidden&&(d.style.visibility="hidden"),m(this,On,Ju).call(this,d,o.id),c.annotationEditorType>0&&(r(this,Pr).set(c.data.id,c),(a=this._annotationEditorUIManager)==null||a.renderAnnotationElement(c))}m(this,On,wc).call(this)}update({viewport:t}){const e=this.div;this.viewport=t,Mn(e,{rotation:t.rotation}),m(this,On,wc).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(r(this,Pr).values())}getEditableAnnotation(t){return r(this,Pr).get(t)}},So=new WeakMap,fn=new WeakMap,Pr=new WeakMap,On=new WeakSet,Ju=function(t,e){var i;const s=t.firstChild||t;s.id=`${qd}${e}`,this.div.append(t),(i=r(this,So))==null||i.moveElementInDOM(this.div,t,s,!1)},wc=function(){if(!r(this,fn))return;const t=this.div;for(const[e,s]of r(this,fn)){const i=t.querySelector(`[data-annotation-id="${e}"]`);if(!i)continue;s.className="annotationContent";const{firstChild:n}=i;n?n.nodeName==="CANVAS"?n.replaceWith(s):n.classList.contains("annotationContent")?n.after(s):n.before(s):i.append(s)}r(this,fn).clear()},zd);const fl=/\r\n?|\n/g;var xo,Co,To,Ro,Po,ze,Ae,Lo,ve,Lr,St,Zu,tf,ef,Tl,Ys,Rl,Pl,sf,xc,nf;const ut=class ut extends nt{constructor(e){super({...e,name:"freeTextEditor"});A(this,St);A(this,xo,this.editorDivBlur.bind(this));A(this,Co,this.editorDivFocus.bind(this));A(this,To,this.editorDivInput.bind(this));A(this,Ro,this.editorDivKeydown.bind(this));A(this,Po,this.editorDivPaste.bind(this));A(this,ze);A(this,Ae,"");A(this,Lo,`${this.id}-editor`);A(this,ve);A(this,Lr,null);g(this,ze,e.color||ut._defaultColor||nt._defaultLineColor),g(this,ve,e.fontSize||ut._defaultFontSize)}static get _keyboardManager(){const e=ut.prototype,s=a=>a.isEmpty(),i=In.TRANSLATE_SMALL,n=In.TRANSLATE_BIG;return J(this,"_keyboardManager",new el([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-i,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-n,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[i,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[n,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-i],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-n],checker:s}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,i],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,n],checker:s}]]))}static initialize(e,s){nt.initialize(e,s,{strings:["pdfjs-free-text-default-content"]});const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(e,s){switch(e){case G.FREETEXT_SIZE:ut._defaultFontSize=s;break;case G.FREETEXT_COLOR:ut._defaultColor=s;break}}updateParams(e,s){switch(e){case G.FREETEXT_SIZE:m(this,St,Zu).call(this,s);break;case G.FREETEXT_COLOR:m(this,St,tf).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[G.FREETEXT_SIZE,ut._defaultFontSize],[G.FREETEXT_COLOR,ut._defaultColor||nt._defaultLineColor]]}get propertiesToUpdate(){return[[G.FREETEXT_SIZE,r(this,ve)],[G.FREETEXT_COLOR,r(this,ze)]]}_translateEmpty(e,s){this._uiManager.translateSelectedEditors(e,s,!0)}getInitialTranslation(){const e=this.parentScale;return[-ut._internalPadding*e,-(ut._internalPadding+r(this,ve))*e]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(Q.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",r(this,Ro)),this.editorDiv.addEventListener("focus",r(this,Co)),this.editorDiv.addEventListener("blur",r(this,xo)),this.editorDiv.addEventListener("input",r(this,To)),this.editorDiv.addEventListener("paste",r(this,Po)))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",r(this,Lo)),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",r(this,Ro)),this.editorDiv.removeEventListener("focus",r(this,Co)),this.editorDiv.removeEventListener("blur",r(this,xo)),this.editorDiv.removeEventListener("input",r(this,To)),this.editorDiv.removeEventListener("paste",r(this,Po)),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(e){this._focusEventsAllowed&&(super.focusin(e),e.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){var e;this.width||(this.enableEditMode(),this.editorDiv.focus(),(e=this._initialOptions)!=null&&e.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const e=r(this,Ae),s=g(this,Ae,m(this,St,ef).call(this).trimEnd());if(e===s)return;const i=n=>{if(g(this,Ae,n),!n){this.remove();return}m(this,St,Pl).call(this),this._uiManager.rebuild(this),m(this,St,Tl).call(this)};this.addCommands({cmd:()=>{i(s)},undo:()=>{i(e)},mustExec:!1}),m(this,St,Tl).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(e){this.enterInEditMode()}keydown(e){e.target===this.div&&e.key==="Enter"&&(this.enterInEditMode(),e.preventDefault())}editorDivKeydown(e){ut._keyboardManager.exec(this,e)}editorDivFocus(e){this.isEditing=!0}editorDivBlur(e){this.isEditing=!1}editorDivInput(e){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let e,s;this.width&&(e=this.x,s=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",r(this,Lo)),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text"),this.enableEditing(),nt._l10nPromise.get("pdfjs-free-text-default-content").then(n=>{var a;return(a=this.editorDiv)==null?void 0:a.setAttribute("default-content",n)}),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${r(this,ve)}px * var(--scale-factor))`,i.color=r(this,ze),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),Bl(this,this.div,["dblclick","keydown"]),this.width){const[n,a]=this.parentDimensions;if(this.annotationElementId){const{position:o}=r(this,Lr);let[l,c]=this.getInitialTranslation();[l,c]=this.pageTranslationToScreen(l,c);const[d,u]=this.pageDimensions,[f,p]=this.pageTranslation;let b,v;switch(this.rotation){case 0:b=e+(o[0]-f)/d,v=s+this.height-(o[1]-p)/u;break;case 90:b=e+(o[0]-f)/d,v=s-(o[1]-p)/u,[l,c]=[c,-l];break;case 180:b=e-this.width+(o[0]-f)/d,v=s-(o[1]-p)/u,[l,c]=[-l,-c];break;case 270:b=e+(o[0]-f-this.height*u)/d,v=s+(o[1]-p-this.width*d)/u,[l,c]=[-c,l];break}this.setAt(b*n,v*a,l,c)}else this.setAt(e*n,s*a,this.width*n,this.height*a);m(this,St,Pl).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}editorDivPaste(e){var b,v,_;const s=e.clipboardData||window.clipboardData,{types:i}=s;if(i.length===1&&i[0]==="text/plain")return;e.preventDefault();const n=m(b=ut,Ys,xc).call(b,s.getData("text")||"").replaceAll(fl,`
`);if(!n)return;const a=window.getSelection();if(!a.rangeCount)return;this.editorDiv.normalize(),a.deleteFromDocument();const o=a.getRangeAt(0);if(!n.includes(`
`)){o.insertNode(document.createTextNode(n)),this.editorDiv.normalize(),a.collapseToStart();return}const{startContainer:l,startOffset:c}=o,d=[],u=[];if(l.nodeType===Node.TEXT_NODE){const y=l.parentElement;if(u.push(l.nodeValue.slice(c).replaceAll(fl,"")),y!==this.editorDiv){let E=d;for(const w of this.editorDiv.childNodes){if(w===y){E=u;continue}E.push(m(v=ut,Ys,Rl).call(v,w))}}d.push(l.nodeValue.slice(0,c).replaceAll(fl,""))}else if(l===this.editorDiv){let y=d,E=0;for(const w of this.editorDiv.childNodes)E++===c&&(y=u),y.push(m(_=ut,Ys,Rl).call(_,w))}g(this,Ae,`${d.join(`
`)}${n}${u.join(`
`)}`),m(this,St,Pl).call(this);const f=new Range;let p=d.reduce((y,E)=>y+E.length,0);for(const{firstChild:y}of this.editorDiv.childNodes)if(y.nodeType===Node.TEXT_NODE){const E=y.nodeValue.length;if(p<=E){f.setStart(y,p),f.setEnd(y,p);break}p-=E}a.removeAllRanges(),a.addRange(f)}get contentDiv(){return this.editorDiv}static deserialize(e,s,i){var o;let n=null;if(e instanceof Xu){const{data:{defaultAppearanceData:{fontSize:l,fontColor:c},rect:d,rotation:u,id:f},textContent:p,textPosition:b,parent:{page:{pageNumber:v}}}=e;if(!p||p.length===0)return null;n=e={annotationType:Q.FREETEXT,color:Array.from(c),fontSize:l,value:p.join(`
`),position:b,pageIndex:v-1,rect:d.slice(0),rotation:u,id:f,deleted:!1}}const a=super.deserialize(e,s,i);return g(a,ve,e.fontSize),g(a,ze,F.makeHexColor(...e.color)),g(a,Ae,m(o=ut,Ys,xc).call(o,e.value)),a.annotationElementId=e.id||null,g(a,Lr,n),a}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const s=ut._internalPadding*this.parentScale,i=this.getRect(s,s),n=nt._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:r(this,ze)),a={annotationType:Q.FREETEXT,color:n,fontSize:r(this,ve),value:m(this,St,sf).call(this),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?a:this.annotationElementId&&!m(this,St,nf).call(this,a)?null:(a.id=this.annotationElementId,a)}renderAnnotationElement(e){const s=super.renderAnnotationElement(e);if(this.deleted)return s;const{style:i}=s;i.fontSize=`calc(${r(this,ve)}px * var(--scale-factor))`,i.color=r(this,ze),s.replaceChildren();for(const a of r(this,Ae).split(`
`)){const o=document.createElement("div");o.append(a?document.createTextNode(a):document.createElement("br")),s.append(o)}const n=ut._internalPadding*this.parentScale;return e.updateEdited({rect:this.getRect(n,n),popupContent:r(this,Ae)}),s}resetAnnotationElement(e){super.resetAnnotationElement(e),e.resetEdited()}};xo=new WeakMap,Co=new WeakMap,To=new WeakMap,Ro=new WeakMap,Po=new WeakMap,ze=new WeakMap,Ae=new WeakMap,Lo=new WeakMap,ve=new WeakMap,Lr=new WeakMap,St=new WeakSet,Zu=function(e){const s=n=>{this.editorDiv.style.fontSize=`calc(${n}px * var(--scale-factor))`,this.translate(0,-(n-r(this,ve))*this.parentScale),g(this,ve,n),m(this,St,Tl).call(this)},i=r(this,ve);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},tf=function(e){const s=n=>{g(this,ze,this.editorDiv.style.color=n)},i=r(this,ze);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},ef=function(){var s;const e=[];this.editorDiv.normalize();for(const i of this.editorDiv.childNodes)e.push(m(s=ut,Ys,Rl).call(s,i));return e.join(`
`)},Tl=function(){const[e,s]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:n,div:a}=this,o=a.style.display,l=a.classList.contains("hidden");a.classList.remove("hidden"),a.style.display="hidden",n.div.append(this.div),i=a.getBoundingClientRect(),a.remove(),a.style.display=o,a.classList.toggle("hidden",l)}this.rotation%180===this.parentRotation%180?(this.width=i.width/e,this.height=i.height/s):(this.width=i.height/e,this.height=i.width/s),this.fixAndSetPosition()},Ys=new WeakSet,Rl=function(e){return(e.nodeType===Node.TEXT_NODE?e.nodeValue:e.innerText).replaceAll(fl,"")},Pl=function(){if(this.editorDiv.replaceChildren(),!!r(this,Ae))for(const e of r(this,Ae).split(`
`)){const s=document.createElement("div");s.append(e?document.createTextNode(e):document.createElement("br")),this.editorDiv.append(s)}},sf=function(){return r(this,Ae).replaceAll(" "," ")},xc=function(e){return e.replaceAll(" "," ")},nf=function(e){const{value:s,fontSize:i,color:n,pageIndex:a}=r(this,Lr);return this._hasBeenMoved||e.value!==s||e.fontSize!==i||e.color.some((o,l)=>o!==n[l])||e.pageIndex!==a},A(ut,Ys),V(ut,"_freeTextDefaultContent",""),V(ut,"_internalPadding",0),V(ut,"_defaultColor",null),V(ut,"_defaultFontSize",10),V(ut,"_type","freetext"),V(ut,"_editorType",Q.FREETEXT);let Sc=ut;var ko,pi,Ge,se,rf,Ll,af,of,Tc;class Cc{constructor(t,e=0,s=0,i=!0){A(this,se);A(this,ko);A(this,pi,[]);A(this,Ge,[]);let n=1/0,a=-1/0,o=1/0,l=-1/0;const d=10**-4;for(const{x:y,y:E,width:w,height:x}of t){const S=Math.floor((y-e)/d)*d,C=Math.ceil((y+w+e)/d)*d,R=Math.floor((E-e)/d)*d,k=Math.ceil((E+x+e)/d)*d,D=[S,R,k,!0],H=[C,R,k,!1];r(this,pi).push(D,H),n=Math.min(n,S),a=Math.max(a,C),o=Math.min(o,R),l=Math.max(l,k)}const u=a-n+2*s,f=l-o+2*s,p=n-s,b=o-s,v=r(this,pi).at(i?-1:-2),_=[v[0],v[2]];for(const y of r(this,pi)){const[E,w,x]=y;y[0]=(E-p)/u,y[1]=(w-b)/f,y[2]=(x-b)/f}g(this,ko,{x:p,y:b,width:u,height:f,lastPoint:_})}getOutlines(){r(this,pi).sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of r(this,pi))e[3]?(t.push(...m(this,se,Tc).call(this,e)),m(this,se,af).call(this,e)):(m(this,se,of).call(this,e),t.push(...m(this,se,Tc).call(this,e)));return m(this,se,rf).call(this,t)}}ko=new WeakMap,pi=new WeakMap,Ge=new WeakMap,se=new WeakSet,rf=function(t){const e=[],s=new Set;for(const a of t){const[o,l,c]=a;e.push([o,l,a],[o,c,a])}e.sort((a,o)=>a[1]-o[1]||a[0]-o[0]);for(let a=0,o=e.length;a<o;a+=2){const l=e[a][2],c=e[a+1][2];l.push(c),c.push(l),s.add(l),s.add(c)}const i=[];let n;for(;s.size>0;){const a=s.values().next().value;let[o,l,c,d,u]=a;s.delete(a);let f=o,p=l;for(n=[o,c],i.push(n);;){let b;if(s.has(d))b=d;else if(s.has(u))b=u;else break;s.delete(b),[o,l,c,d,u]=b,f!==o&&(n.push(f,p,o,p===l?l:c),f=o),p=p===l?c:l}n.push(f,p)}return new um(i,r(this,ko))},Ll=function(t){const e=r(this,Ge);let s=0,i=e.length-1;for(;s<=i;){const n=s+i>>1,a=e[n][0];if(a===t)return n;a<t?s=n+1:i=n-1}return i+1},af=function([,t,e]){const s=m(this,se,Ll).call(this,t);r(this,Ge).splice(s,0,[t,e])},of=function([,t,e]){const s=m(this,se,Ll).call(this,t);for(let i=s;i<r(this,Ge).length;i++){const[n,a]=r(this,Ge)[i];if(n!==t)break;if(n===t&&a===e){r(this,Ge).splice(i,1);return}}for(let i=s-1;i>=0;i--){const[n,a]=r(this,Ge)[i];if(n!==t)break;if(n===t&&a===e){r(this,Ge).splice(i,1);return}}},Tc=function(t){const[e,s,i]=t,n=[[e,s,i]],a=m(this,se,Ll).call(this,i);for(let o=0;o<a;o++){const[l,c]=r(this,Ge)[o];for(let d=0,u=n.length;d<u;d++){const[,f,p]=n[d];if(!(c<=f||p<=l)){if(f>=l){if(p>c)n[d][1]=c;else{if(u===1)return[];n.splice(d,1),d--,u--}continue}n[d][2]=l,p>c&&n.push([e,c,p])}}}return n};class lf{toSVGPath(){throw new Error("Abstract method `toSVGPath` must be implemented.")}get box(){throw new Error("Abstract getter `box` must be implemented.")}serialize(t,e){throw new Error("Abstract method `serialize` must be implemented.")}get free(){return this instanceof Pc}}var Mo,kr;class um extends lf{constructor(e,s){super();A(this,Mo);A(this,kr);g(this,kr,e),g(this,Mo,s)}toSVGPath(){const e=[];for(const s of r(this,kr)){let[i,n]=s;e.push(`M${i} ${n}`);for(let a=2;a<s.length;a+=2){const o=s[a],l=s[a+1];o===i?(e.push(`V${l}`),n=l):l===n&&(e.push(`H${o}`),i=o)}e.push("Z")}return e.join(" ")}serialize([e,s,i,n],a){const o=[],l=i-e,c=n-s;for(const d of r(this,kr)){const u=new Array(d.length);for(let f=0;f<d.length;f+=2)u[f]=e+d[f]*l,u[f+1]=n-d[f+1]*c;o.push(u)}return o}get box(){return r(this,Mo)}}Mo=new WeakMap,kr=new WeakMap;var rs,Bs,Mr,Ir,as,K,pn,gn,Io,Do,Dr,Fr,gi,Fo,lh,hh,Oo,Rc;const vs=class vs{constructor({x:t,y:e},s,i,n,a,o=0){A(this,Oo);A(this,rs);A(this,Bs,[]);A(this,Mr);A(this,Ir);A(this,as,[]);A(this,K,new Float64Array(18));A(this,pn);A(this,gn);A(this,Io);A(this,Do);A(this,Dr);A(this,Fr);A(this,gi,[]);g(this,rs,s),g(this,Fr,n*i),g(this,Ir,a),r(this,K).set([NaN,NaN,NaN,NaN,t,e],6),g(this,Mr,o),g(this,Do,r(vs,Fo)*i),g(this,Io,r(vs,hh)*i),g(this,Dr,i),r(this,gi).push(t,e)}get free(){return!0}isEmpty(){return isNaN(r(this,K)[8])}add({x:t,y:e}){var D;g(this,pn,t),g(this,gn,e);const[s,i,n,a]=r(this,rs);let[o,l,c,d]=r(this,K).subarray(8,12);const u=t-c,f=e-d,p=Math.hypot(u,f);if(p<r(this,Io))return!1;const b=p-r(this,Do),v=b/p,_=v*u,y=v*f;let E=o,w=l;o=c,l=d,c+=_,d+=y,(D=r(this,gi))==null||D.push(t,e);const x=-y/b,S=_/b,C=x*r(this,Fr),R=S*r(this,Fr);return r(this,K).set(r(this,K).subarray(2,8),0),r(this,K).set([c+C,d+R],4),r(this,K).set(r(this,K).subarray(14,18),12),r(this,K).set([c-C,d-R],16),isNaN(r(this,K)[6])?(r(this,as).length===0&&(r(this,K).set([o+C,l+R],2),r(this,as).push(NaN,NaN,NaN,NaN,(o+C-s)/n,(l+R-i)/a),r(this,K).set([o-C,l-R],14),r(this,Bs).push(NaN,NaN,NaN,NaN,(o-C-s)/n,(l-R-i)/a)),r(this,K).set([E,w,o,l,c,d],6),!this.isEmpty()):(r(this,K).set([E,w,o,l,c,d],6),Math.abs(Math.atan2(w-l,E-o)-Math.atan2(y,_))<Math.PI/2?([o,l,c,d]=r(this,K).subarray(2,6),r(this,as).push(NaN,NaN,NaN,NaN,((o+c)/2-s)/n,((l+d)/2-i)/a),[o,l,E,w]=r(this,K).subarray(14,18),r(this,Bs).push(NaN,NaN,NaN,NaN,((E+o)/2-s)/n,((w+l)/2-i)/a),!0):([E,w,o,l,c,d]=r(this,K).subarray(0,6),r(this,as).push(((E+5*o)/6-s)/n,((w+5*l)/6-i)/a,((5*o+c)/6-s)/n,((5*l+d)/6-i)/a,((o+c)/2-s)/n,((l+d)/2-i)/a),[c,d,o,l,E,w]=r(this,K).subarray(12,18),r(this,Bs).push(((E+5*o)/6-s)/n,((w+5*l)/6-i)/a,((5*o+c)/6-s)/n,((5*l+d)/6-i)/a,((o+c)/2-s)/n,((l+d)/2-i)/a),!0))}toSVGPath(){if(this.isEmpty())return"";const t=r(this,as),e=r(this,Bs),s=r(this,K).subarray(4,6),i=r(this,K).subarray(16,18),[n,a,o,l]=r(this,rs),[c,d,u,f]=m(this,Oo,Rc).call(this);if(isNaN(r(this,K)[6])&&!this.isEmpty())return`M${(r(this,K)[2]-n)/o} ${(r(this,K)[3]-a)/l} L${(r(this,K)[4]-n)/o} ${(r(this,K)[5]-a)/l} L${c} ${d} L${u} ${f} L${(r(this,K)[16]-n)/o} ${(r(this,K)[17]-a)/l} L${(r(this,K)[14]-n)/o} ${(r(this,K)[15]-a)/l} Z`;const p=[];p.push(`M${t[4]} ${t[5]}`);for(let b=6;b<t.length;b+=6)isNaN(t[b])?p.push(`L${t[b+4]} ${t[b+5]}`):p.push(`C${t[b]} ${t[b+1]} ${t[b+2]} ${t[b+3]} ${t[b+4]} ${t[b+5]}`);p.push(`L${(s[0]-n)/o} ${(s[1]-a)/l} L${c} ${d} L${u} ${f} L${(i[0]-n)/o} ${(i[1]-a)/l}`);for(let b=e.length-6;b>=6;b-=6)isNaN(e[b])?p.push(`L${e[b+4]} ${e[b+5]}`):p.push(`C${e[b]} ${e[b+1]} ${e[b+2]} ${e[b+3]} ${e[b+4]} ${e[b+5]}`);return p.push(`L${e[4]} ${e[5]} Z`),p.join(" ")}getOutlines(){var y;const t=r(this,as),e=r(this,Bs),s=r(this,K),i=s.subarray(4,6),n=s.subarray(16,18),[a,o,l,c]=r(this,rs),d=new Float64Array((((y=r(this,gi))==null?void 0:y.length)??0)+2);for(let E=0,w=d.length-2;E<w;E+=2)d[E]=(r(this,gi)[E]-a)/l,d[E+1]=(r(this,gi)[E+1]-o)/c;d[d.length-2]=(r(this,pn)-a)/l,d[d.length-1]=(r(this,gn)-o)/c;const[u,f,p,b]=m(this,Oo,Rc).call(this);if(isNaN(s[6])&&!this.isEmpty()){const E=new Float64Array(36);return E.set([NaN,NaN,NaN,NaN,(s[2]-a)/l,(s[3]-o)/c,NaN,NaN,NaN,NaN,(s[4]-a)/l,(s[5]-o)/c,NaN,NaN,NaN,NaN,u,f,NaN,NaN,NaN,NaN,p,b,NaN,NaN,NaN,NaN,(s[16]-a)/l,(s[17]-o)/c,NaN,NaN,NaN,NaN,(s[14]-a)/l,(s[15]-o)/c],0),new Pc(E,d,r(this,rs),r(this,Dr),r(this,Mr),r(this,Ir))}const v=new Float64Array(r(this,as).length+24+r(this,Bs).length);let _=t.length;for(let E=0;E<_;E+=2){if(isNaN(t[E])){v[E]=v[E+1]=NaN;continue}v[E]=t[E],v[E+1]=t[E+1]}v.set([NaN,NaN,NaN,NaN,(i[0]-a)/l,(i[1]-o)/c,NaN,NaN,NaN,NaN,u,f,NaN,NaN,NaN,NaN,p,b,NaN,NaN,NaN,NaN,(n[0]-a)/l,(n[1]-o)/c],_),_+=24;for(let E=e.length-6;E>=6;E-=6)for(let w=0;w<6;w+=2){if(isNaN(e[E+w])){v[_]=v[_+1]=NaN,_+=2;continue}v[_]=e[E+w],v[_+1]=e[E+w+1],_+=2}return v.set([NaN,NaN,NaN,NaN,e[4],e[5]],_),new Pc(v,d,r(this,rs),r(this,Dr),r(this,Mr),r(this,Ir))}};rs=new WeakMap,Bs=new WeakMap,Mr=new WeakMap,Ir=new WeakMap,as=new WeakMap,K=new WeakMap,pn=new WeakMap,gn=new WeakMap,Io=new WeakMap,Do=new WeakMap,Dr=new WeakMap,Fr=new WeakMap,gi=new WeakMap,Fo=new WeakMap,lh=new WeakMap,hh=new WeakMap,Oo=new WeakSet,Rc=function(){const t=r(this,K).subarray(4,6),e=r(this,K).subarray(16,18),[s,i,n,a]=r(this,rs);return[(r(this,pn)+(t[0]-e[0])/2-s)/n,(r(this,gn)+(t[1]-e[1])/2-i)/a,(r(this,pn)+(e[0]-t[0])/2-s)/n,(r(this,gn)+(e[1]-t[1])/2-i)/a]},A(vs,Fo,8),A(vs,lh,2),A(vs,hh,r(vs,Fo)+r(vs,lh));let zl=vs;var Or,mn,Us,No,ye,Ho,yt,ce,ha,ca,hf;class Pc extends lf{constructor(e,s,i,n,a,o){super();A(this,ce);A(this,Or);A(this,mn,null);A(this,Us);A(this,No);A(this,ye);A(this,Ho);A(this,yt);g(this,yt,e),g(this,ye,s),g(this,Or,i),g(this,Ho,n),g(this,Us,a),g(this,No,o),m(this,ce,hf).call(this,o);const{x:l,y:c,width:d,height:u}=r(this,mn);for(let f=0,p=e.length;f<p;f+=2)e[f]=(e[f]-l)/d,e[f+1]=(e[f+1]-c)/u;for(let f=0,p=s.length;f<p;f+=2)s[f]=(s[f]-l)/d,s[f+1]=(s[f+1]-c)/u}toSVGPath(){const e=[`M${r(this,yt)[4]} ${r(this,yt)[5]}`];for(let s=6,i=r(this,yt).length;s<i;s+=6){if(isNaN(r(this,yt)[s])){e.push(`L${r(this,yt)[s+4]} ${r(this,yt)[s+5]}`);continue}e.push(`C${r(this,yt)[s]} ${r(this,yt)[s+1]} ${r(this,yt)[s+2]} ${r(this,yt)[s+3]} ${r(this,yt)[s+4]} ${r(this,yt)[s+5]}`)}return e.push("Z"),e.join(" ")}serialize([e,s,i,n],a){const o=i-e,l=n-s;let c,d;switch(a){case 0:c=m(this,ce,ha).call(this,r(this,yt),e,n,o,-l),d=m(this,ce,ha).call(this,r(this,ye),e,n,o,-l);break;case 90:c=m(this,ce,ca).call(this,r(this,yt),e,s,o,l),d=m(this,ce,ca).call(this,r(this,ye),e,s,o,l);break;case 180:c=m(this,ce,ha).call(this,r(this,yt),i,s,-o,l),d=m(this,ce,ha).call(this,r(this,ye),i,s,-o,l);break;case 270:c=m(this,ce,ca).call(this,r(this,yt),i,n,-o,-l),d=m(this,ce,ca).call(this,r(this,ye),i,n,-o,-l);break}return{outline:Array.from(c),points:[Array.from(d)]}}get box(){return r(this,mn)}getNewOutline(e,s){const{x:i,y:n,width:a,height:o}=r(this,mn),[l,c,d,u]=r(this,Or),f=a*d,p=o*u,b=i*d+l,v=n*u+c,_=new zl({x:r(this,ye)[0]*f+b,y:r(this,ye)[1]*p+v},r(this,Or),r(this,Ho),e,r(this,No),s??r(this,Us));for(let y=2;y<r(this,ye).length;y+=2)_.add({x:r(this,ye)[y]*f+b,y:r(this,ye)[y+1]*p+v});return _.getOutlines()}}Or=new WeakMap,mn=new WeakMap,Us=new WeakMap,No=new WeakMap,ye=new WeakMap,Ho=new WeakMap,yt=new WeakMap,ce=new WeakSet,ha=function(e,s,i,n,a){const o=new Float64Array(e.length);for(let l=0,c=e.length;l<c;l+=2)o[l]=s+e[l]*n,o[l+1]=i+e[l+1]*a;return o},ca=function(e,s,i,n,a){const o=new Float64Array(e.length);for(let l=0,c=e.length;l<c;l+=2)o[l]=s+e[l+1]*n,o[l+1]=i+e[l]*a;return o},hf=function(e){const s=r(this,yt);let i=s[4],n=s[5],a=i,o=n,l=i,c=n,d=i,u=n;const f=e?Math.max:Math.min;for(let y=6,E=s.length;y<E;y+=6){if(isNaN(s[y]))a=Math.min(a,s[y+4]),o=Math.min(o,s[y+5]),l=Math.max(l,s[y+4]),c=Math.max(c,s[y+5]),u<s[y+5]?(d=s[y+4],u=s[y+5]):u===s[y+5]&&(d=f(d,s[y+4]));else{const w=F.bezierBoundingBox(i,n,...s.slice(y,y+6));a=Math.min(a,w[0]),o=Math.min(o,w[1]),l=Math.max(l,w[2]),c=Math.max(c,w[3]),u<w[3]?(d=w[2],u=w[3]):u===w[3]&&(d=f(d,w[2]))}i=s[y+4],n=s[y+5]}const p=a-r(this,Us),b=o-r(this,Us),v=l-a+2*r(this,Us),_=c-o+2*r(this,Us);g(this,mn,{x:p,y:b,width:v,height:_,lastPoint:[d,u]})};var Bo,Uo,Ve,bn,Nr,Tt,jo,Hr,$o,zo,mi,Br,ct,Lc,kc,cf,Mi,df,si;const ys=class ys{constructor({editor:t=null,uiManager:e=null}){A(this,ct);A(this,Bo,m(this,ct,cf).bind(this));A(this,Uo,m(this,ct,df).bind(this));A(this,Ve,null);A(this,bn,null);A(this,Nr);A(this,Tt,null);A(this,jo,!1);A(this,Hr,!1);A(this,$o,null);A(this,zo);A(this,mi,null);A(this,Br);var s;t?(g(this,Hr,!1),g(this,Br,G.HIGHLIGHT_COLOR),g(this,$o,t)):(g(this,Hr,!0),g(this,Br,G.HIGHLIGHT_DEFAULT_COLOR)),g(this,mi,(t==null?void 0:t._uiManager)||e),g(this,zo,r(this,mi)._eventBus),g(this,Nr,(t==null?void 0:t.color)||((s=r(this,mi))==null?void 0:s.highlightColors.values().next().value)||"#FFFF98")}static get _keyboardManager(){return J(this,"_keyboardManager",new el([[["Escape","mac+Escape"],ys.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ys.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ys.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ys.prototype._moveToPrevious],[["Home","mac+Home"],ys.prototype._moveToBeginning],[["End","mac+End"],ys.prototype._moveToEnd]]))}renderButton(){const t=g(this,Ve,document.createElement("button"));t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0),t.addEventListener("click",m(this,ct,Mi).bind(this)),t.addEventListener("keydown",r(this,Bo));const e=g(this,bn,document.createElement("span"));return e.className="swatch",e.setAttribute("aria-hidden",!0),e.style.backgroundColor=r(this,Nr),t.append(e),t}renderMainDropdown(){const t=g(this,Tt,m(this,ct,Lc).call(this));return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}_colorSelectFromKeyboard(t){if(t.target===r(this,Ve)){m(this,ct,Mi).call(this,t);return}const e=t.target.getAttribute("data-color");e&&m(this,ct,kc).call(this,e,t)}_moveToNext(t){var e,s;if(!r(this,ct,si)){m(this,ct,Mi).call(this,t);return}if(t.target===r(this,Ve)){(e=r(this,Tt).firstChild)==null||e.focus();return}(s=t.target.nextSibling)==null||s.focus()}_moveToPrevious(t){var e,s;if(t.target===((e=r(this,Tt))==null?void 0:e.firstChild)||t.target===r(this,Ve)){r(this,ct,si)&&this._hideDropdownFromKeyboard();return}r(this,ct,si)||m(this,ct,Mi).call(this,t),(s=t.target.previousSibling)==null||s.focus()}_moveToBeginning(t){var e;if(!r(this,ct,si)){m(this,ct,Mi).call(this,t);return}(e=r(this,Tt).firstChild)==null||e.focus()}_moveToEnd(t){var e;if(!r(this,ct,si)){m(this,ct,Mi).call(this,t);return}(e=r(this,Tt).lastChild)==null||e.focus()}hideDropdown(){var t;(t=r(this,Tt))==null||t.classList.add("hidden"),window.removeEventListener("pointerdown",r(this,Uo))}_hideDropdownFromKeyboard(){var t;if(!r(this,Hr)){if(!r(this,ct,si)){(t=r(this,$o))==null||t.unselect();return}this.hideDropdown(),r(this,Ve).focus({preventScroll:!0,focusVisible:r(this,jo)})}}updateColor(t){if(r(this,bn)&&(r(this,bn).style.backgroundColor=t),!r(this,Tt))return;const e=r(this,mi).highlightColors.values();for(const s of r(this,Tt).children)s.setAttribute("aria-selected",e.next().value===t)}destroy(){var t,e;(t=r(this,Ve))==null||t.remove(),g(this,Ve,null),g(this,bn,null),(e=r(this,Tt))==null||e.remove(),g(this,Tt,null)}};Bo=new WeakMap,Uo=new WeakMap,Ve=new WeakMap,bn=new WeakMap,Nr=new WeakMap,Tt=new WeakMap,jo=new WeakMap,Hr=new WeakMap,$o=new WeakMap,zo=new WeakMap,mi=new WeakMap,Br=new WeakMap,ct=new WeakSet,Lc=function(){const t=document.createElement("div");t.addEventListener("contextmenu",de),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[e,s]of r(this,mi).highlightColors){const i=document.createElement("button");i.tabIndex="0",i.role="option",i.setAttribute("data-color",s),i.title=e,i.setAttribute("data-l10n-id",`pdfjs-editor-colorpicker-${e}`);const n=document.createElement("span");i.append(n),n.className="swatch",n.style.backgroundColor=s,i.setAttribute("aria-selected",s===r(this,Nr)),i.addEventListener("click",m(this,ct,kc).bind(this,s)),t.append(i)}return t.addEventListener("keydown",r(this,Bo)),t},kc=function(t,e){e.stopPropagation(),r(this,zo).dispatch("switchannotationeditorparams",{source:this,type:r(this,Br),value:t})},cf=function(t){ys._keyboardManager.exec(this,t)},Mi=function(t){if(r(this,ct,si)){this.hideDropdown();return}if(g(this,jo,t.detail===0),window.addEventListener("pointerdown",r(this,Uo)),r(this,Tt)){r(this,Tt).classList.remove("hidden");return}const e=g(this,Tt,m(this,ct,Lc).call(this));r(this,Ve).append(e)},df=function(t){var e;(e=r(this,Tt))!=null&&e.contains(t.target)||this.hideDropdown()},si=function(){return r(this,Tt)&&!r(this,Tt).classList.contains("hidden")};let Gl=ys;var Ur,Go,bi,An,jr,Re,Vo,Wo,vn,We,Jt,_e,ch,$r,yn,Rt,zr,os,qo,Y,Mc,Ic,uf,ff,pf,Dc,da,Me,Xn,gf,kl,ua,mf,bf,Af,vf;const st=class st extends nt{constructor(e){super({...e,name:"highlightEditor"});A(this,Y);A(this,Ur,null);A(this,Go,0);A(this,bi);A(this,An,null);A(this,jr,null);A(this,Re,null);A(this,Vo,null);A(this,Wo,0);A(this,vn,null);A(this,We,null);A(this,Jt,null);A(this,_e,!1);A(this,ch,m(this,Y,gf).bind(this));A(this,$r,null);A(this,yn);A(this,Rt,null);A(this,zr,"");A(this,os);A(this,qo,"");this.color=e.color||st._defaultColor,g(this,os,e.thickness||st._defaultThickness),g(this,yn,e.opacity||st._defaultOpacity),g(this,bi,e.boxes||null),g(this,qo,e.methodOfCreation||""),g(this,zr,e.text||""),this._isDraggable=!1,e.highlightId>-1?(g(this,_e,!0),m(this,Y,Ic).call(this,e),m(this,Y,da).call(this)):(g(this,Ur,e.anchorNode),g(this,Go,e.anchorOffset),g(this,Vo,e.focusNode),g(this,Wo,e.focusOffset),m(this,Y,Mc).call(this),m(this,Y,da).call(this),this.rotate(this.rotation))}static get _keyboardManager(){const e=st.prototype;return J(this,"_keyboardManager",new el([[["ArrowLeft","mac+ArrowLeft"],e._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],e._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],e._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],e._moveCaret,{args:[3]}]]))}get telemetryInitialData(){return{action:"added",type:r(this,_e)?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:r(this,os),methodOfCreation:r(this,qo)}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(e){return{numberOfColors:e.get("color").size}}static initialize(e,s){var i;nt.initialize(e,s),st._defaultColor||(st._defaultColor=((i=s.highlightColors)==null?void 0:i.values().next().value)||"#fff066")}static updateDefaultParams(e,s){switch(e){case G.HIGHLIGHT_DEFAULT_COLOR:st._defaultColor=s;break;case G.HIGHLIGHT_THICKNESS:st._defaultThickness=s;break}}translateInPage(e,s){}get toolbarPosition(){return r(this,$r)}updateParams(e,s){switch(e){case G.HIGHLIGHT_COLOR:m(this,Y,uf).call(this,s);break;case G.HIGHLIGHT_THICKNESS:m(this,Y,ff).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[G.HIGHLIGHT_DEFAULT_COLOR,st._defaultColor],[G.HIGHLIGHT_THICKNESS,st._defaultThickness]]}get propertiesToUpdate(){return[[G.HIGHLIGHT_COLOR,this.color||st._defaultColor],[G.HIGHLIGHT_THICKNESS,r(this,os)||st._defaultThickness],[G.HIGHLIGHT_FREE,r(this,_e)]]}async addEditToolbar(){const e=await super.addEditToolbar();return e?(this._uiManager.highlightColors&&(g(this,jr,new Gl({editor:this})),e.addColorPicker(r(this,jr))),e):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(m(this,Y,ua).call(this))}getBaseTranslation(){return[0,0]}getRect(e,s){return super.getRect(e,s,m(this,Y,ua).call(this))}onceAdded(){this.parent.addUndoableEditor(this),this.div.focus()}remove(){m(this,Y,Dc).call(this),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(m(this,Y,da).call(this),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){var i;let s=!1;this.parent&&!e?m(this,Y,Dc).call(this):e&&(m(this,Y,da).call(this,e),s=!this.parent&&((i=this.div)==null?void 0:i.classList.contains("selectedEditor"))),super.setParent(e),this.show(this._isVisible),s&&this.select()}rotate(e){var n,a,o;const{drawLayer:s}=this.parent;let i;r(this,_e)?(e=(e-this.rotation+360)%360,i=m(n=st,Me,Xn).call(n,r(this,We).box,e)):i=m(a=st,Me,Xn).call(a,this,e),s.rotate(r(this,Jt),e),s.rotate(r(this,Rt),e),s.updateBox(r(this,Jt),i),s.updateBox(r(this,Rt),m(o=st,Me,Xn).call(o,r(this,Re).box,e))}render(){if(this.div)return this.div;const e=super.render();r(this,zr)&&(e.setAttribute("aria-label",r(this,zr)),e.setAttribute("role","mark")),r(this,_e)?e.classList.add("free"):this.div.addEventListener("keydown",r(this,ch));const s=g(this,vn,document.createElement("div"));e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal",s.style.clipPath=r(this,An);const[i,n]=this.parentDimensions;return this.setDims(this.width*i,this.height*n),Bl(this,r(this,vn),["pointerover","pointerleave"]),this.enableEditing(),e}pointerover(){this.parent.drawLayer.addClass(r(this,Rt),"hovered")}pointerleave(){this.parent.drawLayer.removeClass(r(this,Rt),"hovered")}_moveCaret(e){switch(this.parent.unselect(this),e){case 0:case 2:m(this,Y,kl).call(this,!0);break;case 1:case 3:m(this,Y,kl).call(this,!1);break}}select(){var e,s;super.select(),r(this,Rt)&&((e=this.parent)==null||e.drawLayer.removeClass(r(this,Rt),"hovered"),(s=this.parent)==null||s.drawLayer.addClass(r(this,Rt),"selected"))}unselect(){var e;super.unselect(),r(this,Rt)&&((e=this.parent)==null||e.drawLayer.removeClass(r(this,Rt),"selected"),r(this,_e)||m(this,Y,kl).call(this,!1))}get _mustFixPosition(){return!r(this,_e)}show(e=this._isVisible){super.show(e),this.parent&&(this.parent.drawLayer.show(r(this,Jt),e),this.parent.drawLayer.show(r(this,Rt),e))}static startHighlighting(e,s,{target:i,x:n,y:a}){const{x:o,y:l,width:c,height:d}=i.getBoundingClientRect(),u=v=>{m(this,Me,Af).call(this,e,v)},f={capture:!0,passive:!1},p=v=>{v.preventDefault(),v.stopPropagation()},b=v=>{i.removeEventListener("pointermove",u),window.removeEventListener("blur",b),window.removeEventListener("pointerup",b),window.removeEventListener("pointerdown",p,f),window.removeEventListener("contextmenu",de),m(this,Me,vf).call(this,e,v)};window.addEventListener("blur",b),window.addEventListener("pointerup",b),window.addEventListener("pointerdown",p,f),window.addEventListener("contextmenu",de),i.addEventListener("pointermove",u),this._freeHighlight=new zl({x:n,y:a},[o,l,c,d],e.scale,this._defaultThickness/2,s,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=e.drawLayer.highlight(this._freeHighlight,this._defaultColor,this._defaultOpacity,!0)}static deserialize(e,s,i){var v;const n=super.deserialize(e,s,i),{rect:[a,o,l,c],color:d,quadPoints:u}=e;n.color=F.makeHexColor(...d),g(n,yn,e.opacity);const[f,p]=n.pageDimensions;n.width=(l-a)/f,n.height=(c-o)/p;const b=g(n,bi,[]);for(let _=0;_<u.length;_+=8)b.push({x:(u[4]-l)/f,y:(c-(1-u[_+5]))/p,width:(u[_+2]-u[_])/f,height:(u[_+5]-u[_+1])/p});return m(v=n,Y,Mc).call(v),n}serialize(e=!1){if(this.isEmpty()||e)return null;const s=this.getRect(0,0),i=nt._colorManager.convert(this.color);return{annotationType:Q.HIGHLIGHT,color:i,opacity:r(this,yn),thickness:r(this,os),quadPoints:m(this,Y,mf).call(this),outlines:m(this,Y,bf).call(this,s),pageIndex:this.pageIndex,rect:s,rotation:m(this,Y,ua).call(this),structTreeParentId:this._structTreeParentId}}static canCreateNewEmptyEditor(){return!1}};Ur=new WeakMap,Go=new WeakMap,bi=new WeakMap,An=new WeakMap,jr=new WeakMap,Re=new WeakMap,Vo=new WeakMap,Wo=new WeakMap,vn=new WeakMap,We=new WeakMap,Jt=new WeakMap,_e=new WeakMap,ch=new WeakMap,$r=new WeakMap,yn=new WeakMap,Rt=new WeakMap,zr=new WeakMap,os=new WeakMap,qo=new WeakMap,Y=new WeakSet,Mc=function(){const e=new Cc(r(this,bi),.001);g(this,We,e.getOutlines()),{x:this.x,y:this.y,width:this.width,height:this.height}=r(this,We).box;const s=new Cc(r(this,bi),.0025,.001,this._uiManager.direction==="ltr");g(this,Re,s.getOutlines());const{lastPoint:i}=r(this,Re).box;g(this,$r,[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height])},Ic=function({highlightOutlines:e,highlightId:s,clipPathId:i}){var u,f;if(g(this,We,e),g(this,Re,e.getNewOutline(r(this,os)/2****,.0025)),s>=0)g(this,Jt,s),g(this,An,i),this.parent.drawLayer.finalizeLine(s,e),g(this,Rt,this.parent.drawLayer.highlightOutline(r(this,Re)));else if(this.parent){const p=this.parent.viewport.rotation;this.parent.drawLayer.updateLine(r(this,Jt),e),this.parent.drawLayer.updateBox(r(this,Jt),m(u=st,Me,Xn).call(u,r(this,We).box,(p-this.rotation+360)%360)),this.parent.drawLayer.updateLine(r(this,Rt),r(this,Re)),this.parent.drawLayer.updateBox(r(this,Rt),m(f=st,Me,Xn).call(f,r(this,Re).box,p))}const{x:a,y:o,width:l,height:c}=e.box;switch(this.rotation){case 0:this.x=a,this.y=o,this.width=l,this.height=c;break;case 90:{const[p,b]=this.parentDimensions;this.x=o,this.y=1-a,this.width=l*b/p,this.height=c*p/b;break}case 180:this.x=1-a,this.y=1-o,this.width=l,this.height=c;break;case 270:{const[p,b]=this.parentDimensions;this.x=1-o,this.y=a,this.width=l*b/p,this.height=c*p/b;break}}const{lastPoint:d}=r(this,Re).box;g(this,$r,[(d[0]-a)/l,(d[1]-o)/c])},uf=function(e){const s=n=>{var a,o;this.color=n,(a=this.parent)==null||a.drawLayer.changeColor(r(this,Jt),n),(o=r(this,jr))==null||o.updateColor(n)},i=this.color;this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(e)},!0)},ff=function(e){const s=r(this,os),i=n=>{g(this,os,n),m(this,Y,pf).call(this,n)};this.addCommands({cmd:i.bind(this,e),undo:i.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:e},!0)},pf=function(e){if(!r(this,_e))return;m(this,Y,Ic).call(this,{highlightOutlines:r(this,We).getNewOutline(e/2)}),this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)},Dc=function(){r(this,Jt)===null||!this.parent||(this.parent.drawLayer.remove(r(this,Jt)),g(this,Jt,null),this.parent.drawLayer.remove(r(this,Rt)),g(this,Rt,null))},da=function(e=this.parent){r(this,Jt)===null&&({id:pe(this,Jt)._,clipPathId:pe(this,An)._}=e.drawLayer.highlight(r(this,We),this.color,r(this,yn)),g(this,Rt,e.drawLayer.highlightOutline(r(this,Re))),r(this,vn)&&(r(this,vn).style.clipPath=r(this,An)))},Me=new WeakSet,Xn=function({x:e,y:s,width:i,height:n},a){switch(a){case 90:return{x:1-s-n,y:e,width:n,height:i};case 180:return{x:1-e-i,y:1-s-n,width:i,height:n};case 270:return{x:s,y:1-e-i,width:n,height:i}}return{x:e,y:s,width:i,height:n}},gf=function(e){st._keyboardManager.exec(this,e)},kl=function(e){if(!r(this,Ur))return;const s=window.getSelection();e?s.setPosition(r(this,Ur),r(this,Go)):s.setPosition(r(this,Vo),r(this,Wo))},ua=function(){return r(this,_e)?this.rotation:0},mf=function(){if(r(this,_e))return null;const[e,s]=this.pageDimensions,i=r(this,bi),n=new Array(i.length*8);let a=0;for(const{x:o,y:l,width:c,height:d}of i){const u=o*e,f=(1-l-d)*s;n[a]=n[a+4]=u,n[a+1]=n[a+3]=f,n[a+2]=n[a+6]=u+c*e,n[a+5]=n[a+7]=f+d*s,a+=8}return n},bf=function(e){return r(this,We).serialize(e,m(this,Y,ua).call(this))},Af=function(e,s){this._freeHighlight.add(s)&&e.drawLayer.updatePath(this._freeHighlightId,this._freeHighlight)},vf=function(e,s){this._freeHighlight.isEmpty()?e.drawLayer.removeFreeHighlight(this._freeHighlightId):e.createAndAddNewEditor(s,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""},A(st,Me),V(st,"_defaultColor",null),V(st,"_defaultOpacity",1),V(st,"_defaultThickness",12),V(st,"_l10nPromise"),V(st,"_type","highlight"),V(st,"_editorType",Q.HIGHLIGHT),V(st,"_freeHighlightId",-1),V(st,"_freeHighlight",null),V(st,"_freeHighlightClipId","");let Vl=st;var _n,En,Xo,Yo,Ko,wn,ls,js,Pe,Sn,xn,Cn,Tn,Rn,Ai,L,yf,_f,Ef,wf,Oc,Sf,Nc,xf,Cf,Tf,Rf,Pf,Ii,Hc,Ml,Il,Yn,Bc,Dl,Qs,Lf,Uc,kf,Mf,jc,Fl,fa;const gt=class gt extends nt{constructor(e){super({...e,name:"inkEditor"});A(this,L);A(this,_n,0);A(this,En,0);A(this,Xo,this.canvasPointermove.bind(this));A(this,Yo,this.canvasPointerleave.bind(this));A(this,Ko,this.canvasPointerup.bind(this));A(this,wn,this.canvasPointerdown.bind(this));A(this,ls,null);A(this,js,new Path2D);A(this,Pe,!1);A(this,Sn,!1);A(this,xn,!1);A(this,Cn,null);A(this,Tn,0);A(this,Rn,0);A(this,Ai,null);this.color=e.color||null,this.thickness=e.thickness||null,this.opacity=e.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(e,s){nt.initialize(e,s)}static updateDefaultParams(e,s){switch(e){case G.INK_THICKNESS:gt._defaultThickness=s;break;case G.INK_COLOR:gt._defaultColor=s;break;case G.INK_OPACITY:gt._defaultOpacity=s/100;break}}updateParams(e,s){switch(e){case G.INK_THICKNESS:m(this,L,yf).call(this,s);break;case G.INK_COLOR:m(this,L,_f).call(this,s);break;case G.INK_OPACITY:m(this,L,Ef).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[G.INK_THICKNESS,gt._defaultThickness],[G.INK_COLOR,gt._defaultColor||nt._defaultLineColor],[G.INK_OPACITY,Math.round(gt._defaultOpacity*100)]]}get propertiesToUpdate(){return[[G.INK_THICKNESS,this.thickness||gt._defaultThickness],[G.INK_COLOR,this.color||gt._defaultColor||nt._defaultLineColor],[G.INK_OPACITY,Math.round(100*(this.opacity??gt._defaultOpacity))]]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.canvas||(m(this,L,Ml).call(this),m(this,L,Il).call(this)),this.isAttachedToDOM||(this.parent.add(this),m(this,L,Yn).call(this)),m(this,L,fa).call(this)))}remove(){this.canvas!==null&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,r(this,ls)&&(clearTimeout(r(this,ls)),g(this,ls,null)),r(this,Cn).disconnect(),g(this,Cn,null),super.remove())}setParent(e){!this.parent&&e?this._uiManager.removeShouldRescale(this):this.parent&&e===null&&this._uiManager.addShouldRescale(this),super.setParent(e)}onScaleChanging(){const[e,s]=this.parentDimensions,i=this.width*e,n=this.height*s;this.setDimensions(i,n)}enableEditMode(){r(this,Pe)||this.canvas===null||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",r(this,wn)))}disableEditMode(){!this.isInEditMode()||this.canvas===null||(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",r(this,wn)))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return this.paths.length===0||this.paths.length===1&&this.paths[0].length===0}commit(){r(this,Pe)||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),g(this,Pe,!0),this.div.classList.add("disabled"),m(this,L,fa).call(this,!0),this.select(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(e){this._focusEventsAllowed&&(super.focusin(e),this.enableEditMode())}canvasPointerdown(e){e.button!==0||!this.isInEditMode()||r(this,Pe)||(this.setInForeground(),e.preventDefault(),this.div.contains(document.activeElement)||this.div.focus({preventScroll:!0}),m(this,L,Sf).call(this,e.offsetX,e.offsetY))}canvasPointermove(e){e.preventDefault(),m(this,L,Nc).call(this,e.offsetX,e.offsetY)}canvasPointerup(e){e.preventDefault(),m(this,L,Hc).call(this,e)}canvasPointerleave(e){m(this,L,Hc).call(this,e)}get isResizable(){return!this.isEmpty()&&r(this,Pe)}render(){if(this.div)return this.div;let e,s;this.width&&(e=this.x,s=this.y),super.render(),this.div.setAttribute("data-l10n-id","pdfjs-ink");const[i,n,a,o]=m(this,L,wf).call(this);if(this.setAt(i,n,0,0),this.setDims(a,o),m(this,L,Ml).call(this),this.width){const[l,c]=this.parentDimensions;this.setAspectRatio(this.width*l,this.height*c),this.setAt(e*l,s*c,this.width*l,this.height*c),g(this,xn,!0),m(this,L,Yn).call(this),this.setDims(this.width*l,this.height*c),m(this,L,Ii).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return m(this,L,Il).call(this),this.div}setDimensions(e,s){const i=Math.round(e),n=Math.round(s);if(r(this,Tn)===i&&r(this,Rn)===n)return;g(this,Tn,i),g(this,Rn,n),this.canvas.style.visibility="hidden";const[a,o]=this.parentDimensions;this.width=e/a,this.height=s/o,this.fixAndSetPosition(),r(this,Pe)&&m(this,L,Bc).call(this,e,s),m(this,L,Yn).call(this),m(this,L,Ii).call(this),this.canvas.style.visibility="visible",this.fixDims()}static deserialize(e,s,i){var _,y,E;if(e instanceof Ku)return null;const n=super.deserialize(e,s,i);n.thickness=e.thickness,n.color=F.makeHexColor(...e.color),n.opacity=e.opacity;const[a,o]=n.pageDimensions,l=n.width*a,c=n.height*o,d=n.parentScale,u=e.thickness/2;g(n,Pe,!0),g(n,Tn,Math.round(l)),g(n,Rn,Math.round(c));const{paths:f,rect:p,rotation:b}=e;for(let{bezier:w}of f){w=m(_=gt,Qs,kf).call(_,w,p,b);const x=[];n.paths.push(x);let S=d*(w[0]-u),C=d*(w[1]-u);for(let k=2,D=w.length;k<D;k+=6){const H=d*(w[k]-u),T=d*(w[k+1]-u),j=d*(w[k+2]-u),O=d*(w[k+3]-u),$=d*(w[k+4]-u),W=d*(w[k+5]-u);x.push([[S,C],[H,T],[j,O],[$,W]]),S=$,C=W}const R=m(this,Qs,Lf).call(this,x);n.bezierPath2D.push(R)}const v=m(y=n,L,jc).call(y);return g(n,En,Math.max(nt.MIN_SIZE,v[2]-v[0])),g(n,_n,Math.max(nt.MIN_SIZE,v[3]-v[1])),m(E=n,L,Bc).call(E,l,c),n}serialize(){if(this.isEmpty())return null;const e=this.getRect(0,0),s=nt._colorManager.convert(this.ctx.strokeStyle);return{annotationType:Q.INK,color:s,thickness:this.thickness,opacity:this.opacity,paths:m(this,L,Mf).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,e),pageIndex:this.pageIndex,rect:e,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}};_n=new WeakMap,En=new WeakMap,Xo=new WeakMap,Yo=new WeakMap,Ko=new WeakMap,wn=new WeakMap,ls=new WeakMap,js=new WeakMap,Pe=new WeakMap,Sn=new WeakMap,xn=new WeakMap,Cn=new WeakMap,Tn=new WeakMap,Rn=new WeakMap,Ai=new WeakMap,L=new WeakSet,yf=function(e){const s=n=>{this.thickness=n,m(this,L,fa).call(this)},i=this.thickness;this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})},_f=function(e){const s=n=>{this.color=n,m(this,L,Ii).call(this)},i=this.color;this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})},Ef=function(e){const s=n=>{this.opacity=n,m(this,L,Ii).call(this)};e/=100;const i=this.opacity;this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:G.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})},wf=function(){const{parentRotation:e,parentDimensions:[s,i]}=this;switch(e){case 90:return[0,i,i,s];case 180:return[s,i,s,i];case 270:return[s,0,i,s];default:return[0,0,s,i]}},Oc=function(){const{ctx:e,color:s,opacity:i,thickness:n,parentScale:a,scaleFactor:o}=this;e.lineWidth=n*a/o,e.lineCap="round",e.lineJoin="round",e.miterLimit=10,e.strokeStyle=`${s}${Op(i)}`},Sf=function(e,s){this.canvas.addEventListener("contextmenu",de),this.canvas.addEventListener("pointerleave",r(this,Yo)),this.canvas.addEventListener("pointermove",r(this,Xo)),this.canvas.addEventListener("pointerup",r(this,Ko)),this.canvas.removeEventListener("pointerdown",r(this,wn)),this.isEditing=!0,r(this,xn)||(g(this,xn,!0),m(this,L,Yn).call(this),this.thickness||(this.thickness=gt._defaultThickness),this.color||(this.color=gt._defaultColor||nt._defaultLineColor),this.opacity??(this.opacity=gt._defaultOpacity)),this.currentPath.push([e,s]),g(this,Sn,!1),m(this,L,Oc).call(this),g(this,Ai,()=>{m(this,L,Tf).call(this),r(this,Ai)&&window.requestAnimationFrame(r(this,Ai))}),window.requestAnimationFrame(r(this,Ai))},Nc=function(e,s){const[i,n]=this.currentPath.at(-1);if(this.currentPath.length>1&&e===i&&s===n)return;const a=this.currentPath;let o=r(this,js);if(a.push([e,s]),g(this,Sn,!0),a.length<=2){o.moveTo(...a[0]),o.lineTo(e,s);return}a.length===3&&(g(this,js,o=new Path2D),o.moveTo(...a[0])),m(this,L,Rf).call(this,o,...a.at(-3),...a.at(-2),e,s)},xf=function(){if(this.currentPath.length===0)return;const e=this.currentPath.at(-1);r(this,js).lineTo(...e)},Cf=function(e,s){g(this,Ai,null),e=Math.min(Math.max(e,0),this.canvas.width),s=Math.min(Math.max(s,0),this.canvas.height),m(this,L,Nc).call(this,e,s),m(this,L,xf).call(this);let i;if(this.currentPath.length!==1)i=m(this,L,Pf).call(this);else{const c=[e,s];i=[[c,c.slice(),c.slice(),c]]}const n=r(this,js),a=this.currentPath;this.currentPath=[],g(this,js,new Path2D);const o=()=>{this.allRawPaths.push(a),this.paths.push(i),this.bezierPath2D.push(n),this._uiManager.rebuild(this)},l=()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),this.paths.length===0?this.remove():(this.canvas||(m(this,L,Ml).call(this),m(this,L,Il).call(this)),m(this,L,fa).call(this))};this.addCommands({cmd:o,undo:l,mustExec:!0})},Tf=function(){if(!r(this,Sn))return;g(this,Sn,!1);const e=Math.ceil(this.thickness*this.parentScale),s=this.currentPath.slice(-3),i=s.map(o=>o[0]),n=s.map(o=>o[1]);Math.min(...i)-e,Math.max(...i)+e,Math.min(...n)-e,Math.max(...n)+e;const{ctx:a}=this;a.save(),a.clearRect(0,0,this.canvas.width,this.canvas.height);for(const o of this.bezierPath2D)a.stroke(o);a.stroke(r(this,js)),a.restore()},Rf=function(e,s,i,n,a,o,l){const c=(s+n)/2,d=(i+a)/2,u=(n+o)/2,f=(a+l)/2;e.bezierCurveTo(c+2*(n-c)/3,d+2*(a-d)/3,u+2*(n-u)/3,f+2*(a-f)/3,u,f)},Pf=function(){const e=this.currentPath;if(e.length<=2)return[[e[0],e[0],e.at(-1),e.at(-1)]];const s=[];let i,[n,a]=e[0];for(i=1;i<e.length-2;i++){const[p,b]=e[i],[v,_]=e[i+1],y=(p+v)/2,E=(b+_)/2,w=[n+2*(p-n)/3,a+2*(b-a)/3],x=[y+2*(p-y)/3,E+2*(b-E)/3];s.push([[n,a],w,x,[y,E]]),[n,a]=[y,E]}const[o,l]=e[i],[c,d]=e[i+1],u=[n+2*(o-n)/3,a+2*(l-a)/3],f=[c+2*(o-c)/3,d+2*(l-d)/3];return s.push([[n,a],u,f,[c,d]]),s},Ii=function(){if(this.isEmpty()){m(this,L,Dl).call(this);return}m(this,L,Oc).call(this);const{canvas:e,ctx:s}=this;s.setTransform(1,0,0,1,0,0),s.clearRect(0,0,e.width,e.height),m(this,L,Dl).call(this);for(const i of this.bezierPath2D)s.stroke(i)},Hc=function(e){this.canvas.removeEventListener("pointerleave",r(this,Yo)),this.canvas.removeEventListener("pointermove",r(this,Xo)),this.canvas.removeEventListener("pointerup",r(this,Ko)),this.canvas.addEventListener("pointerdown",r(this,wn)),r(this,ls)&&clearTimeout(r(this,ls)),g(this,ls,setTimeout(()=>{g(this,ls,null),this.canvas.removeEventListener("contextmenu",de)},10)),m(this,L,Cf).call(this,e.offsetX,e.offsetY),this.addToAnnotationStorage(),this.setInBackground()},Ml=function(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",this.canvas.setAttribute("data-l10n-id","pdfjs-ink-canvas"),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")},Il=function(){g(this,Cn,new ResizeObserver(e=>{const s=e[0].contentRect;s.width&&s.height&&this.setDimensions(s.width,s.height)})),r(this,Cn).observe(this.div)},Yn=function(){if(!r(this,xn))return;const[e,s]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*e),this.canvas.height=Math.ceil(this.height*s),m(this,L,Dl).call(this)},Bc=function(e,s){const i=m(this,L,Fl).call(this),n=(e-i)/r(this,En),a=(s-i)/r(this,_n);this.scaleFactor=Math.min(n,a)},Dl=function(){const e=m(this,L,Fl).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+e,this.translationY*this.scaleFactor+e)},Qs=new WeakSet,Lf=function(e){const s=new Path2D;for(let i=0,n=e.length;i<n;i++){const[a,o,l,c]=e[i];i===0&&s.moveTo(...a),s.bezierCurveTo(o[0],o[1],l[0],l[1],c[0],c[1])}return s},Uc=function(e,s,i){const[n,a,o,l]=s;switch(i){case 0:for(let c=0,d=e.length;c<d;c+=2)e[c]+=n,e[c+1]=l-e[c+1];break;case 90:for(let c=0,d=e.length;c<d;c+=2){const u=e[c];e[c]=e[c+1]+n,e[c+1]=u+a}break;case 180:for(let c=0,d=e.length;c<d;c+=2)e[c]=o-e[c],e[c+1]+=a;break;case 270:for(let c=0,d=e.length;c<d;c+=2){const u=e[c];e[c]=o-e[c+1],e[c+1]=l-u}break;default:throw new Error("Invalid rotation")}return e},kf=function(e,s,i){const[n,a,o,l]=s;switch(i){case 0:for(let c=0,d=e.length;c<d;c+=2)e[c]-=n,e[c+1]=l-e[c+1];break;case 90:for(let c=0,d=e.length;c<d;c+=2){const u=e[c];e[c]=e[c+1]-a,e[c+1]=u-n}break;case 180:for(let c=0,d=e.length;c<d;c+=2)e[c]=o-e[c],e[c+1]-=a;break;case 270:for(let c=0,d=e.length;c<d;c+=2){const u=e[c];e[c]=l-e[c+1],e[c+1]=o-u}break;default:throw new Error("Invalid rotation")}return e},Mf=function(e,s,i,n){var d,u;const a=[],o=this.thickness/2,l=e*s+o,c=e*i+o;for(const f of this.paths){const p=[],b=[];for(let v=0,_=f.length;v<_;v++){const[y,E,w,x]=f[v];if(y[0]===x[0]&&y[1]===x[1]&&_===1){const O=e*y[0]+l,$=e*y[1]+c;p.push(O,$),b.push(O,$);break}const S=e*y[0]+l,C=e*y[1]+c,R=e*E[0]+l,k=e*E[1]+c,D=e*w[0]+l,H=e*w[1]+c,T=e*x[0]+l,j=e*x[1]+c;v===0&&(p.push(S,C),b.push(S,C)),p.push(R,k,D,H,T,j),b.push(R,k),v===_-1&&b.push(T,j)}a.push({bezier:m(d=gt,Qs,Uc).call(d,p,n,this.rotation),points:m(u=gt,Qs,Uc).call(u,b,n,this.rotation)})}return a},jc=function(){let e=1/0,s=-1/0,i=1/0,n=-1/0;for(const a of this.paths)for(const[o,l,c,d]of a){const u=F.bezierBoundingBox(...o,...l,...c,...d);e=Math.min(e,u[0]),i=Math.min(i,u[1]),s=Math.max(s,u[2]),n=Math.max(n,u[3])}return[e,i,s,n]},Fl=function(){return r(this,Pe)?Math.ceil(this.thickness*this.parentScale):0},fa=function(e=!1){if(this.isEmpty())return;if(!r(this,Pe)){m(this,L,Ii).call(this);return}const s=m(this,L,jc).call(this),i=m(this,L,Fl).call(this);g(this,En,Math.max(nt.MIN_SIZE,s[2]-s[0])),g(this,_n,Math.max(nt.MIN_SIZE,s[3]-s[1]));const n=Math.ceil(i+r(this,En)*this.scaleFactor),a=Math.ceil(i+r(this,_n)*this.scaleFactor),[o,l]=this.parentDimensions;this.width=n/o,this.height=a/l,this.setAspectRatio(n,a);const c=this.translationX,d=this.translationY;this.translationX=-s[0],this.translationY=-s[1],m(this,L,Yn).call(this),m(this,L,Ii).call(this),g(this,Tn,n),g(this,Rn,a),this.setDims(n,a);const u=e?i/this.scaleFactor/2:0;this.translate(c-this.translationX-u,d-this.translationY-u)},A(gt,Qs),V(gt,"_defaultColor",null),V(gt,"_defaultOpacity",1),V(gt,"_defaultThickness",1),V(gt,"_type","ink"),V(gt,"_editorType",Q.INK);let Fc=gt;var Bt,Ut,vi,$s,yi,Gr,hs,Pn,cs,qe,Qo,tt,pa,ga,Ol,zc,If,Df,Gc,Nl,Ff;const Aa=class Aa extends nt{constructor(e){super({...e,name:"stampEditor"});A(this,tt);A(this,Bt,null);A(this,Ut,null);A(this,vi,null);A(this,$s,null);A(this,yi,null);A(this,Gr,"");A(this,hs,null);A(this,Pn,null);A(this,cs,null);A(this,qe,!1);A(this,Qo,!1);g(this,$s,e.bitmapUrl),g(this,yi,e.bitmapFile)}static initialize(e,s){nt.initialize(e,s)}static get supportedTypes(){return J(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map(s=>`image/${s}`))}static get supportedTypesStr(){return J(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(e){return this.supportedTypes.includes(e)}static paste(e,s){s.pasteEditor(Q.STAMP,{bitmapFile:e.getAsFile()})}remove(){var e,s;r(this,Ut)&&(g(this,Bt,null),this._uiManager.imageManager.deleteId(r(this,Ut)),(e=r(this,hs))==null||e.remove(),g(this,hs,null),(s=r(this,Pn))==null||s.disconnect(),g(this,Pn,null),r(this,cs)&&(clearTimeout(r(this,cs)),g(this,cs,null))),super.remove()}rebuild(){if(!this.parent){r(this,Ut)&&m(this,tt,Ol).call(this);return}super.rebuild(),this.div!==null&&(r(this,Ut)&&r(this,hs)===null&&m(this,tt,Ol).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(r(this,vi)||r(this,Bt)||r(this,$s)||r(this,yi)||r(this,Ut))}get isResizable(){return!0}render(){if(this.div)return this.div;let e,s;if(this.width&&(e=this.x,s=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),r(this,Bt)?m(this,tt,zc).call(this):m(this,tt,Ol).call(this),this.width){const[i,n]=this.parentDimensions;this.setAt(e*i,s*n,this.width*i,this.height*n)}return this.div}getImageForAltText(){return r(this,hs)}static deserialize(e,s,i){if(e instanceof Qu)return null;const n=super.deserialize(e,s,i),{rect:a,bitmapUrl:o,bitmapId:l,isSvg:c,accessibilityData:d}=e;l&&i.imageManager.isValidId(l)?g(n,Ut,l):g(n,$s,o),g(n,qe,c);const[u,f]=n.pageDimensions;return n.width=(a[2]-a[0])/u,n.height=(a[3]-a[1])/f,d&&(n.altTextData=d),n}serialize(e=!1,s=null){if(this.isEmpty())return null;const i={annotationType:Q.STAMP,bitmapId:r(this,Ut),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:r(this,qe),structTreeParentId:this._structTreeParentId};if(e)return i.bitmapUrl=m(this,tt,Nl).call(this,!0),i.accessibilityData=this.altTextData,i;const{decorative:n,altText:a}=this.altTextData;if(!n&&a&&(i.accessibilityData={type:"Figure",alt:a}),s===null)return i;s.stamps||(s.stamps=new Map);const o=r(this,qe)?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(!s.stamps.has(r(this,Ut)))s.stamps.set(r(this,Ut),{area:o,serialized:i}),i.bitmap=m(this,tt,Nl).call(this,!1);else if(r(this,qe)){const l=s.stamps.get(r(this,Ut));o>l.area&&(l.area=o,l.serialized.bitmap.close(),l.serialized.bitmap=m(this,tt,Nl).call(this,!1))}return i}};Bt=new WeakMap,Ut=new WeakMap,vi=new WeakMap,$s=new WeakMap,yi=new WeakMap,Gr=new WeakMap,hs=new WeakMap,Pn=new WeakMap,cs=new WeakMap,qe=new WeakMap,Qo=new WeakMap,tt=new WeakSet,pa=function(e,s=!1){if(!e){this.remove();return}g(this,Bt,e.bitmap),s||(g(this,Ut,e.id),g(this,qe,e.isSvg)),e.file&&g(this,Gr,e.file.name),m(this,tt,zc).call(this)},ga=function(){g(this,vi,null),this._uiManager.enableWaiting(!1),r(this,hs)&&this.div.focus()},Ol=function(){if(r(this,Ut)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(r(this,Ut)).then(s=>m(this,tt,pa).call(this,s,!0)).finally(()=>m(this,tt,ga).call(this));return}if(r(this,$s)){const s=r(this,$s);g(this,$s,null),this._uiManager.enableWaiting(!0),g(this,vi,this._uiManager.imageManager.getFromUrl(s).then(i=>m(this,tt,pa).call(this,i)).finally(()=>m(this,tt,ga).call(this)));return}if(r(this,yi)){const s=r(this,yi);g(this,yi,null),this._uiManager.enableWaiting(!0),g(this,vi,this._uiManager.imageManager.getFromFile(s).then(i=>m(this,tt,pa).call(this,i)).finally(()=>m(this,tt,ga).call(this)));return}const e=document.createElement("input");e.type="file",e.accept=Aa.supportedTypesStr,g(this,vi,new Promise(s=>{e.addEventListener("change",async()=>{if(!e.files||e.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const i=await this._uiManager.imageManager.getFromFile(e.files[0]);m(this,tt,pa).call(this,i)}s()}),e.addEventListener("cancel",()=>{this.remove(),s()})}).finally(()=>m(this,tt,ga).call(this))),e.click()},zc=function(){const{div:e}=this;let{width:s,height:i}=r(this,Bt);const[n,a]=this.pageDimensions,o=.75;if(this.width)s=this.width*n,i=this.height*a;else if(s>o*n||i>o*a){const u=Math.min(o*n/s,o*a/i);s*=u,i*=u}const[l,c]=this.parentDimensions;this.setDims(s*l/n,i*c/a),this._uiManager.enableWaiting(!1);const d=g(this,hs,document.createElement("canvas"));e.append(d),e.hidden=!1,m(this,tt,Gc).call(this,s,i),m(this,tt,Ff).call(this),r(this,Qo)||(this.parent.addUndoableEditor(this),g(this,Qo,!0)),this._reportTelemetry({action:"inserted_image"}),r(this,Gr)&&d.setAttribute("aria-label",r(this,Gr))},If=function(e,s){var o;const[i,n]=this.parentDimensions;this.width=e/i,this.height=s/n,this.setDims(e,s),(o=this._initialOptions)!=null&&o.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,r(this,cs)!==null&&clearTimeout(r(this,cs)),g(this,cs,setTimeout(()=>{g(this,cs,null),m(this,tt,Gc).call(this,e,s)},200))},Df=function(e,s){const{width:i,height:n}=r(this,Bt);let a=i,o=n,l=r(this,Bt);for(;a>2*e||o>2*s;){const c=a,d=o;a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2)),o>2*s&&(o=o>=16384?Math.floor(o/2)-1:Math.ceil(o/2));const u=new OffscreenCanvas(a,o);u.getContext("2d").drawImage(l,0,0,c,d,0,0,a,o),l=u.transferToImageBitmap()}return l},Gc=function(e,s){e=Math.ceil(e),s=Math.ceil(s);const i=r(this,hs);if(!i||i.width===e&&i.height===s)return;i.width=e,i.height=s;const n=r(this,qe)?r(this,Bt):m(this,tt,Df).call(this,e,s);if(this._uiManager.hasMLManager&&!this.hasAltText()){const l=new OffscreenCanvas(e,s).getContext("2d");l.drawImage(n,0,0,n.width,n.height,0,0,e,s),this._uiManager.mlGuess({service:"image-to-text",request:{data:l.getImageData(0,0,e,s).data,width:e,height:s,channels:4}}).then(c=>{const d=(c==null?void 0:c.output)||"";this.parent&&d&&!this.hasAltText()&&(this.altTextData={altText:d,decorative:!1})})}const a=i.getContext("2d");a.filter=this._uiManager.hcmFilter,a.drawImage(n,0,0,n.width,n.height,0,0,e,s)},Nl=function(e){if(e){if(r(this,qe)){const n=this._uiManager.imageManager.getSvgUrl(r(this,Ut));if(n)return n}const s=document.createElement("canvas");return{width:s.width,height:s.height}=r(this,Bt),s.getContext("2d").drawImage(r(this,Bt),0,0),s.toDataURL()}if(r(this,qe)){const[s,i]=this.pageDimensions,n=Math.round(this.width*s*xi.PDF_TO_CSS_UNITS),a=Math.round(this.height*i*xi.PDF_TO_CSS_UNITS),o=new OffscreenCanvas(n,a);return o.getContext("2d").drawImage(r(this,Bt),0,0,r(this,Bt).width,r(this,Bt).height,0,0,n,a),o.transferToImageBitmap()}return structuredClone(r(this,Bt))},Ff=function(){g(this,Pn,new ResizeObserver(e=>{const s=e[0].contentRect;s.width&&s.height&&m(this,tt,If).call(this,s.width,s.height)})),r(this,Pn).observe(this.div)},V(Aa,"_type","stamp"),V(Aa,"_editorType",Q.STAMP);let $c=Aa;var Ln,Vr,ds,kn,zs,Gs,Vs,Ee,_i,Wr,qr,Zt,B,Ei,jt,Of,Wc,qc,Xc,Hl;const Fe=class Fe{constructor({uiManager:t,pageIndex:e,div:s,accessibilityManager:i,annotationLayer:n,drawLayer:a,textLayer:o,viewport:l,l10n:c}){A(this,jt);A(this,Ln);A(this,Vr,!1);A(this,ds,null);A(this,kn,null);A(this,zs,null);A(this,Gs,null);A(this,Vs,null);A(this,Ee,new Map);A(this,_i,!1);A(this,Wr,!1);A(this,qr,!1);A(this,Zt,null);A(this,B);const d=[...r(Fe,Ei).values()];if(!Fe._initialized){Fe._initialized=!0;for(const u of d)u.initialize(c,t)}t.registerEditorTypes(d),g(this,B,t),this.pageIndex=e,this.div=s,g(this,Ln,i),g(this,ds,n),this.viewport=l,g(this,Zt,o),this.drawLayer=a,r(this,B).addLayer(this)}get isEmpty(){return r(this,Ee).size===0}get isInvisible(){return this.isEmpty&&r(this,B).getMode()===Q.NONE}updateToolbar(t){r(this,B).updateToolbar(t)}updateMode(t=r(this,B).getMode()){switch(m(this,jt,Hl).call(this),t){case Q.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case Q.INK:this.addInkEditorIfNeeded(!1),this.disableTextSelection(),this.togglePointerEvents(!0),this.disableClick();break;case Q.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of r(Fe,Ei).values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){var e;return t===((e=r(this,Zt))==null?void 0:e.div)}addInkEditorIfNeeded(t){if(r(this,B).getMode()!==Q.INK)return;if(!t){for(const s of r(this,Ee).values())if(s.isEmpty()){s.setInBackground();return}}this.createAndAddNewEditor({offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(t){r(this,B).setEditingState(t)}addCommands(t){r(this,B).addCommands(t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){var e;(e=r(this,ds))==null||e.div.classList.toggle("disabled",!t)}enable(){this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const s of r(this,Ee).values())s.enableEditing(),s.show(!0),s.annotationElementId&&(r(this,B).removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!r(this,ds))return;const e=r(this,ds).getEditableAnnotations();for(const s of e){if(s.hide(),r(this,B).isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}}disable(){var i;g(this,qr,!0),this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const n of r(this,Ee).values())if(n.disableEditing(),!!n.annotationElementId){if(n.serialize()!==null){t.set(n.annotationElementId,n);continue}else e.set(n.annotationElementId,n);(i=this.getEditableAnnotation(n.annotationElementId))==null||i.show(),n.remove()}if(r(this,ds)){const n=r(this,ds).getEditableAnnotations();for(const a of n){const{id:o}=a.data;if(r(this,B).isDeletedAnnotationElement(o))continue;let l=e.get(o);if(l){l.resetAnnotationElement(a),l.show(!1),a.show();continue}l=t.get(o),l&&(r(this,B).addChangedExistingAnnotation(l),l.renderAnnotationElement(a),l.show(!1)),a.show()}}m(this,jt,Hl).call(this),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const n of r(Fe,Ei).values())s.remove(`${n._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),g(this,qr,!1)}getEditableAnnotation(t){var e;return((e=r(this,ds))==null?void 0:e.getEditableAnnotation(t))||null}setActiveEditor(t){r(this,B).getActive()!==t&&r(this,B).setActiveEditor(t)}enableTextSelection(){var t;this.div.tabIndex=-1,(t=r(this,Zt))!=null&&t.div&&!r(this,Gs)&&(g(this,Gs,m(this,jt,Of).bind(this)),r(this,Zt).div.addEventListener("pointerdown",r(this,Gs)),r(this,Zt).div.classList.add("highlighting"))}disableTextSelection(){var t;this.div.tabIndex=0,(t=r(this,Zt))!=null&&t.div&&r(this,Gs)&&(r(this,Zt).div.removeEventListener("pointerdown",r(this,Gs)),g(this,Gs,null),r(this,Zt).div.classList.remove("highlighting"))}enableClick(){r(this,zs)||(g(this,zs,this.pointerdown.bind(this)),g(this,kn,this.pointerup.bind(this)),this.div.addEventListener("pointerdown",r(this,zs)),this.div.addEventListener("pointerup",r(this,kn)))}disableClick(){r(this,zs)&&(this.div.removeEventListener("pointerdown",r(this,zs)),this.div.removeEventListener("pointerup",r(this,kn)),g(this,zs,null),g(this,kn,null))}attach(t){r(this,Ee).set(t.id,t);const{annotationElementId:e}=t;e&&r(this,B).isDeletedAnnotationElement(e)&&r(this,B).removeDeletedAnnotationElement(t)}detach(t){var e;r(this,Ee).delete(t.id),(e=r(this,Ln))==null||e.removePointerInTextLayer(t.contentDiv),!r(this,qr)&&t.annotationElementId&&r(this,B).addDeletedAnnotationElement(t)}remove(t){this.detach(t),r(this,B).removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1,r(this,Wr)||this.addInkEditorIfNeeded(!1)}changeParent(t){var e;t.parent!==this&&(t.parent&&t.annotationElementId&&(r(this,B).addDeletedAnnotationElement(t.annotationElementId),nt.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),(e=t.parent)==null||e.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(!(t.parent===this&&t.isAttachedToDOM)){if(this.changeParent(t),r(this,B).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(),r(this,B).addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){var s;if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!r(this,Vs)&&(t._focusEventsAllowed=!1,g(this,Vs,setTimeout(()=>{g(this,Vs,null),t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0}),e.focus())},0))),t._structTreeParentId=(s=r(this,Ln))==null?void 0:s.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||(t.parent=this),t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),s=()=>{t.remove()};this.addCommands({cmd:e,undo:s,mustExec:!1})}getNextId(){return r(this,B).getId()}canCreateNewEmptyEditor(){var t;return(t=r(this,jt,Wc))==null?void 0:t.canCreateNewEmptyEditor()}pasteEditor(t,e){r(this,B).updateToolbar(t),r(this,B).updateMode(t);const{offsetX:s,offsetY:i}=m(this,jt,Xc).call(this),n=this.getNextId(),a=m(this,jt,qc).call(this,{parent:this,id:n,x:s,y:i,uiManager:r(this,B),isCentered:!0,...e});a&&this.add(a)}deserialize(t){var e;return((e=r(Fe,Ei).get(t.annotationType??t.annotationEditorType))==null?void 0:e.deserialize(t,this,r(this,B)))||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),n=m(this,jt,qc).call(this,{parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:r(this,B),isCentered:e,...s});return n&&this.add(n),n}addNewEditor(){this.createAndAddNewEditor(m(this,jt,Xc).call(this),!0)}setSelected(t){r(this,B).setSelected(t)}toggleSelected(t){r(this,B).toggleSelected(t)}isSelected(t){return r(this,B).isSelected(t)}unselect(t){r(this,B).unselect(t)}pointerup(t){const{isMac:e}=he.platform;if(!(t.button!==0||t.ctrlKey&&e)&&t.target===this.div&&r(this,_i)){if(g(this,_i,!1),!r(this,Vr)){g(this,Vr,!0);return}if(r(this,B).getMode()===Q.STAMP){r(this,B).unselectAll();return}this.createAndAddNewEditor(t,!1)}}pointerdown(t){if(r(this,B).getMode()===Q.HIGHLIGHT&&this.enableTextSelection(),r(this,_i)){g(this,_i,!1);return}const{isMac:e}=he.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;g(this,_i,!0);const s=r(this,B).getActive();g(this,Vr,!s||s.isEmpty())}findNewParent(t,e,s){const i=r(this,B).findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}destroy(){var t,e;((t=r(this,B).getActive())==null?void 0:t.parent)===this&&(r(this,B).commitOrRemove(),r(this,B).setActiveEditor(null)),r(this,Vs)&&(clearTimeout(r(this,Vs)),g(this,Vs,null));for(const s of r(this,Ee).values())(e=r(this,Ln))==null||e.removePointerInTextLayer(s.contentDiv),s.setParent(null),s.isAttachedToDOM=!1,s.div.remove();this.div=null,r(this,Ee).clear(),r(this,B).removeLayer(this)}render({viewport:t}){this.viewport=t,Mn(this.div,t);for(const e of r(this,B).getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){r(this,B).commitOrRemove(),m(this,jt,Hl).call(this);const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,Mn(this.div,{rotation:s}),e!==s)for(const i of r(this,Ee).values())i.rotate(s);this.addInkEditorIfNeeded(!1)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return r(this,B).viewParameters.realScale}};Ln=new WeakMap,Vr=new WeakMap,ds=new WeakMap,kn=new WeakMap,zs=new WeakMap,Gs=new WeakMap,Vs=new WeakMap,Ee=new WeakMap,_i=new WeakMap,Wr=new WeakMap,qr=new WeakMap,Zt=new WeakMap,B=new WeakMap,Ei=new WeakMap,jt=new WeakSet,Of=function(t){if(r(this,B).unselectAll(),t.target===r(this,Zt).div){const{isMac:e}=he.platform;if(t.button!==0||t.ctrlKey&&e)return;r(this,B).showAllEditors("highlight",!0,!0),r(this,Zt).div.classList.add("free"),Vl.startHighlighting(this,r(this,B).direction==="ltr",t),r(this,Zt).div.addEventListener("pointerup",()=>{r(this,Zt).div.classList.remove("free")},{once:!0}),t.preventDefault()}},Wc=function(){return r(Fe,Ei).get(r(this,B).getMode())},qc=function(t){const e=r(this,jt,Wc);return e?new e.prototype.constructor(t):null},Xc=function(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),n=Math.max(0,t),a=Math.max(0,e),o=Math.min(window.innerWidth,t+s),l=Math.min(window.innerHeight,e+i),c=(n+o)/2-t,d=(a+l)/2-e,[u,f]=this.viewport.rotation%180===0?[c,d]:[d,c];return{offsetX:u,offsetY:f}},Hl=function(){g(this,Wr,!0);for(const t of r(this,Ee).values())t.isEmpty()&&t.remove();g(this,Wr,!1)},V(Fe,"_initialized",!1),A(Fe,Ei,new Map([Sc,Fc,$c,Vl].map(t=>[t._editorType,t])));let Vc=Fe;var us,Jo,Pt,wi,Zo,Kc,Nn,Qc,Nf;const Dt=class Dt{constructor({pageIndex:t}){A(this,Nn);A(this,us,null);A(this,Jo,0);A(this,Pt,new Map);A(this,wi,new Map);this.pageIndex=t}setParent(t){if(!r(this,us)){g(this,us,t);return}if(r(this,us)!==t){if(r(this,Pt).size>0)for(const e of r(this,Pt).values())e.remove(),t.append(e);g(this,us,t)}}static get _svgFactory(){return J(this,"_svgFactory",new sd)}highlight(t,e,s,i=!1){const n=pe(this,Jo)._++,a=m(this,Nn,Qc).call(this,t.box);a.classList.add("highlight"),t.free&&a.classList.add("free");const o=Dt._svgFactory.createElement("defs");a.append(o);const l=Dt._svgFactory.createElement("path");o.append(l);const c=`path_p${this.pageIndex}_${n}`;l.setAttribute("id",c),l.setAttribute("d",t.toSVGPath()),i&&r(this,wi).set(n,l);const d=m(this,Nn,Nf).call(this,o,c),u=Dt._svgFactory.createElement("use");return a.append(u),a.setAttribute("fill",e),a.setAttribute("fill-opacity",s),u.setAttribute("href",`#${c}`),r(this,Pt).set(n,a),{id:n,clipPathId:`url(#${d})`}}highlightOutline(t){const e=pe(this,Jo)._++,s=m(this,Nn,Qc).call(this,t.box);s.classList.add("highlightOutline");const i=Dt._svgFactory.createElement("defs");s.append(i);const n=Dt._svgFactory.createElement("path");i.append(n);const a=`path_p${this.pageIndex}_${e}`;n.setAttribute("id",a),n.setAttribute("d",t.toSVGPath()),n.setAttribute("vector-effect","non-scaling-stroke");let o;if(t.free){s.classList.add("free");const d=Dt._svgFactory.createElement("mask");i.append(d),o=`mask_p${this.pageIndex}_${e}`,d.setAttribute("id",o),d.setAttribute("maskUnits","objectBoundingBox");const u=Dt._svgFactory.createElement("rect");d.append(u),u.setAttribute("width","1"),u.setAttribute("height","1"),u.setAttribute("fill","white");const f=Dt._svgFactory.createElement("use");d.append(f),f.setAttribute("href",`#${a}`),f.setAttribute("stroke","none"),f.setAttribute("fill","black"),f.setAttribute("fill-rule","nonzero"),f.classList.add("mask")}const l=Dt._svgFactory.createElement("use");s.append(l),l.setAttribute("href",`#${a}`),o&&l.setAttribute("mask",`url(#${o})`);const c=l.cloneNode();return s.append(c),l.classList.add("mainOutline"),c.classList.add("secondaryOutline"),r(this,Pt).set(e,s),e}finalizeLine(t,e){const s=r(this,wi).get(t);r(this,wi).delete(t),this.updateBox(t,e.box),s.setAttribute("d",e.toSVGPath())}updateLine(t,e){r(this,Pt).get(t).firstChild.firstChild.setAttribute("d",e.toSVGPath())}removeFreeHighlight(t){this.remove(t),r(this,wi).delete(t)}updatePath(t,e){r(this,wi).get(t).setAttribute("d",e.toSVGPath())}updateBox(t,e){var s;m(s=Dt,Zo,Kc).call(s,r(this,Pt).get(t),e)}show(t,e){r(this,Pt).get(t).classList.toggle("hidden",!e)}rotate(t,e){r(this,Pt).get(t).setAttribute("data-main-rotation",e)}changeColor(t,e){r(this,Pt).get(t).setAttribute("fill",e)}changeOpacity(t,e){r(this,Pt).get(t).setAttribute("fill-opacity",e)}addClass(t,e){r(this,Pt).get(t).classList.add(e)}removeClass(t,e){r(this,Pt).get(t).classList.remove(e)}remove(t){r(this,us)!==null&&(r(this,Pt).get(t).remove(),r(this,Pt).delete(t))}destroy(){g(this,us,null);for(const t of r(this,Pt).values())t.remove();r(this,Pt).clear()}};us=new WeakMap,Jo=new WeakMap,Pt=new WeakMap,wi=new WeakMap,Zo=new WeakSet,Kc=function(t,{x:e=0,y:s=0,width:i=1,height:n=1}={}){const{style:a}=t;a.top=`${100*s}%`,a.left=`${100*e}%`,a.width=`${100*i}%`,a.height=`${100*n}%`},Nn=new WeakSet,Qc=function(t){var s;const e=Dt._svgFactory.create(1,1,!0);return r(this,us).append(e),e.setAttribute("aria-hidden",!0),m(s=Dt,Zo,Kc).call(s,e,t),e},Nf=function(t,e){const s=Dt._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const n=Dt._svgFactory.createElement("use");return s.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),i},A(Dt,Zo);let Yc=Dt;var fm=z.AbortException,pm=z.AnnotationEditorLayer,gm=z.AnnotationEditorParamsType,mm=z.AnnotationEditorType,bm=z.AnnotationEditorUIManager,Hf=z.AnnotationLayer,Bf=z.AnnotationMode,Am=z.CMapCompressionType,vm=z.ColorPicker,ym=z.DOMSVGFactory,_m=z.DrawLayer,Em=z.FeatureTest,cd=z.GlobalWorkerOptions,wm=z.ImageKind,Sm=z.InvalidPDFException,xm=z.MissingPDFException,Cm=z.OPS,Tm=z.Outliner,Rm=z.PDFDataRangeTransport,Pm=z.PDFDateString,Lm=z.PDFWorker,km=z.PasswordResponses,Mm=z.PermissionFlag,Im=z.PixelsPerInch,Dm=z.RenderingCancelledException,Uf=z.TextLayer,Fm=z.UnexpectedResponseException,Om=z.Util,Nm=z.VerbosityLevel,Hm=z.XfaLayer,Bm=z.build,Um=z.createValidAbsoluteUrl,jm=z.fetchData,jf=z.getDocument,$m=z.getFilenameFromUrl,zm=z.getPdfFilenameFromUrl,Gm=z.getXfaPageViewport,Vm=z.isDataScheme,Wm=z.isPdfFile,qm=z.noContextMenu,Xm=z.normalizeUnicode,Ym=z.renderTextLayer,Km=z.setLayerDimensions,Qm=z.shadow,Jm=z.updateTextLayer,Zm=z.version;const tb=Object.freeze(Object.defineProperty({__proto__:null,AbortException:fm,AnnotationEditorLayer:pm,AnnotationEditorParamsType:gm,AnnotationEditorType:mm,AnnotationEditorUIManager:bm,AnnotationLayer:Hf,AnnotationMode:Bf,CMapCompressionType:Am,ColorPicker:vm,DOMSVGFactory:ym,DrawLayer:_m,FeatureTest:Em,GlobalWorkerOptions:cd,ImageKind:wm,InvalidPDFException:Sm,MissingPDFException:xm,OPS:Cm,Outliner:Tm,PDFDataRangeTransport:Rm,PDFDateString:Pm,PDFWorker:Lm,PasswordResponses:km,PermissionFlag:Mm,PixelsPerInch:Im,RenderingCancelledException:Dm,TextLayer:Uf,UnexpectedResponseException:Fm,Util:Om,VerbosityLevel:Nm,XfaLayer:Hm,build:Bm,createValidAbsoluteUrl:Um,fetchData:jm,getDocument:jf,getFilenameFromUrl:$m,getPdfFilenameFromUrl:zm,getXfaPageViewport:Gm,isDataScheme:Vm,isPdfFile:Wm,noContextMenu:qm,normalizeUnicode:Xm,renderTextLayer:Ym,setLayerDimensions:Km,shadow:Qm,updateTextLayer:Jm,version:Zm},Symbol.toStringTag,{value:"Module"}));var kt=function(h,t,e){if(e||arguments.length===2)for(var s=0,i=t.length,n;s<i;s++)(n||!(s in t))&&(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return h.concat(n||Array.prototype.slice.call(t))},eb=["onCopy","onCut","onPaste"],sb=["onCompositionEnd","onCompositionStart","onCompositionUpdate"],ib=["onFocus","onBlur"],nb=["onInput","onInvalid","onReset","onSubmit"],rb=["onLoad","onError"],ab=["onKeyDown","onKeyPress","onKeyUp"],ob=["onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting"],lb=["onClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp"],hb=["onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop"],cb=["onSelect"],db=["onTouchCancel","onTouchEnd","onTouchMove","onTouchStart"],ub=["onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut"],fb=["onScroll"],pb=["onWheel"],gb=["onAnimationStart","onAnimationEnd","onAnimationIteration"],mb=["onTransitionEnd"],bb=["onToggle"],Ab=["onChange"],vb=kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt(kt([],eb,!0),sb,!0),ib,!0),nb,!0),rb,!0),ab,!0),ob,!0),lb,!0),hb,!0),cb,!0),db,!0),ub,!0),fb,!0),pb,!0),gb,!0),mb,!0),Ab,!0),bb,!0);function $f(h,t){var e={};return vb.forEach(function(s){var i=h[s];i&&(t?e[s]=function(n){return i(n,t(s))}:e[s]=i)}),e}function nl(h){var t=!1,e=new Promise(function(s,i){h.then(function(n){return!t&&s(n)}).catch(function(n){return!t&&i(n)})});return{promise:e,cancel:function(){t=!0}}}var Id=Object.prototype.hasOwnProperty;function Dd(h,t,e){for(e of h.keys())if(Zn(e,t))return e}function Zn(h,t){var e,s,i;if(h===t)return!0;if(h&&t&&(e=h.constructor)===t.constructor){if(e===Date)return h.getTime()===t.getTime();if(e===RegExp)return h.toString()===t.toString();if(e===Array){if((s=h.length)===t.length)for(;s--&&Zn(h[s],t[s]););return s===-1}if(e===Set){if(h.size!==t.size)return!1;for(s of h)if(i=s,i&&typeof i=="object"&&(i=Dd(t,i),!i)||!t.has(i))return!1;return!0}if(e===Map){if(h.size!==t.size)return!1;for(s of h)if(i=s[0],i&&typeof i=="object"&&(i=Dd(t,i),!i)||!Zn(s[1],t.get(i)))return!1;return!0}if(e===ArrayBuffer)h=new Uint8Array(h),t=new Uint8Array(t);else if(e===DataView){if((s=h.byteLength)===t.byteLength)for(;s--&&h.getInt8(s)===t.getInt8(s););return s===-1}if(ArrayBuffer.isView(h)){if((s=h.byteLength)===t.byteLength)for(;s--&&h[s]===t[s];);return s===-1}if(!e||typeof h=="object"){s=0;for(e in h)if(Id.call(h,e)&&++s&&!Id.call(t,e)||!(e in t)||!Zn(h[e],t[e]))return!1;return Object.keys(t).length===s}}return h!==h&&t!==t}const zf=N.createContext(null);function tr({children:h,type:t}){return _t("div",{className:`react-pdf__message react-pdf__message--${t}`,children:h})}const yb="noopener noreferrer nofollow";class _b{constructor(){this.externalLinkEnabled=!0,this.externalLinkRel=void 0,this.externalLinkTarget=void 0,this.isInPresentationMode=!1,this.pdfDocument=void 0,this.pdfViewer=void 0}setDocument(t){this.pdfDocument=t}setViewer(t){this.pdfViewer=t}setExternalLinkRel(t){this.externalLinkRel=t}setExternalLinkTarget(t){this.externalLinkTarget=t}setHistory(){}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return ht(this.pdfViewer),this.pdfViewer.currentPageNumber||0}set page(t){ht(this.pdfViewer),this.pdfViewer.currentPageNumber=t}get rotation(){return 0}set rotation(t){}goToDestination(t){return new Promise(e=>{ht(this.pdfDocument),ht(t),typeof t=="string"?this.pdfDocument.getDestination(t).then(e):Array.isArray(t)?e(t):t.then(e)}).then(e=>{ht(Array.isArray(e));const s=e[0];new Promise(i=>{ht(this.pdfDocument),s instanceof Object?this.pdfDocument.getPageIndex(s).then(n=>{i(n)}).catch(()=>{ht(!1)}):typeof s=="number"?i(s):ht(!1)}).then(i=>{const n=i+1;ht(this.pdfViewer),ht(n>=1&&n<=this.pagesCount),this.pdfViewer.scrollPageIntoView({dest:e,pageIndex:i,pageNumber:n})})})}navigateTo(t){this.goToDestination(t)}goToPage(t){const e=t-1;ht(this.pdfViewer),ht(t>=1&&t<=this.pagesCount),this.pdfViewer.scrollPageIntoView({pageIndex:e,pageNumber:t})}addLinkAttributes(t,e,s){t.href=e,t.rel=this.externalLinkRel||yb,t.target=s?"_blank":this.externalLinkTarget||""}getDestinationHash(){return"#"}getAnchorUrl(){return"#"}setHash(){}executeNamedAction(){}cachePageRef(){}isPageVisible(){return!0}isPageCached(){return!0}executeSetOCGState(){}}const Fd={NEED_PASSWORD:1,INCORRECT_PASSWORD:2},wh=typeof document<"u",Gf=wh&&window.location.protocol==="file:";function Eb(h){return typeof h<"u"}function Li(h){return Eb(h)&&h!==null}function wb(h){return typeof h=="string"}function Sb(h){return h instanceof ArrayBuffer}function xb(h){return ht(wh),h instanceof Blob}function Jc(h){return wb(h)&&/^data:/.test(h)}function Od(h){ht(Jc(h));const[t="",e=""]=h.split(",");return t.split(";").indexOf("base64")!==-1?atob(e):unescape(e)}function Cb(){return wh&&window.devicePixelRatio||1}const Vf="On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.";function Nd(){Wt(!Gf,`Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${Vf}`)}function Tb(){Wt(!Gf,`Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${Vf}`)}function Fn(h){h&&h.cancel&&h.cancel()}function Zc(h,t){return Object.defineProperty(h,"width",{get(){return this.view[2]*t},configurable:!0}),Object.defineProperty(h,"height",{get(){return this.view[3]*t},configurable:!0}),Object.defineProperty(h,"originalWidth",{get(){return this.view[2]},configurable:!0}),Object.defineProperty(h,"originalHeight",{get(){return this.view[3]},configurable:!0}),h}function Rb(h){return h.name==="RenderingCancelledException"}function Pb(h){return new Promise((t,e)=>{const s=new FileReader;s.onload=()=>{if(!s.result)return e(new Error("Error while reading a file."));t(s.result)},s.onerror=i=>{if(!i.target)return e(new Error("Error while reading a file."));const{error:n}=i.target;if(!n)return e(new Error("Error while reading a file."));switch(n.code){case n.NOT_FOUND_ERR:return e(new Error("Error while reading a file: File not found."));case n.SECURITY_ERR:return e(new Error("Error while reading a file: Security error."));case n.ABORT_ERR:return e(new Error("Error while reading a file: Aborted."));default:return e(new Error("Error while reading a file."))}},s.readAsArrayBuffer(h)})}function Lb(h,t){switch(t.type){case"RESOLVE":return{value:t.value,error:void 0};case"REJECT":return{value:!1,error:t.error};case"RESET":return{value:void 0,error:void 0};default:return h}}function Xr(){return N.useReducer(Lb,{value:void 0,error:void 0})}var kb=function(h,t,e,s){function i(n){return n instanceof e?n:new e(function(a){a(n)})}return new(e||(e=Promise))(function(n,a){function o(d){try{c(s.next(d))}catch(u){a(u)}}function l(d){try{c(s.throw(d))}catch(u){a(u)}}function c(d){d.done?n(d.value):i(d.value).then(o,l)}c((s=s.apply(h,t||[])).next())})},Hd=function(h,t){var e={};for(var s in h)Object.prototype.hasOwnProperty.call(h,s)&&t.indexOf(s)<0&&(e[s]=h[s]);if(h!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,s=Object.getOwnPropertySymbols(h);i<s.length;i++)t.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(h,s[i])&&(e[s[i]]=h[s[i]]);return e};const{PDFDataRangeTransport:Mb}=tb,Ib=(h,t)=>{switch(t){case Fd.NEED_PASSWORD:{const e=prompt("Enter the password to open this PDF file.");h(e);break}case Fd.INCORRECT_PASSWORD:{const e=prompt("Invalid password. Please try again.");h(e);break}}};function Bd(h){return typeof h=="object"&&h!==null&&("data"in h||"range"in h||"url"in h)}const Db=N.forwardRef(function(t,e){var{children:s,className:i,error:n="Failed to load PDF file.",externalLinkRel:a,externalLinkTarget:o,file:l,inputRef:c,imageResourcesPath:d,loading:u="Loading PDF…",noData:f="No PDF file specified.",onItemClick:p,onLoadError:b,onLoadProgress:v,onLoadSuccess:_,onPassword:y=Ib,onSourceError:E,onSourceSuccess:w,options:x,renderMode:S,rotate:C}=t,R=Hd(t,["children","className","error","externalLinkRel","externalLinkTarget","file","inputRef","imageResourcesPath","loading","noData","onItemClick","onLoadError","onLoadProgress","onLoadSuccess","onPassword","onSourceError","onSourceSuccess","options","renderMode","rotate"]);const[k,D]=Xr(),{value:H,error:T}=k,[j,O]=Xr(),{value:$,error:W}=j,Z=N.useRef(new _b),M=N.useRef([]),U=N.useRef(void 0),Xt=N.useRef(void 0);l&&l!==U.current&&Bd(l)&&(Wt(!Zn(l,U.current),`File prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.`),U.current=l),x&&x!==Xt.current&&(Wt(!Zn(x,Xt.current),`Options prop passed to <Document /> changed, but it's equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.`),Xt.current=x);const Se=N.useRef({scrollPageIntoView:dt=>{const{dest:Yt,pageNumber:De,pageIndex:Zs=De-1}=dt;if(p){p({dest:Yt,pageIndex:Zs,pageNumber:De});return}const rl=M.current[Zs];if(rl){rl.scrollIntoView();return}Wt(!1,`An internal link leading to page ${De} was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>.`)}});N.useImperativeHandle(e,()=>({linkService:Z,pages:M,viewer:Se}),[]);function ue(){w&&w()}function vt(){T&&(Wt(!1,T.toString()),E&&E(T))}function pt(){D({type:"RESET"})}N.useEffect(pt,[l,D]);const $t=N.useCallback(()=>kb(this,void 0,void 0,function*(){if(!l)return null;if(typeof l=="string")return Jc(l)?{data:Od(l)}:(Nd(),{url:l});if(l instanceof Mb)return{range:l};if(Sb(l))return{data:l};if(wh&&xb(l))return{data:yield Pb(l)};if(ht(typeof l=="object"),ht(Bd(l)),"url"in l&&typeof l.url=="string"){if(Jc(l.url)){const{url:dt}=l,Yt=Hd(l,["url"]),De=Od(dt);return Object.assign({data:De},Yt)}Nd()}return l}),[l]);N.useEffect(()=>{const dt=nl($t());return dt.promise.then(Yt=>{D({type:"RESOLVE",value:Yt})}).catch(Yt=>{D({type:"REJECT",error:Yt})}),()=>{Fn(dt)}},[$t,D]),N.useEffect(()=>{if(!(typeof H>"u")){if(H===!1){vt();return}ue()}},[H]);function Ie(){$&&(_&&_($),M.current=new Array($.numPages),Z.current.setDocument($))}function ie(){W&&(Wt(!1,W.toString()),b&&b(W))}function et(){O({type:"RESET"})}N.useEffect(et,[O,H]);function jn(){if(!H)return;const dt=Object.assign(Object.assign({},H),x),Yt=jf(dt);v&&(Yt.onProgress=v),y&&(Yt.onPassword=y);const De=Yt;return De.promise.then(Zs=>{O({type:"RESOLVE",value:Zs})}).catch(Zs=>{De.destroyed||O({type:"REJECT",error:Zs})}),()=>{De.destroy()}}N.useEffect(jn,[x,O,H]),N.useEffect(()=>{if(!(typeof $>"u")){if($===!1){ie();return}Ie()}},[$]);function Pi(){Z.current.setViewer(Se.current),Z.current.setExternalLinkRel(a),Z.current.setExternalLinkTarget(o)}N.useEffect(Pi,[a,o]);function bt(dt,Yt){M.current[dt]=Yt}function Yr(dt){delete M.current[dt]}const Kr=N.useMemo(()=>({imageResourcesPath:d,linkService:Z.current,onItemClick:p,pdf:$,registerPage:bt,renderMode:S,rotate:C,unregisterPage:Yr}),[d,p,$,S,C]),fe=N.useMemo(()=>$f(R,()=>$),[R,$]);function Xe(){return _t(zf.Provider,{value:Kr,children:s})}function Js(){return l?$==null?_t(tr,{type:"loading",children:typeof u=="function"?u():u}):$===!1?_t(tr,{type:"error",children:typeof n=="function"?n():n}):Xe():_t(tr,{type:"no-data",children:typeof f=="function"?f():f})}return _t("div",Object.assign({className:dh("react-pdf__Document",i),ref:c,style:{"--scale-factor":"1"}},fe,{children:Js()}))});function Wf(){return N.useContext(zf)}function qf(){for(var h=[],t=0;t<arguments.length;t++)h[t]=arguments[t];var e=h.filter(Boolean);if(e.length<=1){var s=e[0];return s||null}return function(n){e.forEach(function(a){typeof a=="function"?a(n):a&&(a.current=n)})}}const Xf=N.createContext(null),Yf={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},Fb=/^H(\d+)$/;function Ob(h){return h in Yf}function Sh(h){return"children"in h}function Kf(h){return Sh(h)?h.children.length===1&&0 in h.children&&"id"in h.children[0]:!1}function Nb(h){const t={};if(Sh(h)){const{role:e}=h,s=e.match(Fb);if(s)t.role="heading",t["aria-level"]=Number(s[1]);else if(Ob(e)){const i=Yf[e];i&&(t.role=i)}}return t}function Qf(h){const t={};if(Sh(h)){if(h.alt!==void 0&&(t["aria-label"]=h.alt),h.lang!==void 0&&(t.lang=h.lang),Kf(h)){const[e]=h.children;if(e){const s=Qf(e);return Object.assign(Object.assign({},t),s)}}}else"id"in h&&(t["aria-owns"]=h.id);return t}function Hb(h){return h?Object.assign(Object.assign({},Nb(h)),Qf(h)):null}function Jf({className:h,node:t}){const e=N.useMemo(()=>Hb(t),[t]),s=N.useMemo(()=>!Sh(t)||Kf(t)?null:t.children.map((i,n)=>_t(Jf,{node:i},n)),[t]);return _t("span",Object.assign({className:h},e,{children:s}))}function xh(){return N.useContext(Xf)}function Bb(){const h=xh();ht(h);const{onGetStructTreeError:t,onGetStructTreeSuccess:e}=h,[s,i]=Xr(),{value:n,error:a}=s,{customTextRenderer:o,page:l}=h;function c(){n&&e&&e(n)}function d(){a&&(Wt(!1,a.toString()),t&&t(a))}function u(){i({type:"RESET"})}N.useEffect(u,[i,l]);function f(){if(o||!l)return;const p=nl(l.getStructTree()),b=p;return p.promise.then(v=>{i({type:"RESOLVE",value:v})}).catch(v=>{i({type:"REJECT",error:v})}),()=>Fn(b)}return N.useEffect(f,[o,l,i]),N.useEffect(()=>{if(n!==void 0){if(n===!1){d();return}c()}},[n]),n?_t(Jf,{className:"react-pdf__Page__structTree structTree",node:n}):null}const Ud=Bf;function Ub(h){const t=xh();ht(t);const e=Object.assign(Object.assign({},t),h),{_className:s,canvasBackground:i,devicePixelRatio:n=Cb(),onRenderError:a,onRenderSuccess:o,page:l,renderForms:c,renderTextLayer:d,rotate:u,scale:f}=e,{canvasRef:p}=h;ht(l);const b=N.useRef(null);function v(){l&&o&&o(Zc(l,f))}function _(S){Rb(S)||(Wt(!1,S.toString()),a&&a(S))}const y=N.useMemo(()=>l.getViewport({scale:f*n,rotation:u}),[n,l,u,f]),E=N.useMemo(()=>l.getViewport({scale:f,rotation:u}),[l,u,f]);function w(){if(!l)return;l.cleanup();const{current:S}=b;if(!S)return;S.width=y.width,S.height=y.height,S.style.width=`${Math.floor(E.width)}px`,S.style.height=`${Math.floor(E.height)}px`,S.style.visibility="hidden";const C={annotationMode:c?Ud.ENABLE_FORMS:Ud.ENABLE,canvasContext:S.getContext("2d",{alpha:!1}),viewport:y};i&&(C.background=i);const R=l.render(C),k=R;return R.promise.then(()=>{S.style.visibility="",v()}).catch(_),()=>Fn(k)}N.useEffect(w,[i,b,n,l,c,y,E]);const x=N.useCallback(()=>{const{current:S}=b;S&&(S.width=0,S.height=0)},[b]);return N.useEffect(()=>x,[x]),_t("canvas",{className:`${s}__canvas`,dir:"ltr",ref:qf(p,b),style:{display:"block",userSelect:"none"},children:d?_t(Bb,{}):null})}function jb(h){return"str"in h}function $b(){const h=xh();ht(h);const{customTextRenderer:t,onGetTextError:e,onGetTextSuccess:s,onRenderTextLayerError:i,onRenderTextLayerSuccess:n,page:a,pageIndex:o,pageNumber:l,rotate:c,scale:d}=h;ht(a);const[u,f]=Xr(),{value:p,error:b}=u,v=N.useRef(null),_=N.useRef(void 0);Wt(parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-text-layer"),10)===1,"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer");function y(){p&&s&&s(p)}function E(){b&&(Wt(!1,b.toString()),e&&e(b))}function w(){f({type:"RESET"})}N.useEffect(w,[a,f]);function x(){if(!a)return;const T=nl(a.getTextContent()),j=T;return T.promise.then(O=>{f({type:"RESOLVE",value:O})}).catch(O=>{f({type:"REJECT",error:O})}),()=>Fn(j)}N.useEffect(x,[a,f]),N.useEffect(()=>{if(p!==void 0){if(p===!1){E();return}y()}},[p]);const S=N.useCallback(()=>{n&&n()},[n]),C=N.useCallback(T=>{Wt(!1,T.toString()),i&&i(T)},[i]);function R(){const T=_.current;T&&T.classList.add("active")}function k(){const T=_.current;T&&T.classList.remove("active")}const D=N.useMemo(()=>a.getViewport({scale:d,rotation:c}),[a,c,d]);function H(){if(!a||!p)return;const{current:T}=v;if(!T)return;T.innerHTML="";const j=a.streamTextContent({includeMarkedContent:!0}),O={container:T,textContentSource:j,viewport:D},$=new Uf(O),W=$;return $.render().then(()=>{const Z=document.createElement("div");Z.className="endOfContent",T.append(Z),_.current=Z;const M=T.querySelectorAll('[role="presentation"]');if(t){let U=0;p.items.forEach((Xt,Se)=>{if(!jb(Xt))return;const ue=M[U];if(!ue)return;const vt=t(Object.assign({pageIndex:o,pageNumber:l,itemIndex:Se},Xt));ue.innerHTML=vt,U+=Xt.str&&Xt.hasEOL?2:1})}S()}).catch(C),()=>Fn(W)}return N.useLayoutEffect(H,[t,C,S,a,o,l,p,D]),_t("div",{className:dh("react-pdf__Page__textContent","textLayer"),onMouseUp:k,onMouseDown:R,ref:v})}function zb(){const h=Wf(),t=xh();ht(t);const e=Object.assign(Object.assign({},h),t),{imageResourcesPath:s,linkService:i,onGetAnnotationsError:n,onGetAnnotationsSuccess:a,onRenderAnnotationLayerError:o,onRenderAnnotationLayerSuccess:l,page:c,pdf:d,renderForms:u,rotate:f,scale:p=1}=e;ht(d),ht(c),ht(i);const[b,v]=Xr(),{value:_,error:y}=b,E=N.useRef(null);Wt(parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-annotation-layer"),10)===1,"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations");function w(){_&&a&&a(_)}function x(){y&&(Wt(!1,y.toString()),n&&n(y))}function S(){v({type:"RESET"})}N.useEffect(S,[v,c]);function C(){if(!c)return;const T=nl(c.getAnnotations()),j=T;return T.promise.then(O=>{v({type:"RESOLVE",value:O})}).catch(O=>{v({type:"REJECT",error:O})}),()=>{Fn(j)}}N.useEffect(C,[v,c,u]),N.useEffect(()=>{if(_!==void 0){if(_===!1){x();return}w()}},[_]);function R(){l&&l()}function k(T){Wt(!1,`${T}`),o&&o(T)}const D=N.useMemo(()=>c.getViewport({scale:p,rotation:f}),[c,f,p]);function H(){if(!d||!c||!i||!_)return;const{current:T}=E;if(!T)return;const j=D.clone({dontFlip:!0}),O={accessibilityManager:null,annotationCanvasMap:null,annotationEditorUIManager:null,div:T,l10n:null,page:c,viewport:j},$={annotations:_,annotationStorage:d.annotationStorage,div:T,imageResourcesPath:s,linkService:i,page:c,renderForms:u,viewport:j};T.innerHTML="";try{new Hf(O).render($),R()}catch(W){k(W)}return()=>{}}return N.useEffect(H,[_,s,i,c,u,D]),_t("div",{className:dh("react-pdf__Page__annotations","annotationLayer"),ref:E})}var Gb=function(h,t){var e={};for(var s in h)Object.prototype.hasOwnProperty.call(h,s)&&t.indexOf(s)<0&&(e[s]=h[s]);if(h!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,s=Object.getOwnPropertySymbols(h);i<s.length;i++)t.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(h,s[i])&&(e[s[i]]=h[s[i]]);return e};const jd=1;function Vb(h){const t=Wf(),e=Object.assign(Object.assign({},t),h),{_className:s="react-pdf__Page",_enableRegisterUnregisterPage:i=!0,canvasBackground:n,canvasRef:a,children:o,className:l,customRenderer:c,customTextRenderer:d,devicePixelRatio:u,error:f="Failed to load the page.",height:p,inputRef:b,loading:v="Loading page…",noData:_="No page specified.",onGetAnnotationsError:y,onGetAnnotationsSuccess:E,onGetStructTreeError:w,onGetStructTreeSuccess:x,onGetTextError:S,onGetTextSuccess:C,onLoadError:R,onLoadSuccess:k,onRenderAnnotationLayerError:D,onRenderAnnotationLayerSuccess:H,onRenderError:T,onRenderSuccess:j,onRenderTextLayerError:O,onRenderTextLayerSuccess:$,pageIndex:W,pageNumber:Z,pdf:M,registerPage:U,renderAnnotationLayer:Xt=!0,renderForms:Se=!1,renderMode:ue="canvas",renderTextLayer:vt=!0,rotate:pt,scale:$t=jd,unregisterPage:Ie,width:ie}=e,et=Gb(e,["_className","_enableRegisterUnregisterPage","canvasBackground","canvasRef","children","className","customRenderer","customTextRenderer","devicePixelRatio","error","height","inputRef","loading","noData","onGetAnnotationsError","onGetAnnotationsSuccess","onGetStructTreeError","onGetStructTreeSuccess","onGetTextError","onGetTextSuccess","onLoadError","onLoadSuccess","onRenderAnnotationLayerError","onRenderAnnotationLayerSuccess","onRenderError","onRenderSuccess","onRenderTextLayerError","onRenderTextLayerSuccess","pageIndex","pageNumber","pdf","registerPage","renderAnnotationLayer","renderForms","renderMode","renderTextLayer","rotate","scale","unregisterPage","width"]),[jn,Pi]=Xr(),{value:bt,error:Yr}=jn,Kr=N.useRef(null);ht(M);const fe=Li(Z)?Z-1:W??null,Xe=Z??(Li(W)?W+1:null),Js=pt??(bt?bt.rotate:null),dt=N.useMemo(()=>{if(!bt)return null;let $n=1;const Ch=$t??jd;if(ie||p){const zn=bt.getViewport({scale:1,rotation:Js});ie?$n=ie/zn.width:p&&($n=p/zn.height)}return Ch*$n},[p,bt,Js,$t,ie]);function Yt(){return()=>{Li(fe)&&i&&Ie&&Ie(fe)}}N.useEffect(Yt,[i,M,fe,Ie]);function De(){if(k){if(!bt||!dt)return;k(Zc(bt,dt))}if(i&&U){if(!Li(fe)||!Kr.current)return;U(fe,Kr.current)}}function Zs(){Yr&&(Wt(!1,Yr.toString()),R&&R(Yr))}function rl(){Pi({type:"RESET"})}N.useEffect(rl,[Pi,M,fe]);function Zf(){if(!M||!Xe)return;const $n=nl(M.getPage(Xe)),Ch=$n;return $n.promise.then(zn=>{Pi({type:"RESOLVE",value:zn})}).catch(zn=>{Pi({type:"REJECT",error:zn})}),()=>Fn(Ch)}N.useEffect(Zf,[Pi,M,fe,Xe,U]),N.useEffect(()=>{if(bt!==void 0){if(bt===!1){Zs();return}De()}},[bt,dt]);const tp=N.useMemo(()=>bt&&Li(fe)&&Xe&&Li(Js)&&Li(dt)?{_className:s,canvasBackground:n,customTextRenderer:d,devicePixelRatio:u,onGetAnnotationsError:y,onGetAnnotationsSuccess:E,onGetStructTreeError:w,onGetStructTreeSuccess:x,onGetTextError:S,onGetTextSuccess:C,onRenderAnnotationLayerError:D,onRenderAnnotationLayerSuccess:H,onRenderError:T,onRenderSuccess:j,onRenderTextLayerError:O,onRenderTextLayerSuccess:$,page:bt,pageIndex:fe,pageNumber:Xe,renderForms:Se,renderTextLayer:vt,rotate:Js,scale:dt}:null,[s,n,d,u,y,E,w,x,S,C,D,H,T,j,O,$,bt,fe,Xe,Se,vt,Js,dt]),ep=N.useMemo(()=>$f(et,()=>bt&&(dt?Zc(bt,dt):void 0)),[et,bt,dt]),al=`${fe}@${dt}/${Js}`;function sp(){switch(ue){case"custom":return ht(c),_t(c,{},`${al}_custom`);case"none":return null;case"canvas":default:return _t(Ub,{canvasRef:a},`${al}_canvas`)}}function ip(){return vt?_t($b,{},`${al}_text`):null}function np(){return Xt?_t(zb,{},`${al}_annotations`):null}function rp(){return hp(Xf.Provider,{value:tp,children:[sp(),ip(),np(),o]})}function ap(){return Xe?M===null||bt===void 0||bt===null?_t(tr,{type:"loading",children:typeof v=="function"?v():v}):M===!1||bt===!1?_t(tr,{type:"error",children:typeof f=="function"?f():f}):rp():_t(tr,{type:"no-data",children:typeof _=="function"?_():_})}return _t("div",Object.assign({className:dh(s,l),"data-page-number":Xe,ref:qf(b,Kr),style:{"--scale-factor":`${dt}`,backgroundColor:n||"white",position:"relative",minWidth:"min-content",minHeight:"min-content"}},ep,{children:ap()}))}Tb();cd.workerSrc="pdf.worker.mjs";cd.workerSrc=cp().pdfjsWorkerSrc;var Wb=function(h){var t=h.classnames,e=h.className,s=h.loading,i=h.width,n=i===void 0?300:i,a=Qr(ft.useState(h.file),2),o=a[0],l=a[1],c=Qr(ft.useState(!1),2),d=c[0],u=c[1],f=Qr(ft.useState(1),2),p=f[0],b=f[1],v=Qr(ft.useState(1),2),_=v[0],y=v[1],E=Qr(ft.useState(1),2),w=E[0],x=E[1],S=ft.useRef();ft.useEffect(function(){h.file instanceof ArrayBuffer&&h.file.byteLength>0?l(h.file):l(void 0)},[h.file]);function C(j){var O=j.numPages;u(!0),x(O)}function R(j){var O=p+j;O<=0||O>w||b(O)}function k(j){var O=+j.target.value;if(isNaN(O)||O<=0||O>w){S.current&&(S.current.value=p+"");return}b(O)}function D(j){y(_*j)}function H(){return ft.createElement("div",{className:t("PdfViewer-Loading")},ft.createElement(up,null))}function T(){return ft.createElement("div",{className:t("PdfViewer-Tool")},ft.createElement(ll,{className:"icon",icon:"prev",onClick:function(){return R(-1)}}),ft.createElement(fp,{className:"page-input",value:p,onBlur:k,ref:S}),ft.createElement("span",{className:"gap"},"/"),ft.createElement("span",null,w),ft.createElement(ll,{className:"icon",icon:"next",onClick:function(){return R(1)}}),ft.createElement(ll,{className:"icon",icon:"zoom-in",onClick:function(){return D(1.2)}}),ft.createElement(ll,{className:"icon",icon:"zoom-out",onClick:function(){return D(.8)}}))}return ft.createElement("div",{className:t(e,"PdfViewer")},!o||s?H():ft.createElement(ft.Fragment,null,ft.createElement("div",{className:t("PdfViewer-Content",{"is-loaded":d})},ft.createElement(Db,{file:o,onLoadSuccess:C,onLoadError:function(j){return console.log(j)},loading:H()},ft.createElement(Vb,{className:t("PdfViewer-Content-Page"),pageNumber:p,width:n,height:h.height,loading:H(),noData:ft.createElement("div",null,"No PDF data"),scale:_,renderTextLayer:!1,renderAnnotationLayer:!1}))),d?T():null))},Qb=dp(Wb);export{Qb as default};
