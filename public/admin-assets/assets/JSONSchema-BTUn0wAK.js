import{bU as s,bV as c,t as r,v as l,l as o,z as u,D as i,Q as d,T as a,a2 as p}from"./index-CEmnTf-r.js";var h=s({sourceField:"schema",injectedPropsFilter:function(n,t){return{schema:n.config,loading:n.loading}}})(c),m=function(n){r(t,n);function t(){return n!==null&&n.apply(this,arguments)||this}return t.prototype.controlRef=function(e){for(;e!=null&&e.getWrappedInstance;)e=e.getWrappedInstance();this.control=e},t.prototype.validate=function(){var e;return(e=this.control)===null||e===void 0?void 0:e.validate()},t.prototype.render=function(){var e=l(this.props,[]);return o.createElement(h,u({},e,{ref:this.controlRef}))},i([d,a("design:type",Function),a("design:paramtypes",[Object]),a("design:returntype",void 0)],t.prototype,"controlRef",null),t}(o.PureComponent),v=function(n){r(t,n);function t(){return n!==null&&n.apply(this,arguments)||this}return t=i([p({type:"json-schema",strictMode:!1})],t),t}(m);export{v as JSONSchemaRenderer,m as default};
