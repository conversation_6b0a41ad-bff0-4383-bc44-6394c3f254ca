import{t as b,aL as m,a4 as g,v as y,z as v,w as h,x as V,l as u,D as p,Q as f,T as d,bU as C,cx as B,a2 as O}from"./index-CEmnTf-r.js";var _=function(i){b(r,i);function r(){return i!==null&&i.apply(this,arguments)||this}return r.prototype.renderEtrValue=function(e,t){return this.props.render("inline",Object.assign({},e,{label:!1,inputOnly:!0,changeImmediately:!0}),t)},r.prototype.renderPickerIcon=function(){var e=this.props,t=e.render,n=e.pickerIcon;return n?t("picker-icon",n):void 0},r.prototype.getAddBtnVisible=function(e){var t=this.props,n=t.data,a=t.addBtnVisibleOn;return typeof a=="string"&&a?m(a,g(n,e)):!0},r.prototype.getAddGroupBtnVisible=function(e){var t=this.props,n=t.data,a=t.addGroupBtnVisibleOn;return typeof a=="string"&&a?m(a,g(n,e)):!0},r.prototype.validate=function(){var e,t=this.props,n=t.value,a=t.required,l=t.translate;if(a){if(!n||!n.children)return l("Condition.isRequired");var s=!0,c=["is_empty","is_not_empty"];return(e=n==null?void 0:n.children)===null||e===void 0||e.forEach(function(o){if(o.op&&(o.right||~c.indexOf(o.op))){s=!1;return}}),s?l("Condition.isRequired"):null}},r.prototype.render=function(){var e=this.props,t=e.className,n=e.classnames;e.style,e.pickerIcon;var a=e.env,l=e.popOverContainer,s=e.mobileUI,c=y(e,["className","classnames","style","pickerIcon","env","popOverContainer","mobileUI"]),o=this.props.formula?v({},this.props.formula):void 0;return o&&o.variables&&h(o.variables)&&(o.variables=V(o.variables,this.props.data,"| raw")),u.createElement("div",{className:n("ConditionBuilderControl",{"is-mobile":s},t)},u.createElement(E,v({renderEtrValue:this.renderEtrValue,pickerIcon:this.renderPickerIcon(),isAddBtnVisibleOn:this.getAddBtnVisible,isAddGroupBtnVisibleOn:this.getAddGroupBtnVisible,popOverContainer:l||a.getModalContainer},c,{formula:o})))},p([f,d("design:type",Function),d("design:paramtypes",[Object,Object]),d("design:returntype",void 0)],r.prototype,"renderEtrValue",null),p([f,d("design:type",Function),d("design:paramtypes",[Object]),d("design:returntype",void 0)],r.prototype,"getAddBtnVisible",null),p([f,d("design:type",Function),d("design:paramtypes",[Object]),d("design:returntype",void 0)],r.prototype,"getAddGroupBtnVisible",null),r}(u.PureComponent),E=C({adaptor:function(i){return i.fields||i}})(function(i){b(r,i);function r(){return i!==null&&i.apply(this,arguments)||this}return r.prototype.render=function(){var e=this.props,t=e.loading,n=e.config;e.deferLoad;var a=e.disabled,l=e.renderEtrValue,s=y(e,["loading","config","deferLoad","disabled","renderEtrValue"]);return u.createElement(B,v({},s,{fields:n||s.fields||[],disabled:a||t,renderEtrValue:l}))},r}(u.Component)),A=function(i){b(r,i);function r(){return i!==null&&i.apply(this,arguments)||this}return r=p([O({type:"condition-builder",strictMode:!1})],r),r}(_);export{A as ConditionBuilderRenderer,_ as default};
