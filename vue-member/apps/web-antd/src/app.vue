<script lang="ts" setup>
import { computed, onMounted } from 'vue';

import { useAntdDesignTokens } from '@vben/hooks';
import {
  preferences,
  updatePreferences,
  usePreferences,
} from '@vben/preferences';

import { App, ConfigProvider, theme } from 'ant-design-vue';

import { getSetting } from '#/api';
import { antdLocale } from '#/locales';

defineOptions({ name: 'App' });

const { isDark } = usePreferences();
const { tokens } = useAntdDesignTokens();

const tokenTheme = computed(() => {
  const algorithm = isDark.value
    ? [theme.darkAlgorithm]
    : [theme.defaultAlgorithm];

  // antd 紧凑模式算法
  if (preferences.app.compact) {
    algorithm.push(theme.compactAlgorithm);
  }

  return {
    algorithm,
    token: tokens,
  };
});

onMounted(async () => {
  try {
    const setting = await getSetting();
    updatePreferences({
      app: {
        name: setting.web_site_name,
        pageTitle: setting.web_site_title,
      },
      logo: {
        source: setting.web_logo || '',
      },
      copyright: {
        icp: setting.web_site_icp,
        companyName: setting.web_copyright,
        date: '',
      },
    });
  } catch {}
});
</script>

<template>
  <ConfigProvider :locale="antdLocale" :theme="tokenTheme">
    <App>
      <RouterView />
    </App>
  </ConfigProvider>
</template>
