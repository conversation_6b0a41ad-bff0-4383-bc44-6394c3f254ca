<?php
use yii\helpers\Html;
use yii\widgets\LinkPager;
use yii\helpers\Url;

$this->title = '控制台管理-日志管理-管理员操作日志';
?>
<?php $this->beginBlock('content') ?>
<div class="row">	
	<div class="clearfix">
		<div class="pull-right tableTools-container"></div>
	</div>
</div>
<div class="row" style="margin-bottom: 10px;">
	<form action="">
	<input type="hidden" name="r" value="admin-log">
		<div class="col-xs-12">
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="form-control search-query" name="uname" value='<?php echo Html::encode(Yii::$app->controller->get('uname')) ?>'  placeholder="操作人">
			</div>
			<div style="float: left;margin-right: 6px;">
				<input type="text"  name="time" id="start_time" value='<?php echo Html::encode(Yii::$app->controller->get('time')) ?>'  placeholder="开始时间">
				至
			</div>							 
			<div style="float: left;margin-right: 6px;">
				 <input type="text" name="time_end" id="end_time" value='<?php echo Html::encode(Yii::$app->controller->get('time_end')) ?>'  placeholder="结束时间">
			</div>
			<div style="float: left;margin-right: 6px;">
				 <input type="text" class="form-control search-query" name="description" value='<?php echo Html::encode(Yii::$app->controller->get('description')) ?>'  placeholder="描述">
			</div>
		<div>
			<button type="submit" class="btn btn-white btn-primary btn-bold">
				<span class="ace-icon fa fa-search icon-on-right bigger-110"></span>
				搜索
			</button>
			<button type="button" class="btn btn-white btn-success btn-bold" style="margin-left:10px;" onclick="javascript:location.href='<?php echo Url::to(['admin-log/index']) ?>'">
				<span class="ace-icon fa  fa-refresh"></span>
				刷新
			</button>
		</div>
		</div>
	</form>
</div>
<form action="" id='checkbox'>
<table id="simple-table" width="100%" class="table table-striped table-bordered table-hover">
<thead>
	<tr>
		<!-- <th class="center">
			<label class="pos-rel">
				<input type="checkbox" class="ace" />
				<span class="lbl"></span>
			</label>
		</th> -->
		<th>日志编号</th>
		<th>操作人</th>
		<th>路由</th>
		<th class="hidden-480"><i class="ace-icon fa fa-clock-o bigger-110 hidden-480"></i>操作时间</th>							
		<th>描述</th>							
		<th >操作</th>
	</tr>
</thead>
<tbody>
	<?php foreach ($arrRes as $key => $value): ?>
	<tr>
		<td><?php echo $value['id']?></td>
		<td><?php echo $value['admin_name']?></td>
		<td><?php echo $value['route']?></td>
		<td width="10%"><?php echo date('Y-m-d H:i:s',$value['created_at'])?></td>			
		<td  style="word-break:break-all;"><?php echo $value['description']?></td>							
		<td></td>
	</tr>
	<?php endforeach ?>						
</tbody>
</table>
<div class="row" style="margin-top: 10px;">
	<div class="col-xs-6">
		<div class="dataTables_info" id="dynamic-table_info" role="status" aria-live="polite">每页<?php echo $pageSize?>条记录，共有<?php echo $iCount;?>条记录</div>
	</div>
	<div class="col-xs-6 pagination1" style="text-align: right;">
		<?php 
			echo LinkPager::widget([
				'pagination' => $page,
				'firstPageLabel'=>"首页",
				'prevPageLabel'=>'上一页',
				'nextPageLabel'=>'下一页',
				'lastPageLabel'=>'末页',
			]);
		?>
	</div>
</div>
</form>

<!-- page specific plugin scripts -->


<!-- inline scripts related to this page -->
<script type="text/javascript">
	var start = {
		elem: '#start_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	var end = {
		elem: '#end_time',
		format: 'YYYY-MM-DD hh:mm:ss',
		min: '1900-01-01 00:00:00', //设定最小日期为当前日期
		max: '2099-06-16 23:59:59', //最大日期
		istime: false,
		istoday: true,
		choose: function(datas){
			 end.min = datas; //开始日选好后，重置结束日的最小日期
			 end.start = datas //将结束日的初始值设定为开始日
		}
	};
	laydate(start);
	laydate(end);
	laydate.skin('molv');
</script>
<script type="text/javascript">
	jQuery(function($) {			
		//And for the first simple table, which doesn't have TableTools or dataTables
		//select/deselect all rows according to table header checkbox
		var active_class = 'active';
		$('#simple-table > thead > tr > th input[type=checkbox]').eq(0).on('click', function(){
			var th_checked = this.checked;//checkbox inside "TH" table header				
			$(this).closest('table').find('tbody > tr').each(function(){
				var row = this;
				if(th_checked) $(row).addClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', true);
				else $(row).removeClass(active_class).find('input[type=checkbox]').eq(0).prop('checked', false);
			});
		});				
		//select/deselect a row when the checkbox is checked/unchecked
		$('#simple-table').on('click', 'td input[type=checkbox]' , function(){
			var $row = $(this).closest('tr');
			if($row.is('.detail-row ')) return;
			if(this.checked) $row.addClass(active_class);
			else $row.removeClass(active_class);
		});			
		/***************/
		$('.show-details-btn').on('click', function(e) {
			e.preventDefault();
			$(this).closest('tr').next().toggleClass('open');
			$(this).find(ace.vars['.icon']).toggleClass('fa-angle-double-down').toggleClass('fa-angle-double-up');
		});
	});
</script>
<?php $this->endBlock(); ?>
