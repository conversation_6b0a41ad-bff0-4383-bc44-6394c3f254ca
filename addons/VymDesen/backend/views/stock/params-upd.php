<?php

use yii\helpers\Url;
use addons\VymDesen\common\components\DataHelper;
$this->title = '库存管理 - 新增参数';
?>
<?php $this->beginBlock('content') ?>
    <div class="clearfix">
    	<div class="pull-right tableTools-container"></div>
    </div>
    <form class="form-horizontal">
		<input type="hidden" name="sp_id" value="<?php echo $StockParam['sp_id']?>"/>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > <span style="color:red">*</span>选择类目：</label>        
        	<div class="col-sm-6">
        		<input type="text" name="stock_type" value="<?php echo $StockParam['stock_name']?>" class="col-xs-10 col-sm-5" disabled >
        	</div>
        </div>
		
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > <span style="color:red">*</span>参数名称：</label>        
        	<div class="col-sm-6">
        		<input type="text" name="stock_param_name" value="<?php echo $StockParam['stock_param_name']?>" class="col-xs-10 col-sm-5" disabled >
        	</div>
        </div>
		
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > <span style="color:red">*</span>参数类型：</label>        
        	<div class="col-sm-6">
        		<input type="text" name="stock_param_choose" value="<?php echo $StockParam['stock_param_choose']?>" class="col-xs-10 col-sm-5" disabled >
        	</div>
        </div>
		
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > <span style="color:red">*</span>参数唯一：</label>        
        	<div class="col-sm-6">
        		<select name="stock_params_primary" class="col-xs-10 col-sm-5">
					<option value="">选择参数是否唯一<?php echo $StockParam['stock_params_primary']?></option>
					<option value="Y" <?php if($StockParam['stock_params_primary'] == 'Y'){echo 'selected';}?> >是</option>
					<option value="N" <?php if($StockParam['stock_params_primary'] == 'N'){echo 'selected';}?> >否</option>
				</select>      		
        	</div>
        </div>
		
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > <span style="color:red">*</span>参数必填：</label>        
        	<div class="col-sm-6">
        		<select name="stock_params_must" class="col-xs-10 col-sm-5">
					<option value="">选择参数是否必填</option>
					<option value="Y" <?php if($StockParam['stock_params_must'] == 'Y'){echo 'selected';}?> >是</option>
					<option value="N" <?php if($StockParam['stock_params_must'] == 'N'){echo 'selected';}?> >否</option>
				</select>      		
        	</div>
        </div>
		
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" > <span style="color:red">*</span>参数搜索：</label>        
        	<div class="col-sm-6">
        		<select name="stock_is_search" class="col-xs-10 col-sm-5">
					<option value="">参数是否支持搜索</option>
					<option value="Y" <?php if($StockParam['stock_is_search'] == 'Y'){echo 'selected';}?> >是</option>
					<option value="N" <?php if($StockParam['stock_is_search'] == 'N'){echo 'selected';}?> >否</option>
				</select>      		
        	</div>
        </div>
		
		<div class="form-group">
			<label class="col-sm-3 control-label no-padding-right">取值范围：</label>
			<div class="col-sm-6">
				<input type="text" name="stock_params_canuse" id="canUse" class="col-xs-10 col-sm-5" data-role="tagsinput" value="" placeholder="请填写单选或多选的取值范围 ..." />
			</div>
		</div>
		<div class="form-group">
        	<label class="col-sm-3 control-label no-padding-right" >参数排序：</label>        
        	<div class="col-sm-6">
        		<input type="text" name="stock_sort" value="<?php echo $StockParam['stock_sort']?>" class="col-xs-10 col-sm-5">
        	</div>
        </div>
		
        <div class="clearfix form-actions">
            <div class="col-md-offset-3 col-md-9">               
            	<button id="submitParams" class="btn btn-info" type="button">
            		<i class="ace-icon fa fa-check bigger-110"></i>
            		提交申请
            	</button>
            	&nbsp; &nbsp; &nbsp;
            	<button class="btn btn-danger" type="button" style="width:120px" onclick="window.history.back()">
					<i class="ace-icon fa fa-reply icon-only"></i>
					返回上一页
            	</button> 
            </div>
        </div>
        <div class="hr hr-24"></div>
    </form>
<script src="/js/bootstrap-tag.js"></script>
<script>
$(function() {
	var tag_input = $('#canUse');
	try {
		tag_input.tag({
			placeholder:tag_input.attr('placeholder'),
			//source: ace.vars['US_STATES'],
			/**
			//or fetch data from database, fetch those that match "query"
			source: function(query, process) {
			  $.ajax({url: 'remote_source.php?q='+encodeURIComponent(query)})
			  .done(function(result_items){
				process(result_items);
			  });
			}
			*/
			
		});
		
		var $tag_obj = tag_input.data('tag');
		var dataFetch = "<?php echo $StockParam['stock_params_canuse']?>";
		if(dataFetch) {
			var dataFetchArr = dataFetch.split(',');
			for(var i in dataFetchArr) {
				$tag_obj.add(dataFetchArr[i]);
			}
		}
	}
	catch(e) {
		tag_input.after('<textarea id="'+tag_input.attr('id')+'" name="'+tag_input.attr('name')+'" rows="3">'+tag_input.val()+'</textarea>').remove();
	}
	
	$("#submitParams").click(function() {
		var sp_id = $('[name="sp_id"]').val();
		var stock_param_choose = $('[name="stock_param_choose"]').val();
		var stock_params_primary = $('[name="stock_params_primary"]').val();
		var stock_params_must = $('[name="stock_params_must"]').val();
		var stock_is_search = $('[name="stock_is_search"]').val();
		var stock_params_canuse = $('[name="stock_params_canuse"]').val();
		var stock_sort = $('[name="stock_sort"]').val();
		
		if(!sp_id || !stock_params_primary || !stock_params_must || !stock_is_search) {
			layer.alert("请填写所有参数选项", {icon:7});
			return false;
		}
		
		if(stock_param_choose == '单选' || stock_param_choose == '多选') {
			if(!stock_params_canuse) {
				layer.alert("多个选项请填写取值范围", {icon:7});
				return false;
			}
		}
		
		var url = "<?php echo Url::to(['stock/params-upd'])?>";
		
		var index = layer.load(1, {shade:['0.7', '#fff']});
		$.post(url, {"sp_id":sp_id, "stock_params_primary":stock_params_primary, "stock_params_must":stock_params_must, "stock_is_search":stock_is_search, "stock_params_canuse":stock_params_canuse, "stock_sort":stock_sort }, function(e) {
			layer.close(index);
			if(e.data.status == 1) {
				layer.alert(e.data.info, {icon:1}, function() {
					window.location.href="<?php echo Url::to(['stock/params-list'])?>";
				});
			} else {
				layer.alert(e.data.info, {icon:7});
			}
		}, "json");
	});
	
});    
</script>
<?php $this->endBlock(); ?>
