<?php

namespace addons\VymDesen\backend\controllers;

use addons\VymDesen\backend\models\IdlePdt;
use addons\VymDesen\backend\models\PaymentAccount;
use addons\VymDesen\backend\models\TestServer;
use addons\VymDesen\backend\models\UserAdmin\UserAdmin;
use addons\VymDesen\backend\models\WorkFlow\WorkFlow;
use addons\VymDesen\backend\models\WorkFlow\WorkFlowDetail;
use addons\VymDesen\backend\controllers\BaseController;
use addons\VymDesen\common\components\DataHelper;
use addons\VymDesen\common\components\pay\BillModel;
use addons\VymDesen\common\components\RevenueNotes;
use addons\VymDesen\common\models\AdminSystemConfig;
use addons\VymDesen\common\models\Afterorder\AfterorderList;
use addons\VymDesen\common\models\Member\MemberPdt;
use addons\VymDesen\common\models\NewTrade\TradeDetail;
use addons\VymDesen\common\models\NewTrade\TradeMain;
use addons\VymDesen\common\models\PayOrder\PayorderDetail;
use addons\VymDesen\common\models\PayOrder\PayorderGeneral;
use addons\VymDesen\common\models\PayOrder\PayorderOriginal;
use addons\VymDesen\common\models\Pdt\PdtIp;
use addons\VymDesen\common\models\UserMember\UserMember;
use Yii;
use yii\helpers\ArrayHelper;

class TradeManageController extends BaseController
{

    #订单列表
    public function actionTradeList()
    {

        $OriginalModel = new PayorderOriginal();
        $GeneralModel  = new PayorderGeneral();
        $DetailModel   = new PayorderDetail();

        $UserAdminModel  = new UserAdmin();
        $UserMemberModel = new UserMember();

        $ConfigModel = new AdminSystemConfig();

        $get      = $this->get();
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        $roles    = explode(',', Yii::$app->session['admin']['roles']);

        $isSaler   = false;
        $isDisplay = true;
        #如果是销售的权限，那么有可选择只显示自己，默认也只显示自己，页面上会有显示全部订单的选项

        $configRes  = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $isSaler   = true;
            $isDisplay = false;
            #如果选择了显示所有订单则显示全部，默认只显示销售自己的
            $OriginalModelQuery = $OriginalModel->find()->where(['order_admin_id' => $admin_id]);
            /*if(!$get['isAll']) {
                $OriginalModelQuery = $OriginalModel->find()->where(['order_admin_id' => $admin_id]);
            } else {
                $OriginalModelQuery = $OriginalModel->find();
            }*/
        } else {
            $OriginalModelQuery = $OriginalModel->find();
        }


        #获取客服
        $AdminSystemConfigModel = new AdminSystemConfig();
        $salesConfig            = $AdminSystemConfigModel->find()->where(['config_name' => 'order_default_myself'])->asArray()->one();
        $adminlist              = $UserAdminModel->find()->where([
            'in',
            'admin_id',
            explode(',', $salesConfig['config_value']),
        ])->asArray()->all();

        $OriginalModel->createSearchWhere($OriginalModelQuery, $get);

        $iCount = $OriginalModelQuery->count();
        $oPage  = DataHelper::getPage($iCount);

        $arrTradeRes = $OriginalModelQuery->with("userMember")->offset($oPage->offset)->limit($oPage->limit)->orderBy('original_id desc')->asArray()->all();
        return $this->render('index', [
            'arrRes'       => $arrTradeRes,
            'iCount'       => $iCount,
            'page'         => $oPage,
            'is_sale'      => $isSaler,
            'UserAdminRes' => $adminlist,
            'is_display'   => $isDisplay,
            'admin_id'     => $admin_id,
        ]);
    }

    #订单列表快捷查看下单信息
    public function actionSelectOrderInfo()
    {
        Yii::$app->request->isAjax || die('error');
        $tradeOrder = $this->post('order_id');
        if (!$tradeOrder) {
            $arrReturn = [
                'status' => 0,
                'info'   => "缺少订单号",
            ];
            return $this->renderJSON($arrReturn);
        }

        $DetailModel    = new PayorderDetail();
        $OriginalModel  = new PayorderOriginal();
        $MemberPdtModel = new MemberPdt();

        $originalinfo = $OriginalModel->find()->where(['order_id' => $tradeOrder])->asArray()->one();
        $detailList   = $DetailModel->find()->where(['detail_original_order' => $tradeOrder])->asArray()->all();

        #只能销售看到的
        $isDisplay = true;
        $admin_id  = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalinfo['order_admin_id'] != $admin_id) {
                $isDisplay = false;
            }
        }

        foreach ($detailList as $key => $val) {
            $config = json_decode($val['detail_content'], true);

            if ($originalinfo['order_type'] == '新购') {
                $detailList[$key]['pdtname']      = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $config["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['servername']   = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $config["server_type_id"] . "'")->queryScalar();
                $config['config']['defense']      = $config["config"]["defense"] ? $config["config"]["defense"] : "N/A";
                $config['config']['operatsystem'] = $config["config"]["operatsystem"] ? $config["config"]["operatsystem"] : "N/A";
                $detailList[$key]['config']       = $config['config'];

                if (!$isDisplay) {
                    $detailList[$key]['detail_price'] = '****';
                }

            } else if ($originalinfo['order_type'] == '变更配置') {
                $Pdtinfo                        = $MemberPdtModel->find()->where(['unionid' => $config['unionid']])->asArray()->one();
                $iplist                         = json_decode($Pdtinfo['ip'], true);
                $detailList[$key]['mainip']     = $iplist[0];
                $detailList[$key]['pdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $Pdtinfo["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['servername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $Pdtinfo["server_type_id"] . "'")->queryScalar();

                $payment_cycle = '';

                if ($Pdtinfo['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($Pdtinfo['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($Pdtinfo['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($Pdtinfo['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $Pdtinfo['payment_cycle'] . '月付';
                }

                if (!$isDisplay) {
                    $detailList[$key]['payment_cycle']     = $payment_cycle;
                    $detailList[$key]['old_sell_price']    = '****';
                    $detailList[$key]['modify_sell_price'] = '****';
                } else {
                    $detailList[$key]['payment_cycle']     = $payment_cycle;
                    $detailList[$key]['old_sell_price']    = $config['old_sell_price'];
                    $detailList[$key]['modify_sell_price'] = $config['modify_sell_price'];
                }

                foreach ($config['modifyData'] as $k => $v) {
                    if ($v['modify_type'] == 'pdt_id' || $v['modify_type'] == 'configbandwidth') {
                        unset($config['modifyData'][$k]);
                        continue;
                    } else {
                        if ($v['modify_type'] == 'hdd') {
                            $config['modifyData'][$k]['modify_type'] = '硬盘';
                        } else if ($v['modify_type'] == 'ram') {
                            $config['modifyData'][$k]['modify_type'] = '内存';
                        } else if ($v['modify_type'] == 'requirement_bandwidth') {
                            $config['modifyData'][$k]['modify_type'] = '带宽';
                        } else if ($v['modify_type'] == 'ipnumber') {
                            $config['modifyData'][$k]['modify_type'] = '可用IP数';
                        } else if ($v['modify_type'] == 'defense') {
                            $config['modifyData'][$k]['modify_type'] = '防御流量';
                        }
                    }
                }

                $detailList[$key]['modify_data'] = $config['modifyData'];
            } else if ($originalinfo['order_type'] == '更换机器') {
                $Pdtinfo                           = $MemberPdtModel->find()->where(['unionid' => $config['unionid']])->asArray()->one();#ip,配置，续费金额，补款。
                $detailList[$key]['oldpdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $Pdtinfo["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['oldservername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $Pdtinfo["server_type_id"] . "'")->queryScalar();
                $detailList[$key]['newpdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $config["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['newservername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $config["server_type_id"] . "'")->queryScalar();

                $pdtconfig = json_decode($Pdtinfo['config'], true);

                $detailList[$key]['oldconfiginfo'] = $pdtconfig['cpu'] . ' / ' . $pdtconfig['ram'] . ' / ' . $pdtconfig['hdd'] . ' / ' . $pdtconfig['requirement_bandwidth'] . ' / ' . $pdtconfig['ipnumber'] . ' / ' . $pdtconfig['defense'] . ' / ' . $pdtconfig['operatsystem'];
                $detailList[$key]['newconfiginfo'] = $config['config']['cpu'] . ' / ' . $config['config']['ram'] . ' / ' . $config['config']['hdd'] . ' / ' . $config['config']['requirement_bandwidth'] . ' / ' . $config['config']['ipnumber'] . ' / ' . $config['config']['defense'] . ' / ' . $config['config']['operatsystem'];


                $payment_cycle = '';

                if ($Pdtinfo['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($Pdtinfo['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($Pdtinfo['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($Pdtinfo['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $Pdtinfo['payment_cycle'] . '月付';
                }

                if (!$isDisplay) {
                    $detailList[$key]['oldsellprice'] = '**** / ' . $payment_cycle;
                    $detailList[$key]['newsellprice'] = '**** / ' . $payment_cycle;
                    $detailList[$key]['paymoney']     = '****';
                } else {
                    $detailList[$key]['oldsellprice'] = $Pdtinfo['sell_price'] . ' / ' . $payment_cycle;
                    $detailList[$key]['newsellprice'] = sprintf("%.2f", $config['sell_price']) . ' / ' . $payment_cycle;
                    $detailList[$key]['paymoney']     = sprintf("%.2f", $config['difference_price']);
                }


                $detailList[$key]['oldiplist'] = implode("<br/>", json_decode($Pdtinfo['ip'], true));
                $detailList[$key]['newiplist'] = implode("<br/>", $config['ip']);

            } else if ($originalinfo['order_type'] == '续费') {
                $config['config']['defense']      = $config["config"]["defense"] ? $config["config"]["defense"] : "N/A";
                $config['config']['operatsystem'] = $config["config"]["operatsystem"] ? $config["config"]["operatsystem"] : "N/A";
                $detailList[$key]['config']       = $config['config'];
                $detailList[$key]['sell_price']   = $config['sell_price'];
                $detailList[$key]['renew_num']    = $config['renew_num'];
                $detailList[$key]['ip']           = $config['ip'][0];
                $detailList[$key]['end_time']     = date("Y-m-d", $config['end_time']);

                if ($config['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($config['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($config['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($config['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $config['payment_cycle'] . '月付';
                }
                $detailList[$key]['payment_cycle'] = $payment_cycle;

                if (!$isDisplay) {
                    $detailList[$key]['detail_price'] = '****';
                }
            }
        }

        $arrReturn = [
            'status'          => 1,
            'info'            => "查询完成",
            'data'            => $detailList,
            'data_trade_type' => $originalinfo['order_type'],
        ];
        return $this->renderJSON($arrReturn);
    }

    #订单列表快捷查看付款信息
    public function actionSelectPayInfo()
    {
        Yii::$app->request->isAjax || die('error');
        $tradeOrder = $this->post('order_id');
        if (!$tradeOrder) {
            $arrReturn = [
                'status' => 0,
                'info'   => "缺少订单号",
            ];
            return $this->renderJSON($arrReturn);
        }

        $GeneralModel = new PayorderGeneral();

        $payorderlist = $GeneralModel->find()->where(['general_original_order' => $tradeOrder])->asArray()->all();

        #只能销售看到的
        $isSaler  = false;
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $isSaler = true;
        }

        if ($isSaler) {
            foreach ($payorderlist as $key => $val) {
                if ($val['general_command_adminid_list'] != $admin_id) {
                    $payorderlist[$key]['general_pay_money'] == '****';
                }

            }
        }

        $arrReturn = [
            'status' => 1,
            'info'   => "查询完成",
            'data'   => $payorderlist,
        ];
        return $this->renderJSON($arrReturn);
    }

    #订单详情
    public function actionTradeDetail()
    {

        #判断参数
        $get = $this->get();
        if (!$get['order_id']) {
            return $this->redirect(["/trade-manage/trade-list"]);
        }

        $GeneralModel   = new PayorderGeneral();
        $OriginalModel  = new PayorderOriginal();
        $DetailModel    = new PayorderDetail();
        $MemberPdtModel = new MemberPdt();

        #$OriginalInfo = $OriginalModel->find()->where(['order_id' => $get['order_id']])->asArray()->one();
        $detailList  = $DetailModel->find()->where(['detail_original_order' => $get['order_id']])->asArray()->all();
        $generalList = $GeneralModel->find()->where(['general_original_order' => $get['order_id']])->asArray()->all();

        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');
        $isSaler  = false;
        #如果是销售的权限，那么只能进入自己订单的详情
        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            $OriginalInfo = $OriginalModel->find()->where([
                'order_id'       => $get['order_id'],
                'order_admin_id' => $admin_id,
            ])->asArray()->one();
        } else {
            $OriginalInfo = $OriginalModel->find()->where(['order_id' => $get['order_id']])->asArray()->one();
        }

        #订单异常
        if (!$OriginalInfo || !$detailList || !$generalList) {
            return $this->redirect(["/trade-manage/trade-list"]);
        }
        if ($OriginalInfo['order_status'] == '待审核') {
            return $this->redirect(["/trade-manage/trade-list"]);
        }

        foreach ($detailList as $key => $val) {
            $config = json_decode($val['detail_content'], true);

            if ($OriginalInfo['order_type'] == '新购') {
                $detailList[$key]['pdtname']      = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $config["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['servername']   = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $config["server_type_id"] . "'")->queryScalar();
                $config['config']['defense']      = $config["config"]["defense"] ? $config["config"]["defense"] : "N/A";
                $config['config']['operatsystem'] = $config["config"]["operatsystem"] ? $config["config"]["operatsystem"] : "N/A";

                $payment_cycle = '';

                if ($config['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($config['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($config['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($config['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $config['payment_cycle'] . '月付';
                }

                $detailList[$key]['payment_cycle'] = $payment_cycle;
                $detailList[$key]['config']        = $config['config'];
            } else if ($OriginalInfo['order_type'] == '变更配置') {
                $Pdtinfo                        = $MemberPdtModel->find()->where(['unionid' => $config['unionid']])->asArray()->one();
                $iplist                         = json_decode($Pdtinfo['ip'], true);
                $detailList[$key]['mainip']     = $iplist[0];
                $detailList[$key]['pdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $Pdtinfo["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['servername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $Pdtinfo["server_type_id"] . "'")->queryScalar();

                $payment_cycle = '';

                if ($Pdtinfo['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($Pdtinfo['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($Pdtinfo['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($Pdtinfo['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $Pdtinfo['payment_cycle'] . '月付';
                }

                $detailList[$key]['payment_cycle']     = $payment_cycle;
                $detailList[$key]['old_sell_price']    = $Pdtinfo['sell_price'];
                $detailList[$key]['modify_sell_price'] = $config['modify_sell_price'];

                foreach ($config['modifyData'] as $k => $v) {
                    if ($v['modify_type'] == 'pdt_id' || $v['modify_type'] == 'configbandwidth') {
                        unset($config['modifyData'][$k]);
                        continue;
                    } else {
                        if ($v['modify_type'] == 'hdd') {
                            $config['modifyData'][$k]['modify_type'] = '硬盘';
                        } else if ($v['modify_type'] == 'ram') {
                            $config['modifyData'][$k]['modify_type'] = '内存';
                        } else if ($v['modify_type'] == 'requirement_bandwidth') {
                            $config['modifyData'][$k]['modify_type'] = '带宽';
                        } else if ($v['modify_type'] == 'ipnumber') {
                            $config['modifyData'][$k]['modify_type'] = '可用IP数';
                        } else if ($v['modify_type'] == 'defense') {
                            $config['modifyData'][$k]['modify_type'] = '防御流量';
                        }
                    }
                }

                $detailList[$key]['modify_data'] = $config['modifyData'];
            } else if ($OriginalInfo['order_type'] == '更换机器') {
                $Pdtinfo                           = $MemberPdtModel->find()->where(['unionid' => $config['unionid']])->asArray()->one();#ip,配置，续费金额，补款。
                $detailList[$key]['oldpdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $Pdtinfo["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['oldservername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $Pdtinfo["server_type_id"] . "'")->queryScalar();
                $detailList[$key]['newpdtname']    = Yii::$app->db->createCommand("select name from pdt_manage where id = '" . $config["pdt_id"] . "'")->queryScalar();
                $detailList[$key]['newservername'] = Yii::$app->db->createCommand("select type_name from pdt_manage_type where type_id = '" . $config["server_type_id"] . "'")->queryScalar();

                $pdtconfig = json_decode($Pdtinfo['config'], true);

                $detailList[$key]['oldconfiginfo'] = $pdtconfig['cpu'] . ' / ' . $pdtconfig['ram'] . ' / ' . $pdtconfig['hdd'] . ' / ' . $pdtconfig['requirement_bandwidth'] . ' / ' . $pdtconfig['ipnumber'] . ' / ' . $pdtconfig['defense'] . ' / ' . $pdtconfig['operatsystem'];
                $detailList[$key]['newconfiginfo'] = $config['config']['cpu'] . ' / ' . $config['config']['ram'] . ' / ' . $config['config']['hdd'] . ' / ' . $config['config']['requirement_bandwidth'] . ' / ' . $config['config']['ipnumber'] . ' / ' . $config['config']['defense'] . ' / ' . $config['config']['operatsystem'];


                $payment_cycle = '';

                if ($Pdtinfo['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($Pdtinfo['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($Pdtinfo['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($Pdtinfo['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $Pdtinfo['payment_cycle'] . '月付';
                }

                $detailList[$key]['oldsellprice'] = $Pdtinfo['sell_price'] . ' / ' . $payment_cycle;
                $detailList[$key]['newsellprice'] = sprintf("%.2f", $config['sell_price']) . ' / ' . $payment_cycle;

                $detailList[$key]['oldiplist'] = implode("<br/>", json_decode($Pdtinfo['ip'], true));
                $detailList[$key]['newiplist'] = implode("<br/>", $config['ip']);

                $detailList[$key]['paymoney'] = sprintf("%.2f", $config['difference_price']);
            } else if ($OriginalInfo['order_type'] == '续费') {
                $config['config']['defense']      = $config["config"]["defense"] ? $config["config"]["defense"] : "N/A";
                $config['config']['operatsystem'] = $config["config"]["operatsystem"] ? $config["config"]["operatsystem"] : "N/A";
                $detailList[$key]['config']       = $config['config'];
                $detailList[$key]['sell_price']   = $config['sell_price'];
                $detailList[$key]['renew_num']    = $config['renew_num'];
                $detailList[$key]['ip']           = $config['ip'][0];
                $detailList[$key]['end_time']     = date("Y-m-d", $config['end_time']);

                if ($config['payment_cycle'] == 1) {
                    $payment_cycle = '月付';
                } else if ($config['payment_cycle'] == 3) {
                    $payment_cycle = '季付';
                } else if ($config['payment_cycle'] == 6) {
                    $payment_cycle = '半年付';
                } else if ($config['payment_cycle'] == 12) {
                    $payment_cycle = '年付';
                } else {
                    $payment_cycle = $config['payment_cycle'] . '月付';
                }
                $detailList[$key]['payment_cycle'] = $payment_cycle;
            }
        }

        #查询付款行
        $PaymentModel = new PaymentAccount();
        $PaymentRes   = $PaymentModel->find()->where(['account_status' => 'Y'])->asArray()->all();

        return $this->render('trade-detail', [
            'OriginalInfo' => $OriginalInfo,
            'DetailList'   => $detailList,
            'GeneralList'  => $generalList,
            'PaymentRes'   => $PaymentRes,
        ]);
    }

    #订单修改信息操作
    public function actionUpdateOrderDetail()
    {
        Yii::$app->request->isAjax || die('error');

        $post = $this->post();

        $order_type = $post['order_type'];
        $data       = $post['data'];
        $order_id   = $post['order_id'];

        if (!$order_id || !$order_type || !$data) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '订单提交信息缺失，请联系管理员',
            ]);
        }

        if (!in_array($order_type, ['新购', '续费'])) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '订单类型错误',
            ]);
        }

        $GeneralModel        = new PayorderGeneral();
        $OriginalModel       = new PayorderOriginal();
        $DetailModel         = new PayorderDetail();
        $AfterorderListModel = new AfterorderList();
        $MemberPdtModel      = new MemberPdt();

        $detailList = $DetailModel->find()->where(['detail_original_order' => $order_id])->all();

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $diffrentPrice = 0;

        foreach ($detailList as $key => $val) {

            $detailConfig = json_decode($val['detail_content'], true);

            foreach ($data as $k => $v) {

                if (!is_numeric($v[0]) || !is_numeric($v[1])) {

                    $transaction->rollBack();

                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '金额或者数量不是数字，请核对后重新提交',
                    ]);
                }

                if ($order_type == '新购') {
                    if ($val->detail_id == $v[2]) {

                        $hasUpdatePrice = false;

                        $detailInfo = $DetailModel->find()->where(['detail_id' => $val->detail_id])->one();

                        if (isset($detailConfig['renew_price']) && $detailConfig['renew_price'] == $v[0]) {
                            $hasUpdatePrice = false;
                        } else {
                            $detailConfig['renew_price'] = sprintf("%.2f", $v[0]);
                            $detailInfo->detail_content  = json_encode($detailConfig, JSON_UNESCAPED_UNICODE);

                            #判断是否有工单，更新工单，更新已生成业务
                            $afterorderInfo = $AfterorderListModel->find()->where([
                                "order_id"  => $order_id,
                                "detail_id" => $detailInfo->detail_id,
                            ])->one();

                            if ($afterorderInfo) {
                                $after_config = json_decode($afterorderInfo->ao_request_content, true);

                                if (isset($after_config['preset']['sell_price'])) {
                                    $after_config["preset"]["sell_price"] = sprintf("%.2f", $v[0]);
                                }

                                if (isset($after_config['sell_price'])) {
                                    $after_config["sell_price"] = sprintf("%.2f", $v[0]);
                                }

                                if (isset($after_config['renew_price'])) {
                                    $after_config["renew_price"] = sprintf("%.2f", $v[0]);
                                }

                                $afterorderInfo->ao_request_content = json_encode($after_config, JSON_UNESCAPED_UNICODE);

                                if (!$afterorderInfo->save()) {
                                    $transaction->rollBack();

                                    return $this->renderJSON([
                                        'status' => 0,
                                        'info'   => '更新售后工单新购续费金额出现异常',
                                    ]);
                                }
                            }

                            $pdtinfo = $MemberPdtModel->find()->where(["unionid" => $detailConfig['unionid']])->one();

                            if ($pdtinfo) {

                                $pdtinfo->sell_price = $v[0];

                                if (!$pdtinfo->save()) {
                                    $transaction->rollBack();

                                    return $this->renderJSON([
                                        'status' => 0,
                                        'info'   => '更新业务新购续费金额出现异常',
                                    ]);
                                }
                            }

                            $hasUpdatePrice = true;
                        }

                        if ($val->detail_price != $v[1]) {
                            $diffrentPrice            += $v[1] - $val->detail_price;
                            $detailInfo->detail_price = $v[1];
                            $hasUpdatePrice           = true;
                        }

                        if ($hasUpdatePrice) {
                            if (!$detailInfo->save()) {
                                $transaction->rollBack();
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => '更新订单详情信息出现异常',
                                ]);
                            }
                        }

                        #修改账单金额

                        $newConfig = json_decode($detailInfo->detail_content, true);

                        $billUpdateData = [
                            "type"          => "新购",
                            "unionid"       => $newConfig['unionid'],
                            "order_id"      => $order_id,
                            "renew_price"   => isset($newConfig['renew_price']) ? $newConfig['renew_price'] : $detailInfo->detail_price,
                            "payment_cycle" => $newConfig['payment_cycle'],
                            "renew_num"     => 1,#新购默认1
                            "pay_money"     => $detailInfo->detail_price,
                        ];

                        $updateBill = BillModel::EditBillMoney($billUpdateData);

                        if (!$updateBill["status"]) {
                            $transaction->rollBack();
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => $updateBill["info"],
                            ]);
                        }

                    }
                } else {
                    if ($val->detail_id == $v[2]) {

                        $detailInfo = $DetailModel->find()->where(['detail_id' => $val->detail_id])->one();

                        if ($detailConfig['renew_num'] != $v[1]) {
                            $diffrentPrice              += ($v[1] * $detailConfig['sell_price']) - $detailInfo->detail_price;
                            $detailConfig['renew_num']  = $v[1];
                            $detailInfo->detail_content = json_encode($detailConfig, JSON_UNESCAPED_UNICODE);
                            $detailInfo->detail_price   = $v[1] * $detailConfig['sell_price'];
                            if (!$detailInfo->save()) {
                                $transaction->rollBack();
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => '更新订单详情信息出现异常',
                                ]);
                            }
                        } else {
                            if ($detailInfo->detail_price != $v[0]) {
                                $diffrentPrice              += $v[0] - $detailInfo->detail_price;
                                $detailConfig['renew_num']  = $v[1];
                                $detailInfo->detail_content = json_encode($detailConfig, JSON_UNESCAPED_UNICODE);
                                $detailInfo->detail_price   = $v[0];
                                if (!$detailInfo->save()) {
                                    $transaction->rollBack();
                                    return $this->renderJSON([
                                        'status' => 0,
                                        'info'   => '更新订单详情信息出现异常',
                                    ]);
                                }
                            }
                        }

                        #修改账单金额

                        $newConfig = json_decode($detailInfo->detail_content, true);

                        $billUpdateData = [
                            "type"          => "续费",
                            "unionid"       => $newConfig['unionid'],
                            "order_id"      => $order_id,
                            "renew_price"   => $newConfig['sell_price'],
                            "payment_cycle" => $newConfig['payment_cycle'],
                            "renew_num"     => $newConfig['renew_num'],#新购默认1
                            "pay_money"     => $detailInfo->detail_price,
                        ];

                        $updateBill = BillModel::EditBillMoney($billUpdateData);

                        if (!$updateBill["status"]) {
                            $transaction->rollBack();
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => $updateBill["info"],
                            ]);
                        }
                    }
                }
            }
        }

        #更新订单主表

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                $transaction->rollBack();
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        $originalInfo->order_original_price += $diffrentPrice;
        $originalInfo->order_update_price   += $diffrentPrice;
        $originalInfo->order_update_remark  = '';

        if (!$originalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '更新订单主表信息出现异常',
            ]);
        }

        #更新订单付款单
        $generalInfo                    = $GeneralModel->find()->where(['general_original_order' => $order_id])->one();
        $generalInfo->general_pay_money += $diffrentPrice;

        if (!$generalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '更新订单付款单信息出现异常',
            ]);
        }

        $transaction->commit();

        return $this->renderJSON([
            'status' => 1,
            'info'   => '更新订单信息完成',
        ]);
    }

    #修改订单价格
    public function actionUpdateOrderPrice()
    {
        Yii::$app->request->isAjax || die('error');

        $order_update_price  = $this->post('order_update_price');
        $order_amount_money  = $this->post('order_amount_money');
        $order_update_remark = $this->post('order_update_remark');
        $order_id            = $this->post('order_id');

        if (!$order_id || !$order_update_remark || !$order_update_price) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '修改订单金额参数异常',
            ]);
        }

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();
        $DetailModel   = new PayorderDetail();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();
        $generalInfo  = $GeneralModel->find()->where(['general_original_order' => $order_id])->one();
        $DetailList   = $DetailModel->find()->where(['detail_original_order' => $order_id])->all();

        if (!$originalInfo || !$generalInfo) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '无效的订单',
            ]);
        }

        if ($originalInfo->order_update_status == '已锁定') {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '当前订单总金额已被锁定',
            ]);
        }

        #如果订单金额没有被锁定，那么一定还没有进行付款操作，那么付款单肯定只有一个，同时也没有拆分付款单，但还是要判断一下付款单状态，有可能因为网络延迟的原因导致的
        if ($generalInfo->general_pay_lock != '等待支付') {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '付款单状态异常，可能用户已付款',
            ]);
        }

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        $totalMoney = 0;

        foreach ($DetailList as $key => $val) {
            $totalMoney += $val->detail_price;
        }

        if ($order_update_price + $order_amount_money != $totalMoney) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '修改后价格或抹零金额不正确，和订单总价不相等，不能修改金额<br/>订单付款价 + 抹零价 = 订单明细总价',
            ]);
        }


        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $originalInfo->order_update_price  = $order_update_price;
        $originalInfo->order_amount_money  = $order_amount_money;
        $originalInfo->order_update_remark = $order_update_remark;

        $generalInfo->general_pay_money = $order_update_price;

        if (!$originalInfo->save() || !$generalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '修改订单金额出现异常',
            ]);
        }

        $transaction->commit();

        return $this->renderJSON([
            'status' => 1,
            'info'   => '修改订单金额完成',
        ]);

    }

    #锁定 / 解锁当前订单总价
    public function actionLockAndUnlockPrice()
    {
        Yii::$app->request->isAjax || die('error');

        $order_id = $this->post('order_id');
        $type     = $this->post('type');

        if (!$order_id || !$type || !in_array($type, ['lock', 'unlock'])) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '缺少锁定订单必要参数',
            ]);
        }

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        if (!$originalInfo) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '无效的订单',
            ]);
        }

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                $transaction->rollBack();
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        if ($type == 'lock') {
            #锁定订单
            $originalInfo->order_update_status = '已锁定';
            if (!$originalInfo->save()) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '锁定订单总价出现异常',
                ]);
            }
        } else {
            #解锁订单

            $generalList = $GeneralModel->find()->where(['general_original_order' => $order_id])->all();

            $moneyTotal = 0;//解锁就要回归一笔，去掉自定义

            foreach ($generalList as $key => $val) {
                if ($val->general_pay_lock != '等待支付') {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '当前已经至少有一笔付款单完成了支付，订单价不能解锁',
                    ]);
                }

                if ($val->general_locked_state == '已锁定') {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '当前已经至少有一笔付款单已被锁定，订单价不能解锁',
                    ]);
                }

                if ($val->general_payorder_type == 'original') {
                    $moneyTotal += $val->general_pay_money;
                }
            }

            if (count($generalList) > 1) {
                $deleteRes = Yii::$app->db->createCommand()->delete(PayorderGeneral::tableName(), 'general_original_order = ' . $order_id)->execute();
                if (!$deleteRes) {
                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '移除历史付款单出现异常',
                    ]);
                }

                $generalOrder                                  = DataHelper::createPayorderGeneralID();
                $GeneralModelNew                               = new PayorderGeneral();
                $GeneralModelNew->general_payorder_title       = $originalInfo->order_type;
                $GeneralModelNew->general_payorder_number      = null;
                $GeneralModelNew->general_payorder             = $generalOrder;
                $GeneralModelNew->general_original_order       = $order_id;
                $GeneralModelNew->general_pay_money            = $moneyTotal;
                $GeneralModelNew->general_pay_platform         = null;
                $GeneralModelNew->general_pay_channel          = null;
                $GeneralModelNew->general_pay_time             = null;
                $GeneralModelNew->general_pay_lock             = '等待支付';
                $GeneralModelNew->general_payinfo_review       = '未审核';
                $GeneralModelNew->general_payorder_type        = 'original';
                $GeneralModelNew->general_callback_stream      = null;
                $GeneralModelNew->general_command_adminid_list = $originalInfo->order_admin_id;
                $GeneralModelNew->general_payment_userid       = $originalInfo->order_user_id;
                $GeneralModelNew->general_create_time          = $originalInfo->order_time_create;

                if (!$GeneralModelNew->insert()) {

                    $transaction->rollBack();
                    return $this->renderJSON([
                        'status' => 0,
                        'info'   => '初始化付款单出现异常',
                    ]);
                }
            }

            $originalInfo->order_update_status = '未锁定';
            $originalInfo->order_update_price  = $moneyTotal;
            if (!$originalInfo->save()) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '锁定订单总价出现异常',
                ]);
            }
        }

        $transaction->commit();

        if ($type == 'lock') {
            return $this->renderJSON([
                'status' => 1,
                'info'   => '锁定订单完成',
            ]);
        } else {
            return $this->renderJSON([
                'status' => 1,
                'info'   => '解锁订单完成',
            ]);
        }
    }

    #初始化付款单
    public function actionInitPayorder()
    {
        Yii::$app->request->isAjax || die('error');

        $order_id = $this->post('order_id');

        if (!$order_id) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '订单号异常',
            ]);
        }

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();
        $generalList  = $GeneralModel->find()->where(['general_original_order' => $order_id])->all();

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }
        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $removeMoney = 0;

        foreach ($generalList as $key => $val) {
            if ($val->general_pay_lock != '等待支付') {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '当前已经至少有一笔付款单完成了支付，不能初始化',
                ]);
            }

            if ($val->general_locked_state == '已锁定') {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '当前已经至少有一笔付款单已被锁定，不能初始化',
                ]);
            }

            if ($val->general_payorder_type == 'custom') {
                $removeMoney += $val->general_pay_money;
            }
        }

        $order_amount = $originalInfo->order_update_price - $removeMoney;

        if (count($generalList) > 1) {
            $deleteRes = Yii::$app->db->createCommand()->delete(PayorderGeneral::tableName(), 'general_original_order = ' . $order_id)->execute();
            if (!$deleteRes) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '移除历史付款单出现异常',
                ]);
            }


            $generalOrder                                  = DataHelper::createPayorderGeneralID();
            $GeneralModelNew                               = new PayorderGeneral();
            $GeneralModelNew->general_payorder_title       = $originalInfo->order_type;
            $GeneralModelNew->general_payorder_number      = null;
            $GeneralModelNew->general_payorder             = $generalOrder;
            $GeneralModelNew->general_original_order       = $order_id;
            $GeneralModelNew->general_pay_money            = $order_amount;
            $GeneralModelNew->general_pay_platform         = null;
            $GeneralModelNew->general_pay_channel          = null;
            $GeneralModelNew->general_pay_time             = null;
            $GeneralModelNew->general_pay_lock             = '等待支付';
            $GeneralModelNew->general_payinfo_review       = '未审核';
            $GeneralModelNew->general_payorder_type        = 'original';
            $GeneralModelNew->general_callback_stream      = null;
            $GeneralModelNew->general_command_adminid_list = $originalInfo->order_admin_id;
            $GeneralModelNew->general_payment_userid       = $originalInfo->order_user_id;
            $GeneralModelNew->general_create_time          = $originalInfo->order_time_create;

            if (!$GeneralModelNew->insert()) {

                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '初始化付款单出现异常',
                ]);
            }
        }

        #修改订单总价
        $originalInfo->order_update_price = $order_amount;

        if (!$originalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '初始化付款单出现异常',
            ]);
        }

        $transaction->commit();

        return $this->renderJSON([
            'status' => 1,
            'info'   => '初始化付款单完成',
        ]);

    }

    #添加自定义付款单
    public function actionAddPayorder()
    {
        Yii::$app->request->isAjax || die('error');

        $order_id               = $this->post('order_id');
        $general_payorder_title = $this->post('general_payorder_title');
        $general_pay_money      = $this->post('general_pay_money');

        if (!$order_id) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '订单参数异常',
            ]);
        }
        if (!$general_payorder_title) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '付款名称不能为空',
            ]);
        }
        if ($general_pay_money <= 0) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '付款金额必须大于0',
            ]);
        }

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();
        $DetailModel   = new PayorderDetail();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();

        if (in_array($originalInfo->order_status, ['待审核', '已完成', '已取消'])) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '当前订单状态不允许再添加新的付款项',
            ]);
        }

        if (in_array($originalInfo->order_pay_status, ['已支付', '已退款'])) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '当前订单支付状态不允许再添加新的付款项',
            ]);
        }

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $generalOrder                                  = DataHelper::createPayorderGeneralID();
        $GeneralModelNew                               = new PayorderGeneral();
        $GeneralModelNew->general_payorder_title       = $general_payorder_title;
        $GeneralModelNew->general_payorder_number      = null;
        $GeneralModelNew->general_payorder             = $generalOrder;
        $GeneralModelNew->general_original_order       = $order_id;
        $GeneralModelNew->general_pay_money            = $general_pay_money;
        $GeneralModelNew->general_pay_platform         = null;
        $GeneralModelNew->general_pay_channel          = null;
        $GeneralModelNew->general_pay_time             = null;
        $GeneralModelNew->general_pay_lock             = '等待支付';
        $GeneralModelNew->general_payinfo_review       = '未审核';
        $GeneralModelNew->general_payorder_type        = 'custom';
        $GeneralModelNew->general_callback_stream      = null;
        $GeneralModelNew->general_command_adminid_list = $originalInfo->order_admin_id;
        $GeneralModelNew->general_payment_userid       = $originalInfo->order_user_id;
        $GeneralModelNew->general_create_time          = $originalInfo->order_time_create;

        if (!$GeneralModelNew->insert()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '添加自定义付款单出现异常',
            ]);
        }

        #修改订单总价
        $originalInfo->order_update_price  += $general_pay_money;
        $originalInfo->order_update_status = '已锁定';//对付款单操作有变化的都要锁定

        if (!$originalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '添加自定义付款单出现异常',
            ]);
        }

        $transaction->commit();
        return $this->renderJSON([
            'status' => 1,
            'info'   => '添加自定义付款单完成',
        ]);
    }

    #编辑自定义付款单
    public function actionEditPayorder()
    {
        Yii::$app->request->isAjax || die('error');

        $general_id             = $this->post('general_id');
        $order_id               = $this->post('order_id');
        $general_payorder_title = $this->post('general_payorder_title');
        $general_pay_money      = $this->post('general_pay_money');

        if (!$general_id || !$order_id || !$general_payorder_title || !$general_pay_money) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '付款单参数异常',
            ]);
        }

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();
        $detailInfo   = $GeneralModel->find()->where(['general_id' => $general_id])->one();

        if (in_array($detailInfo->general_pay_lock, ['支付完成', '已取消', '已退款', '后付款'])) {
            return $this->renderJSON(['status' => 0, 'info' => '当前付款单不允许更改']);
        }

        if ($detailInfo->general_locked_state == '已锁定' || $detailInfo->general_payorder_number != '') {
            return $this->renderJSON(['status' => 0, 'info' => '当前付款单已被加入支付流水，需要更新请解除流水']);
        }

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        $diffrentMoney = $general_pay_money - $detailInfo->general_pay_money;

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $detailInfo->general_payorder_title = $general_payorder_title;
        $detailInfo->general_pay_money      = $general_pay_money;

        if (!$detailInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '编辑自定义付款单出现异常',
            ]);
        }

        #修改订单总价
        $originalInfo->order_update_price  = $originalInfo->order_update_price + $diffrentMoney;
        $originalInfo->order_update_status = '已锁定';

        if (!$originalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '编辑自定义付款单出现异常',
            ]);
        }

        $transaction->commit();
        return $this->renderJSON([
            'status' => 1,
            'info'   => '编辑自定义付款单完成',
        ]);
    }

    #删除自定义付款单
    public function actionDelPayorder()
    {
        Yii::$app->request->isAjax || die('error');

        $general_id = $this->post('general_id');
        $order_id   = $this->post('order_id');

        if (!$general_id || !$order_id) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '付款单参数异常',
            ]);
        }

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $order_id])->one();
        $detailInfo   = $GeneralModel->find()->where(['general_id' => $general_id])->one();

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        if (in_array($detailInfo->general_pay_lock, ['支付完成', '已取消', '已退款', '后付款'])) {
            return $this->renderJSON(['status' => 0, 'info' => '当前付款单不允许更改']);
        }

        if ($detailInfo->general_locked_state == '已锁定' || $detailInfo->general_payorder_number != '') {
            return $this->renderJSON(['status' => 0, 'info' => '当前付款单已被加入支付流水，需要更新请解除流水']);
        }

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $deleteRes = Yii::$app->db->createCommand()->delete(PayorderGeneral::tableName(), 'general_id = ' . $general_id)->execute();
        if (!$deleteRes) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '删除自定义付款单出现异常',
            ]);
        }

        #修改订单总价
        $originalInfo->order_update_price = $originalInfo->order_update_price - $detailInfo->general_pay_money;

        if (!$originalInfo->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '删除自定义付款单出现异常',
            ]);
        }

        $transaction->commit();
        return $this->renderJSON([
            'status' => 1,
            'info'   => '删除自定义付款单完成',
        ]);
    }

    #拆分付款单
    public function actionSplitGeneral()
    {
        Yii::$app->request->isAjax || die('error');

        $general_id  = $this->post('general_id');
        $split_money = $this->post('split_money');

        $GeneralModel  = new PayorderGeneral();
        $OriginalModel = new PayorderOriginal();

        $oldRecord = $GeneralModel->find()->where(['general_id' => $general_id])->one();

        if ($split_money <= 0) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '拆分出来的金额不能小于等于0',
            ]);
        }
        if (!$oldRecord) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '原付款单不存在',
            ]);
        }

        if (in_array($oldRecord->general_pay_lock, ['支付完成', '已取消', '已退款', '后付款'])) {
            return $this->renderJSON(['status' => 0, 'info' => '当前付款单不允许拆分']);
        }

        if ($oldRecord->general_locked_state == '已锁定' || $oldRecord->general_payorder_number != '') {
            return $this->renderJSON(['status' => 0, 'info' => '当前付款单已被加入支付流水，不允许拆分']);
        }

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        $originalInfo = $OriginalModel->find()->where(['order_id' => $oldRecord->general_original_order])->one();

        #如果是销售的权限，那么只能销售自己修改
        $admin_id = \yii\helpers\ArrayHelper::getValue(\Yii::$app->user->identity,'id');

        $ConfigModel = new AdminSystemConfig();
        $configRes   = $ConfigModel->find()->where(['config_name' => 'order_default_myself'])->one();
        $configUser  = explode(',', $configRes['config_value']);

        if (in_array($admin_id, $configUser)) {
            if ($originalInfo->order_admin_id != $admin_id) {
                $transaction->rollBack();
                return $this->renderJSON(['status' => 0, 'info' => '您不属于当前订单的销售，无法修改']);
            }
        }

        if ($oldRecord->general_payment_userid != $originalInfo->order_user_id) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '已指定用户付款的单不能拆分',
            ]);
        }

        if ($originalInfo->order_update_status == '未锁定') {

            $originalInfo->order_update_status = '已锁定';

            if (!$originalInfo->save()) {
                $transaction->rollBack();
                return $this->renderJSON([
                    'status' => 0,
                    'info'   => '锁定主订单出现异常',
                ]);
            }
        }

        if ($oldRecord->general_pay_lock != '等待支付') {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '订单已支付或已取消，不能进行拆分',
            ]);
        }

        $oldRecord->general_pay_money = $oldRecord->general_pay_money - $split_money;

        if (!$oldRecord->save()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '更新原付款单信息出现异常',
            ]);
        }

        $GeneralModelNew = new PayorderGeneral();

        $generalOrder                                  = DataHelper::createPayorderGeneralID();
        $GeneralModelNew->general_payorder_title       = $oldRecord->general_payorder_title;
        $GeneralModelNew->general_payorder_number      = null;
        $GeneralModelNew->general_payorder             = $generalOrder;
        $GeneralModelNew->general_original_order       = $oldRecord->general_original_order;
        $GeneralModelNew->general_pay_money            = $split_money;
        $GeneralModelNew->general_pay_platform         = null;
        $GeneralModelNew->general_pay_channel          = null;
        $GeneralModelNew->general_pay_time             = null;
        $GeneralModelNew->general_pay_lock             = '等待支付';
        $GeneralModelNew->general_payinfo_review       = '未审核';
        $GeneralModelNew->general_payorder_type        = 'original';
        $GeneralModelNew->general_callback_stream      = null;
        $GeneralModelNew->general_command_adminid_list = $oldRecord->general_command_adminid_list;
        $GeneralModelNew->general_payment_userid       = $originalInfo->order_user_id;
        $GeneralModelNew->general_create_time          = time();

        if (!$GeneralModelNew->insert()) {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '新增拆分后付款单出现异常',
            ]);
        }

        $transaction->commit();

        return $this->renderJSON([
            'status' => 1,
            'info'   => '拆分付款单完成',
        ]);

    }

    #取消未支付订单
    public function actionCancelPayorder()
    {
        $orderid = $this->post('order_id');
        if (!$orderid)
            return $this->renderJSON(['status' => 0, 'info' => '参数异常',]);

        $result = PayorderOriginal::cancelOrderByOrderId($orderid);
        if (ArrayHelper::getValue($result, 'code')) {
            return $this->renderJSON(['status' => 1, 'info' => '操作成功', 're' => $result]);
        } else {
            return $this->renderJSON(['status' => 0, 'info' => '更新订单异常',]);
        }
    }

    #####################################
    #取消未支付订单
    public function actionCancelOrder()
    {
        Yii::$app->request->isAjax || die('error');

        $post = $this->post();
        if (!$post['orderid']) {
            return $this->renderJSON([
                'status' => 0,
                'info'   => '参数异常',
            ]);
        }

        $TradeMainModel   = new TradeMain();
        $TradeDetailModel = new TradeDetail();

        $TradeMainRes = $TradeMainModel->find()->where(['trade_orderid' => $post['orderid']])->one();

        #开启事务
        $transaction = Yii::$app->db->beginTransaction();

        if ($TradeMainRes->trade_status == '后付款') {
            #后付款订单查询流程状态
            $WorkFlowModel       = new WorkFlow();
            $WorkFlowDetailModel = new WorkFlowDetail();
            $MemberPdtModel      = new MemberPdt();
            $IdlePdtModel        = new IdlePdt();
            $TestServerModel     = new TestServer();

            $WorkFlowRes       = $WorkFlowModel->find()->where(['flow_orderid' => $TradeMainRes->trade_orderid])->one();
            $WorkFlowDetailRes = $WorkFlowDetailModel->find()->where(['flow_id' => $WorkFlowRes->flow_id])->all();

            #更新主流程
            $WorkFlowRes->flow_operate_audit_status = '无需审核';
            $WorkFlowRes->flow_operate_isfinish     = '取消完成';
            $WorkFlowRes->flow_money_isfinish       = '无需审核成本';
            $WorkFlowRes->flow_status               = '中止事务完成';
            $WorkFlowRes->flow_money_audit_status   = '无需审核';
            $WorkFlowRes->flow_end_time             = time();
            $updateFlow                             = $WorkFlowRes->save();

            #更新子流程，还原机器状态，还原测试机，释放IP
            if ($TradeMainRes->trade_type == 'machine_renewal') {
                #续费
                foreach ($WorkFlowDetailRes as $key => $val) {
                    $afterConfig  = json_decode($val['flow_after_config'], true);
                    $MemberPdtRes = $MemberPdtModel->find()->where(['unionid' => $val['flow_unionid']])->one();
                    #$MemberPdtRes->end_time = $afterConfig['new_end_time'];
                    #获取续费的总月数（续费周期乘以续费数量）
                    $month_num              = $afterConfig['payment_cycle'] * $afterConfig['renew_num'];
                    $MemberPdtRes->end_time = DataHelper::getRecoveryEndtime($MemberPdtRes->end_time, $month_num);
                    if (!$MemberPdtRes->save()) {
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '还原续费时间出现异常',
                        ]);
                    }
                }

            } else if ($TradeMainRes->trade_type == 'machine_purchase') {
                #新购
                foreach ($WorkFlowDetailRes as $key => $val) {
                    $afterConfig = json_decode($val['flow_after_config'], true);
                    if ($afterConfig) {
                        #只要存在，则代表新购已配置了机器，只能更新，并不能清空，则需要回档配置
                        #判断是不是测试机
                        if (isset($afterConfig['test_id']) && $afterConfig['test_id'] != '') {
                            #测试机，直接改变测试机状态
                            $TestServerQuery         = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->one();
                            $TestServerQuery->status = 0;
                            if (!$TestServerQuery->update(false)) {
                                $transaction->rollBack();
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => '更新测试机出现异常',
                                ]);
                            }
                        } else {
                            #不是测试机，判断自有 还是供应商
                            if (isset($afterConfig['idle_id']) && $afterConfig['idle_id'] != '') {
                                #自有服务器
                                $IdleRes = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();
                                #修改idle_pdt表
                                $IdleRes->attribute_id = 1;
                                $IdleRes->status       = 0;
                                if (!$IdleRes->save()) {
                                    $transaction->rollBack();
                                    return $this->renderJSON([
                                        'status' => 0,
                                        'info'   => '更新自有库数据异常',
                                    ]);
                                }
                            }
                        }
                    }
                }

            } else if ($TradeMainRes->trade_type == 'replace_machine_rubsidy') {
                #更换机器
                foreach ($WorkFlowDetailRes as $key => $val) {

                    $afterConfig = json_decode($val['flow_after_config'], true);
                    $frontConfig = json_decode($val['flow_front_config'], true);
                    #获取用户产品
                    $MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $val['flow_unionid']])->one();
                    #如果选择的是测试机，
                    if (isset($afterConfig['test_id']) && $afterConfig['test_id']) {
                        $TestServerQuery = $TestServerModel->find()->where(['id' => $afterConfig['test_id']])->one();
                        #将测试机器的状态改为测试服务器
                        $TestServerQuery->status = 0;
                        #更新
                        if (!$TestServerQuery->update(false)) {
                            $transaction->rollBack();
                            return $this->renderJSON([
                                'status' => 0,
                                'info'   => '更新测试机出现异常',
                            ]);
                        }
                    } else {
                        #如果不是选择的测试机。那么选择不是自有机器就是供应商机器。如果选择的自有机器，将自有机器改为闲置状态
                        if ($afterConfig['provider'] == 0) {
                            #将更换的自有机器改为闲置状态
                            $IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();

                            $IdlePdtQuery->status = 0;
                            #更新
                            if (!$IdlePdtQuery->update(false)) {
                                $transaction->rollBack();
                                return $this->renderJSON([
                                    'status' => 0,
                                    'info'   => '自有机器状态更新失败',
                                ]);
                            }
                        } else {
                            #如果选择的供应商机器
                        }
                    }

                    #如果选择使用原IP，并且已经对换过IP（自有才修改）
                    if ($afterConfig['use_original_ip'] == 1) {
                        if ($afterConfig['is_exchangedip'] == 1 && isset($afterConfig['is_exchangedip'])) {
                            if ($MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id != '') {
                                $after_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $afterConfig['idle_id']])->one();#更换后的自有机器信息对象
                                $front_IdlePdtQuery = $IdlePdtModel->find()->where(['id' => $frontConfig['idle_id']])->one();#更换前的自有机器信息对象

                                $after_IdlePdtQuery->ip  = $front_IdlePdtQuery->ip;
                                $after_IdlePdtQuery->ip2 = $front_IdlePdtQuery->ip2;

                                $front_IdlePdtQuery->ip  = $MemberPdtQuery->ip;
                                $front_IdlePdtQuery->ip2 = $MemberPdtQuery->ip2;
                                if (!$front_IdlePdtQuery->update() || !$after_IdlePdtQuery->update()) {
                                    $transaction->rollBack();
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => '恢复对换IP失败',
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }
                            }
                        }
                    }

                    $MemberPdtQuery->status = 1;
                    if (!$MemberPdtQuery->update(false)) {
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '更新业务信息出现异常',
                        ]);
                    }

                }
            } else if ($TradeMainRes->trade_type == 'change_config_rubsidy') {
                #变更配置
                foreach ($WorkFlowDetailRes as $key => $val) {
                    $afterConfig = json_decode($val['flow_after_config'], true);
                    $frontConfig = json_decode($val['flow_after_config'], true);
                    #获取用户产品
                    $MemberPdtQuery = $MemberPdtModel->find()->where(['unionid' => $val['flow_unionid']])->one();

                    if (isset($afterConfig['isneed_replaceip']) && $afterConfig['isneed_replaceip'] == 1) {

                        #为空代表还未确定IP
                        if (!empty($afterConfig['ip'])) {
                            if ($MemberPdtQuery->servicerprovider == 0 && $MemberPdtQuery->idle_id != '') {
                                #获取用户产品所属的自有机器库信息,并将之前临时更改的IP修改回去
                                $IdlePdtRes              = $IdlePdtModel->findOne($MemberPdtQuery->idle_id);
                                $IdlePdtRes->ip          = json_encode($frontConfig['ip'], JSON_UNESCAPED_UNICODE);
                                $IdlePdtRes->ip2         = json_encode($frontConfig['ip2'], JSON_UNESCAPED_UNICODE);
                                $IdlePdtRes->update_time = time();
                                if (!$IdlePdtRes->update()) {
                                    $transaction->rollBack();
                                    $arrReturn = [
                                        'status' => 0,
                                        'info'   => '恢复自有机器配置IP失败',
                                    ];
                                    return $this->renderJSON($arrReturn);
                                }

                                #恢复IP状态
                                if (isset($afterConfig['isneed_replaceip']) && $afterConfig['isneed_replaceip'] == 1) {
                                    $bgIPArray       = array_unique(array_merge($afterConfig['ip2'], $frontConfig['ip2']));
                                    $result_bangding = array_diff($bgIPArray, $frontConfig['ip2']);    #需要改为使用中的
                                    $result_jiechu   = array_diff($bgIPArray, $afterConfig['ip2']);    #需要改为闲置的

                                    $PdtIpModel = new PdtIp();

                                    if (!empty($result_bangding)) {
                                        $res1 = $PdtIpModel->updateAll(['status' => 0], ['ip' => $result_bangding]);
                                        if ($res1 != count($result_bangding)) {
                                            $transaction->rollBack();
                                            $arrReturn = [
                                                'status' => 0,
                                                'info'   => 'IP状态恢复失败',
                                            ];
                                            return $this->renderJSON($arrReturn);
                                        }
                                    }
                                    if (!empty($result_jiechu)) {
                                        $res2 = $PdtIpModel->updateAll(['status' => 1], ['ip' => $result_jiechu]);
                                        if ($res2 != count($result_jiechu)) {
                                            $transaction->rollBack();
                                            $arrReturn = [
                                                'status' => 0,
                                                'info'   => 'IP状态恢复失败',
                                            ];
                                            return $this->renderJSON($arrReturn);
                                        }
                                    }
                                }
                                #End
                            }
                        }
                    }

                    $MemberPdtQuery->status = 1;
                    if (!$MemberPdtQuery->update(false)) {
                        $transaction->rollBack();
                        return $this->renderJSON([
                            'status' => 0,
                            'info'   => '更新业务信息出现异常',
                        ]);
                    }

                }
            }

        } else {

            $updateFlow = true;
        }

        $TradeMainRes->trade_status = '已取消';
        $result                     = $TradeMainRes->save();

        $UpdateRevenue = RevenueNotes::OrderCancel($post['orderid']);
        if ($result && $updateFlow && $UpdateRevenue['status']) {
            $transaction->commit();
            return $this->renderJSON([
                'status' => 1,
                'info'   => '操作成功',
            ]);
        } else {
            $transaction->rollBack();
            return $this->renderJSON([
                'status' => 0,
                'info'   => '更新订单异常',
            ]);
        }

    }

    #ajax查询订单信息
    public function actionAjaxGetTrade()
    {
        $orderid = $this->post('orderid');

        $TradeMainModel = new TradeMain();
        $TradeMainRes   = $TradeMainModel->find()->where(['trade_orderid' => $orderid])->asArray()->one();

        return $this->renderJSON([
            'status' => 1,
            'info'   => '查询完成',
            'data'   => $TradeMainRes,
        ]);
    }

}