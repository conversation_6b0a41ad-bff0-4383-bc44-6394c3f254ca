<?php

namespace addons\VymDesen\common\models\Notice;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "banner".
 *
 * @property integer $id
 * @property string  $title
 * @property string  $image_url
 * @property integer $class_id
 * @property integer $orders
 * @property integer $create_time
 * @property string  $link
 */
class NoticeType extends \yii\db\ActiveRecord
{
    public $fields = [];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'notice_type';
    }

    public static function tableNamealias()
    {
        return 'notice_type表';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['tid', 'status', 'create_time', 'update_time'], 'integer'],
            [['type_name', 'type_en', 'mobile_template', 'email_template', 'classification'], 'string'],
            ['type_name', 'required', 'message' => "分类名不能为空"],
            ['mobile_template', 'required', 'message' => "手机通知模板不能为空"],
            ['email_template', 'required', 'message' => "邮箱通知模板不能为空"],
        ];
    }

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            'addtype'    => ['type_name', 'mobile_template', 'email_template', 'status'],
            'updatetype' => ['type_name', 'mobile_template', 'email_template', 'status'],
        ]);
    }

    public static function TypeEnums($key = null)
    {
        $enums = [
            'system'  => '系统消息',
            'product' => '产品消息',
            'user'    => '用户消息',
            'money'   => '资金消息',
            'other'   => '其它消息',
        ];
        if ($key) {
            if (ArrayHelper::getValue($enums, $key)) {
                return ArrayHelper::getValue($enums, $key);
            } else {
                return ArrayHelper::getValue($enums, 'other');
            }
        } else {
            return $enums;
        }
    }

    /**
     * 获取列表数据（关联查询）
     */
    public function getListAll(array $condition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = ['*',];
        //$model = $this->find()->select($this->fields)->where($condition)->orderBy('create_time desc, id desc');

        $model = $this->find()->select($this->fields)->where($condition)->orderBy('create_time desc, tid desc');
        if ($isObj)
            return $model;
        return $model->asArray()->all();
    }

    /**
     * 通过id 获取一条指定数据
     */
    public function getRowById($id, $condition = [], $isObj = false)
    {
        if ($isObj)
            return $this->findOne($id);

        empty($this->fields) && $this->fields = ['*',];

        return $this->find()->select($this->fields)->where('tid=:id', [':id' => $id])->andWhere($condition)->limit(1)->asArray()->one();
    }


    /**
     * 删除
     *
     * @param unknown $id
     *
     * @return boolean
     */
    public function del($id)
    {
        //多选删除
        if (is_array($id))
            return $this->deleteAll(['tid' => $id]);

        $query = $this->find()->where('tid=:id', [':id' => $id])->limit(1)->one();

        if (empty($query))
            return false;

        return $query->delete();

    }
}
