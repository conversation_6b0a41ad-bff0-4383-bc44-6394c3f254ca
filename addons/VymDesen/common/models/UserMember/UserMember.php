<?php

namespace addons\VymDesen\common\models\UserMember;

use Yii;
use yii\helpers\ArrayHelper;
use addons\VymDesen\common\models\PayOrder\PayorderOriginal;
use common\models\member\Member;

/**
 * This is the model class for table "user_member".
 * @property int    $u_id
 * @property int    $pid
 * @property string $uname
 * @property string $username
 * @property string $pwd
 * @property string $salt
 * @property string $mobile
 * @property int    $mobile_area
 * @property string $telephone
 * @property string $qq
 * @property string $email
 * @property string $truename
 * @property string $company
 * @property string sex
 * @property string birthday
 * @property string sign
 * @property string $province
 * @property string $city
 * @property string $prefecture
 * @property string $address
 * @property string $idcard
 * @property string $bankcard
 * @property int    $reg_time
 * @property int    $last_time
 * @property string $last_ip
 * @property string $balance
 * @property string $price_total
 * @property string $remarks
 * @property int    $status
 * @property int    $admin_id
 * @property string $admin_name
 * @property string $idverifyinfo
 * @property string $bankverifyinfo
 * @property string $user_rating
 * @property int    $invoice_status
 * @property string $invoice
 * @property string $secret [varchar(255)]  Google密钥
 */
class UserMember extends \yii\db\ActiveRecord
{
    public $fields;

    public $verifyCode;//验证码这个变量是必须建的，因为要储存验证码的值

    /**
     * @inheritdoc
     */
    public static function tableName(): string
    {
        return 'user_member';
    }

    public static function tableNamealias(): string
    {
        return '用户信息表';
    }

    /**
     * 状态：启用
     */
    const STATUS_ENABLE = 1;

    /**
     * 状态：禁用
     */
    const STATUS_DISABLE = -1;

    /**
     * @inheritdoc
     */
    public function rules(): array
    {
        return [
            [['last_time', 'reg_time'], 'integer'],
            [['address', 'pwd'], 'string'],
            [['uname', 'email'], 'string', 'max' => 50],
            [['qq'], 'string', 'max' => 255],
            [['last_ip'], 'string', 'max' => 50],
            [['mobile', 'salt', 'truename'], 'string', 'max' => 50],
            [['price_total'], 'number', 'message' => '消费总额必须是数字'],
            ['price_total', 'default', 'value' => 0.00],
            [['balance'], 'number', 'message' => '账户余额必须是数字'],
            ['balance', 'default', 'value' => 0.00],

            [['uname', 'pwd', 'email'], 'trim'], ##['uname', 'unique', 'message'=>'用户昵称已存在'],
            ##['uname', 'required','on'=>['addUserMember'],  'message'=>'用户名不能为空'],
            ##['uname', 'unique','on'=>['addUserMember'],  'message'=>'用户名已经存在'],

            [['mobile'], 'unique', 'message' => '该手机号已存在'],

            [['telephone'], 'string', 'max' => 50],
            [['company', 'remarks', 'user_rating'], 'string', 'max' => 255],
            [['province', 'city', 'prefecture', 'idcard'], 'string', 'max' => 20],
            [['bankcard'], 'string', 'max' => 30],

            // ['mobile', 'required', 'message' => '手机不能为空'],
            ['email', 'unique', 'message' => '该邮箱已存在'],
            ['status', 'default', 'value' => 1],

            ['pwd', 'required', 'on' => ['dorepwd', 'addUserMember'], 'message' => '密码不能为空'],
            ['pwd', 'string', 'min' => 6, 'on' => ['dorepwd', 'addUserMember'], 'message' => '密码至少六位'],

            ##[['qq'], 'required', 'message'=>'联系QQ不能为空'],
            ##[['qq'], 'number', 'message'=>'联系QQ填写错误'],

            [['email'], 'unique', 'message' => '电子邮箱已被注册'],
            ['email', 'email', 'message' => '邮箱格式不正确'],

            ##['truename', 'required', 'message'=>'联系人不能为空'],
            ##['mobile', 'required', 'on'=>['membermodify'],'message'=>'联系电话不能为空'],

            ['mobile', 'number', 'on' => ['addUserMember', 'membermodify'], 'message' => '联系电话填写错误'],
            [
                'mobile',
                'match',
                'pattern' => '/^\d{11}$/',
                'on'      => ['membermodify', 'addUserMember'],
                'message' => '联系电话填写错误',
            ], ##['address', 'required', 'message'=>'通讯地址不能为空'],

            ['admin_id', 'integer'],
            ['admin_name', 'string', 'max' => 50],
            [['pid', 'reg_time', 'last_time'], 'integer'],

            [['idverifyinfo', 'bankverifyinfo'], 'string'],

            //['verifyCode', 'captcha']
            //['verifyCode', 'captcha', 'allowEmpty'=>!CCaptcha::checkRequirements()]
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels(): array
    {
        return [
            'u_id'           => '用户ID',
            'pid'            => '父ID',
            'uname'          => '用户昵称',
            'pwd'            => '密码',
            'salt'           => '盐值',
            'mobile'         => '手机',
            'telephone'      => '电话',
            'idcard'         => '身份证号码',
            'email'          => '电子邮箱',
            'qq'             => 'QQ',
            'truename'       => '用户真实名字',
            'company'        => '公司名',
            'province'       => '省份',
            'city'           => '城市',
            'prefecture'     => '县/区',
            'address'        => '详细地址',
            'reg_time'       => '注册时间',
            'last_time'      => '上次登录时间',
            'last_ip'        => '上次登录IP',
            'balance'        => '余额',
            'price_total'    => '消费总额',
            'remarks'        => '备注',
            'status'         => '状态',
            'admin_id'       => '客服ID',
            'admin_name'     => '客服名称',
            'bankcard'       => '银行卡号码',
            'idverifyinfo'   => '手机实名认证json备份',
            'bankverifyinfo' => '身份证实名认证json备份',
            'user_rating'    => '用户评级分类',
            'verifyCode'     => '图形验证码'
        ];
    }

    public function scenarios(): array
    {
        return array_merge(parent::scenarios(), [
            'membermodify'          => [
                'province',
                'city',
                'prefecture',
                'address',
                'telephone',
                'qq',
                'uname',
                'remarks',
            ],
            'addUserMember'         => [
                'pwd',
                'truename',
                'idcard',
                'company',
                'province',
                'city',
                'prefecture',
                'qq',
                'address',
                'email',
                'mobile',
                'telephone',
                'admin_id',
                'admin_name',
                'verifyCode'
            ],
            'dorepwd'               => ['pwd'],
            'assigncustomerservice' => ['admin_id', 'admin_name'],
        ]);
    }

    /**
     * 生成密码盐值
     * @return string
     * @throws \yii\base\Exception
     */
    public function grenrateSalt(): string
    {
        #return uniqid();
        return Yii::$app->security->generateRandomString();
    }

    /**
     * 获取密码加密值（原来的）
     * @param string $pwd
     * @param string $salt
     * @return string
     * @throws \yii\base\Exception
     */
    public function getPwd($pwd, $salt = ''): string
    {
        empty($salt) && $salt = $this->grenrateSalt();

        return md5(sprintf('%s_%s_%s', $salt, $pwd, $salt));
    }

    /*
        新版设置密码
    */
    public function setPassword($password): string
    {
        return Yii::$app->security->generatePasswordHash($password);
    }

    /*
     新版验证
    */
    public function validatePassword($password, $password_hash): bool
    {
        return Yii::$app->security->validatePassword($password, $password_hash);
    }

    //随机生成密码码
    public function createCode(): string
    {
        $pwd     = "";
        $charset = 'abcdefghkmnprstuvwxyzABCDEFGHKMNPRSTUVWXYZ0123456789'; //随机因子
        $codelen = 9;                                                      //密码长度
        $_len    = strlen($charset) - 1;
        for ($i = 0; $i < $codelen; $i++) {
            $pwd .= $charset[mt_rand(0, $_len)];
        }
        return $pwd;
    }

    /**
     * 密码修改处理方法（原来的）
     * @param $model
     * @param $pwd
     * @return boolean
     * @throws \yii\base\Exception
     */
    public function doPwd($model, $pwd): bool
    {
        #生成盐值
        $model->salt = $this->grenrateSalt();
        #生成密码
        $model->pwd = $this->getPwd($pwd, $model->salt);

        return $model->update($runValidation = false);
    }

    /**
     * 获取会员信息
     * @param integer $id
     * @param array   $field
     * @return array|UserMember|\yii\db\ActiveRecord|null
     */
    public static function getUserInfoById($id, array $field = ['*'])
    {
        return self::find()->select($field)->where('u_id=:id', [':id' => $id])->limit(1)->asArray()->one();
    }

    /**
     * 获取列表数据
     * @param array $condition
     * @param bool  $isObj
     * @return array|UserMember[]|\yii\db\ActiveQuery|\yii\db\ActiveRecord[]
     */
    public function getListAll(array $condition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = ['*'];

        $model = $this->find()->select($this->fields)->where($condition)->orderBy('u_id desc, reg_time desc');

        if ($isObj) return $model;

        return $model->asArray()->all();
    }

    /**
     * 通过主键获取一条数据
     * @param       $id
     * @param array $arrCondition
     * @param false $isObj
     * @return array|UserMember|\yii\db\ActiveQuery|\yii\db\ActiveRecord|null
     */
    public function getRowById($id, array $arrCondition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = ['*'];

        $model = $this->find()
                      ->select($this->fields)
                      ->where('u_id=:id', [':id' => $id])
                      ->andWhere($arrCondition)
                      ->limit(1);

        if ($isObj) return $model;

        return $model->asArray()->one();
    }

    /**
     * 根据uname 来获取用户信息
     * @param       $uname
     * @param array $arrCondition
     * @param false $isObj
     * @return array|UserMember|\yii\db\ActiveQuery|\yii\db\ActiveRecord|null
     */
    public function getRowByuname($uname, array $arrCondition = [], $isObj = false)
    {
        empty($this->fields) && $this->fields = ['*'];

        $model = $this->find()
                      ->select($this->fields)
                      ->where('uname=:uname', [':uname' => $uname])
                      ->andWhere($arrCondition)
                      ->limit(1);

        if ($isObj) return $model;

        return $model->asArray()->one();
    }

    /**
     * 创建搜索条件
     * @param $query
     * @param $params
     */
    public function createSearchWhere(&$query, $params)
    {
        $account_type = ArrayHelper::getValue($params, 'account_type', 0);
        if ($account_type !== '') {
            if ($account_type == 0) {
                $query->andWhere(['=', 'pid', 0]);
            } else {
                $query->andWhere(['<>', 'pid', 0]);
            }
        }

        $query->andWhere(['=', 'status', intval(ArrayHelper::getValue($params, 'status', 1))]);

        $search_key     = ArrayHelper::getValue($params, 'search_key');
        $search_content = ArrayHelper::getValue($params, 'search_content');
        if ($search_key && $search_content) {
            $query->andHaving("locate(:search_key, {$search_key})", [':search_key' => trim($search_content)]);
        }
        if (isset($params['email']) && !empty($params['email'])) {
            $query->andHaving('locate(:email, email)', [':email' => trim($params['email'])]);
        }
        if (isset($params['mobile']) && !empty($params['mobile'])) {
            $query->andHaving('locate(:mobile, mobile)', [':mobile' => trim($params['mobile'])]);
        }
        if (isset($params['rename']) && !empty($params['rename'])) {
            $query->andHaving('locate(:rename, truename)', [':rename' => trim($params['rename'])]);
        }
        if (isset($params['uname']) && !empty($params['uname'])) {
            $query->andHaving('locate(:uname, uname)', [':uname' => trim($params['uname'])]);
        }
        if (isset($params['admin_name']) && !empty($params['admin_name'])) {
            $query->andwhere(':admin_name = admin_name', [':admin_name' => trim($params['admin_name'])]);
        }

        if (isset($params['qq']) && !empty($params['qq'])) {
            $query->andHaving('locate(:qq, qq)', [':qq' => trim($params['qq'])]);
        }
        if (isset($params['company']) && !empty($params['company'])) {
            $query->andWhere('locate(:company, company)', [':company' => trim($params['company'])]);
        }
        if (ArrayHelper::getValue($params, 'reg_time.start')) {
            $query->andWhere([
                '>=',
                'reg_time',
                strtotime(ArrayHelper::getValue($params, 'reg_time.start')),
            ]);
        }
        if (ArrayHelper::getValue($params, 'reg_time.end')) {
            $query->andwhere([
                '<=',
                'reg_time',
                strtotime(ArrayHelper::getValue($params, 'reg_time.end')),
            ]);
        }
        if (isset($params['certification']) && !empty($params['certification'])) {
            if ($params['certification'] == 'Y') {
                #$query->andWhere(['!=', 'mobile', '']);
                $query->andWhere(['or', ['!=', 'idcard', ''], ['!=', 'bankcard', ''], ['!=', 'company', '']]);
            } else {
                //$query->andWhere(['=', 'mobile', ''])->orWhere(['=', 'mobile', "null"]);
                $query->andWhere(['or', ['=', 'idcard', ''], ['is', 'idcard', null]]);
                $query->andWhere(['or', ['=', 'bankcard', ''], ['is', 'bankcard', null]]);
                $query->andWhere(['or', ['=', 'company', ''], ['is', 'company', null]]);
            }
        }
        if (isset($params['user_rating']) && !empty($params['user_rating'])) {
            if ($params['user_rating'] != 'no_rating') {
                $query->andwhere(':user_rating = user_rating', [':user_rating' => trim($params['user_rating'])]);
            } else {
                $query->andwhere(['is', 'user_rating', new \yii\db\Expression('NULL')]);
            }

        }

        return;
    }

    /**
     * 原来的
     * @return bool
     * @throws \Throwable
     * @throws \yii\base\Exception
     */
    public function DoaddUsermember(): bool
    {
        #注册时间
        $this->reg_time = time();

        #生成盐值
        $this->salt = $this->grenrateSalt();
        #生成密码
        $this->pwd = $this->getPwd($this->pwd, $this->salt);
        #状态默认为1
        $this->status = 1;
        return $this->insert($runValidation = false);
    }

    /**
     * @param integer $u_id  会员id
     * @param number  $price 更变金额 如果为增加金额则为整数 减小金额则为负数
     * @param null    $model
     * @return false|int
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function changeBalance($u_id, $price, &$model = null)
    {

        if (empty($model)) {
            $model = $this->getRowById($u_id, [], true)->one();
        }
        if (empty($model)) {
            $model->addError('u_id', '未找到指定账户');
            return false;
        }

        $model->balance += $price;

        if (!is_numeric($price)) {
            $model->addError('balance', '输入金额必须是数字');
            return false;
        }

        if ($model->balance < 0) {
            $model->addError('balance', '账户余额不足');
            return false;
        }

        return $model->update(false);
    }

    /**
     * @param integer $u_id 会员id
     * @param         $price
     * @param null    $model
     * @return false|int
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function changetotal($u_id, $price, &$model = null)
    {

        if (empty($model)) {
            $model = $this->getRowById($u_id, [], true)->one();
        }
        if (empty($model)) {
            $model->addError('u_id', '未找到指定账户');
            return false;
        }

        $model->price_total += $price;

        if (!is_numeric($price)) {
            $model->addError('balance', '输入金额必须是数字');
            return false;
        }

        return $model->update(false);
    }

    /**
     * 修改信息
     * @param $id
     * @param $status
     * @return false|int
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function change($id, $status)
    {
        $query = $this->findOne($id);

        $query->status = $status;

        return $query->update();
    }

    public function getOriginal()
    {
        return $this->hasMany(PayorderOriginal::className(), ['order_user_id' => 'u_id']);
    }

    public function getMember(){
        return $this->hasOne(Member::className(), ['u_id' => 'us_uid']);
    }
}
