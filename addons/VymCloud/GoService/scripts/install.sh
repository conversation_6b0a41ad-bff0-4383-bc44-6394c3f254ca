#!/bin/bash

# VymCloud Configuration Service 简单安装脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要 root 权限运行"
        exit 1
    fi
}

# 创建目录
create_directories() {
    log_info "创建目录..."
    mkdir -p /etc/vymcloud
    mkdir -p /var/lib/vz/snippets
    mkdir -p /var/lib/vz/snippets/backup
}

# 安装二进制文件
install_binary() {
    local binary_path="$1"

    if [[ ! -f "$binary_path" ]]; then
        log_error "二进制文件不存在: $binary_path"
        exit 1
    fi

    log_info "安装程序..."
    cp "$binary_path" /usr/local/bin/vymcloud-config-service
    chmod +x /usr/local/bin/vymcloud-config-service
}

# 安装配置文件
install_config() {
    local config_path="$1"

    if [[ -f "$config_path" ]]; then
        cp "$config_path" /etc/vymcloud/config.yaml
    else
        # 创建简单的默认配置
        cat > /etc/vymcloud/config.yaml << 'EOF'
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s

storage:
  snippets_dir: "/var/lib/vz/snippets"
  backup_dir: "/var/lib/vz/snippets/backup"

auth:
  enabled: true
  api_key: "vymcloud-config-service-2024"

logging:
  level: "info"
  file: "/var/log/vymcloud-config.log"
  max_size: 100
  max_backups: 5
  max_age: 30
EOF
    fi

    chmod 644 /etc/vymcloud/config.yaml
}

# 安装服务
install_service() {
    local service_path="$1"

    if [[ -f "$service_path" ]]; then
        cp "$service_path" /etc/systemd/system/vymcloud-config.service
    else
        # 创建简单的服务文件
        cat > /etc/systemd/system/vymcloud-config.service << 'EOF'
[Unit]
Description=VymCloud Configuration Service
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/vymcloud-config-service --config=/etc/vymcloud/config.yaml
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
    fi

    systemctl daemon-reload
}

# 启动服务
start_service() {
    log_info "启动服务..."
    systemctl enable vymcloud-config
    systemctl start vymcloud-config

    if systemctl is-active --quiet vymcloud-config; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 主函数
main() {
    local binary_path="${1:-./vymcloud-config-service}"
    local config_path="${2:-./configs/config.yaml}"
    local service_path="${3:-./scripts/systemd/vymcloud-config.service}"

    log_info "安装 VymCloud Configuration Service..."

    check_root
    create_directories
    install_binary "$binary_path"
    install_config "$config_path"
    install_service "$service_path"
    start_service

    echo
    echo "安装完成！"
    echo "API 地址: http://localhost:8080"
    echo "测试命令: curl http://localhost:8080/health"
    echo "查看状态: systemctl status vymcloud-config"
}

main "$@"
