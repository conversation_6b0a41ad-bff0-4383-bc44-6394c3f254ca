<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/** @var yii\web\View $this */
/** @var addons\VymCloud\common\models\CloudServer $model */
/** @var yii\widgets\ActiveForm $form */
?>

<div class="cloud-server-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'server_name')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'uid')->textInput() ?>

    <?= $form->field($model, 'cluster_id')->textInput() ?>

    <?= $form->field($model, 'order_id')->textInput() ?>

    <?= $form->field($model, 'order_no')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'order_amount')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'upgrade_amount')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'renew_amount')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'renew_mod')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'renew_auto')->textInput() ?>

    <?= $form->field($model, 'node')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'vmid')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'tpl_vmid')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'ip')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'mac')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'server_detail')->textInput() ?>

    <?= $form->field($model, 'network_detail')->textInput() ?>

    <?= $form->field($model, 'extend_detail')->textInput() ?>

    <?= $form->field($model, 'tasks_detail')->textInput() ?>

    <?= $form->field($model, 'status_server')->textInput() ?>

    <?= $form->field($model, 'status_power')->textInput() ?>

    <?= $form->field($model, 'status_net_off')->textInput() ?>

    <?= $form->field($model, 'server_remark')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'tips_time')->textInput() ?>

    <?= $form->field($model, 'reinstall_count')->textInput() ?>

    <?= $form->field($model, 'reinstall_time')->textInput() ?>

    <?= $form->field($model, 'begin_at')->textInput() ?>

    <?= $form->field($model, 'end_at')->textInput() ?>

    <?= $form->field($model, 'created_at')->textInput() ?>

    <?= $form->field($model, 'updated_at')->textInput() ?>

    <?= $form->field($model, 'deleted_at')->textInput() ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
