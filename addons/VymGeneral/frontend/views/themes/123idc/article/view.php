<?php

use common\helpers\StringHelper;
use yii\widgets\LinkPager; ?>

<?= $this->render('banner', [
    "cates" => $cates
]) ?>


<section class="container">
    <div class="page-breadcrumb">
        <nav class="d-flex mt-5 mb-4" aria-label="breadcrumb">
            <span class="breadcrumb-desc">您当前位置:</span>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">首页</a></li>
                <li class="breadcrumb-item"><a href="/news">新闻资讯</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?= $model["cate"]["title"] ?></li>
            </ol>
        </nav>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- 新闻详情 -->
            <div class="news-detail">
                <div class="news-detail-header">
                    <h1 class="news-detail-title"><?= $model["title"] ?></h1>
                    <div class="news-detail-meta">
                        <span class="news-meta-item">發布時間：<?= Yii::$app->formatter->asDate($model['created_at'], 'php:Y-m-d h:i:s'); ?></span>
                        <span class="news-meta-divider"></span>
                        <span class="news-meta-item">分類：<?= $model["cate"]["title"] ?></span>
                        <span class="news-meta-divider"></span>
                        <span class="news-meta-item">來源：123IDC</span>
                    </div>
                </div>
                <div class="news-detail-content">
                    <?= $model["content"] ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 相关资讯列表 -->
            <div class="news-related">
                <div class="news-related-card">
                    <h3 class="news-related-title">相关资讯</h3>
                    <hr class="news-related-divider">
                    <?php if (!empty($recommend)): ?>
                        <ul class="news-related-list">
                            <?php foreach ($recommend as $new) { ?>
                                <li class="news-related-item"><a href="/news/detail/<?= $new['id'] ?>"><?=  strlen($new['title'])>15?StringHelper::truncate($new['title'],15)."...":$new['title'];  ?></a></li>
                            <?php } ?>
                        </ul>
                    <?php else: ?>
                        <span>暂无相关资讯</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

</section>